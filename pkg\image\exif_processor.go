package image

import (
	"fmt"
	"image"
	"image/gif"
	"image/jpeg"
	"image/png"
	"io"
	"os"
	"strings"
	"time"

	"github.com/rwcarlsen/goexif/exif"
	"github.com/rwcarlsen/goexif/tiff"
)

// EXIFData EXIF数据结构
type EXIFData struct {
	// 基本信息
	Make        string    `json:"make"`        // 制造商
	Model       string    `json:"model"`       // 型号
	DateTime    time.Time `json:"date_time"`   // 拍摄时间
	Orientation int       `json:"orientation"` // 方向
	Software    string    `json:"software"`    // 软件

	// 相机设置
	FNumber      float64 `json:"f_number"`      // 光圈值
	ExposureTime string  `json:"exposure_time"` // 曝光时间
	ISO          int     `json:"iso"`           // ISO感光度
	FocalLength  float64 `json:"focal_length"`  // 焦距
	Flash        string  `json:"flash"`         // 闪光灯

	// 图片信息
	Width       int     `json:"width"`        // 图片宽度
	Height      int     `json:"height"`       // 图片高度
	XResolution float64 `json:"x_resolution"` // X分辨率
	YResolution float64 `json:"y_resolution"` // Y分辨率
	ColorSpace  string  `json:"color_space"`  // 色彩空间

	// GPS信息
	GPSLatitude  float64 `json:"gps_latitude"`  // GPS纬度
	GPSLongitude float64 `json:"gps_longitude"` // GPS经度
	GPSAltitude  float64 `json:"gps_altitude"`  // GPS海拔
	GPSTimestamp string  `json:"gps_timestamp"` // GPS时间戳

	// 其他信息
	Copyright   string            `json:"copyright"`   // 版权信息
	Artist      string            `json:"artist"`      // 艺术家
	Description string            `json:"description"` // 描述
	Keywords    []string          `json:"keywords"`    // 关键词
	RawData     map[string]string `json:"raw_data"`    // 原始EXIF数据
}

// EXIFProcessor EXIF处理器
type EXIFProcessor struct{}

// NewEXIFProcessor 创建EXIF处理器
func NewEXIFProcessor() *EXIFProcessor {
	return &EXIFProcessor{}
}

// ExtractEXIF 提取EXIF数据
func (ep *EXIFProcessor) ExtractEXIF(imagePath string) (*EXIFData, error) {
	file, err := os.Open(imagePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open image: %v", err)
	}
	defer file.Close()

	// 解析EXIF数据
	x, err := exif.Decode(file)
	if err != nil {
		// 如果没有EXIF数据，返回空结构
		return &EXIFData{
			RawData: make(map[string]string),
		}, nil
	}

	exifData := &EXIFData{
		RawData: make(map[string]string),
	}

	// 提取基本信息
	if make, err := x.Get(exif.Make); err == nil {
		if makeStr, err := make.StringVal(); err == nil {
			exifData.Make = strings.TrimSpace(makeStr)
		}
	}

	if model, err := x.Get(exif.Model); err == nil {
		if modelStr, err := model.StringVal(); err == nil {
			exifData.Model = strings.TrimSpace(modelStr)
		}
	}

	if dateTime, err := x.Get(exif.DateTime); err == nil {
		if dateTimeStr, err := dateTime.StringVal(); err == nil {
			if dt, err := time.Parse("2006:01:02 15:04:05", dateTimeStr); err == nil {
				exifData.DateTime = dt
			}
		}
	}

	if orientation, err := x.Get(exif.Orientation); err == nil {
		if val, err := orientation.Int(0); err == nil {
			exifData.Orientation = val
		}
	}

	if software, err := x.Get(exif.Software); err == nil {
		if softwareStr, err := software.StringVal(); err == nil {
			exifData.Software = strings.TrimSpace(softwareStr)
		}
	}

	// 提取相机设置
	if fNumber, err := x.Get(exif.FNumber); err == nil {
		if num, denom, err := fNumber.Rat2(0); err == nil {
			exifData.FNumber = float64(num) / float64(denom)
		}
	}

	if exposureTime, err := x.Get(exif.ExposureTime); err == nil {
		if exposureTimeStr, err := exposureTime.StringVal(); err == nil {
			exifData.ExposureTime = exposureTimeStr
		}
	}

	if iso, err := x.Get(exif.ISOSpeedRatings); err == nil {
		if val, err := iso.Int(0); err == nil {
			exifData.ISO = val
		}
	}

	if focalLength, err := x.Get(exif.FocalLength); err == nil {
		if num, denom, err := focalLength.Rat2(0); err == nil {
			exifData.FocalLength = float64(num) / float64(denom)
		}
	}

	if flash, err := x.Get(exif.Flash); err == nil {
		if flashStr, err := flash.StringVal(); err == nil {
			exifData.Flash = ep.parseFlash(flashStr)
		}
	}

	// 提取图片信息
	if width, err := x.Get(exif.PixelXDimension); err == nil {
		if val, err := width.Int(0); err == nil {
			exifData.Width = val
		}
	}

	if height, err := x.Get(exif.PixelYDimension); err == nil {
		if val, err := height.Int(0); err == nil {
			exifData.Height = val
		}
	}

	if xRes, err := x.Get(exif.XResolution); err == nil {
		if num, denom, err := xRes.Rat2(0); err == nil {
			exifData.XResolution = float64(num) / float64(denom)
		}
	}

	if yRes, err := x.Get(exif.YResolution); err == nil {
		if num, denom, err := yRes.Rat2(0); err == nil {
			exifData.YResolution = float64(num) / float64(denom)
		}
	}

	if colorSpace, err := x.Get(exif.ColorSpace); err == nil {
		if colorSpaceStr, err := colorSpace.StringVal(); err == nil {
			exifData.ColorSpace = ep.parseColorSpace(colorSpaceStr)
		}
	}

	// 提取GPS信息
	if lat, lon, err := x.LatLong(); err == nil {
		exifData.GPSLatitude = lat
		exifData.GPSLongitude = lon
	}

	// 提取其他信息
	if copyright, err := x.Get(exif.Copyright); err == nil {
		if copyrightStr, err := copyright.StringVal(); err == nil {
			exifData.Copyright = strings.TrimSpace(copyrightStr)
		}
	}

	if artist, err := x.Get(exif.Artist); err == nil {
		if artistStr, err := artist.StringVal(); err == nil {
			exifData.Artist = strings.TrimSpace(artistStr)
		}
	}

	if description, err := x.Get(exif.ImageDescription); err == nil {
		if descriptionStr, err := description.StringVal(); err == nil {
			exifData.Description = strings.TrimSpace(descriptionStr)
		}
	}

	// 提取所有原始EXIF数据
	walker := &exifWalker{exifData: exifData}
	x.Walk(walker)

	return exifData, nil
}

// exifWalker 实现exif.Walker接口
type exifWalker struct {
	exifData *EXIFData
}

// Walk 实现Walker接口的Walk方法
func (w *exifWalker) Walk(name exif.FieldName, tag *tiff.Tag) error {
	if tagStr, err := tag.StringVal(); err == nil {
		w.exifData.RawData[string(name)] = tagStr
	}
	return nil
}

// parseFlash 解析闪光灯信息
func (ep *EXIFProcessor) parseFlash(flashStr string) string {
	flashMap := map[string]string{
		"0":  "No Flash",
		"1":  "Flash",
		"5":  "Flash, No Return",
		"7":  "Flash, Return",
		"9":  "Flash, Compulsory",
		"13": "Flash, Compulsory, No Return",
		"15": "Flash, Compulsory, Return",
		"16": "No Flash, Compulsory",
		"24": "No Flash, Auto",
		"25": "Flash, Auto",
		"29": "Flash, Auto, No Return",
		"31": "Flash, Auto, Return",
		"32": "No Flash Available",
	}

	if desc, exists := flashMap[flashStr]; exists {
		return desc
	}
	return flashStr
}

// parseColorSpace 解析色彩空间
func (ep *EXIFProcessor) parseColorSpace(colorSpaceStr string) string {
	switch colorSpaceStr {
	case "1":
		return "sRGB"
	case "2":
		return "Adobe RGB"
	case "65535":
		return "Uncalibrated"
	default:
		return colorSpaceStr
	}
}

// RemoveEXIF 移除EXIF数据
func (ep *EXIFProcessor) RemoveEXIF(inputPath, outputPath string) error {
	// 打开原始文件
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// 解码图片（这会丢失EXIF数据）
	img, format, err := image.Decode(inputFile)
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 创建输出文件
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	// 重新编码图片（不包含EXIF数据）
	switch strings.ToLower(format) {
	case "jpeg":
		return jpeg.Encode(outputFile, img, &jpeg.Options{Quality: 95})
	case "png":
		return png.Encode(outputFile, img)
	case "gif":
		return gif.Encode(outputFile, img, nil)
	default:
		return jpeg.Encode(outputFile, img, &jpeg.Options{Quality: 95})
	}
}

// GetImageOrientation 获取图片方向
func (ep *EXIFProcessor) GetImageOrientation(imagePath string) (int, error) {
	exifData, err := ep.ExtractEXIF(imagePath)
	if err != nil {
		return 1, err // 默认方向
	}

	if exifData.Orientation == 0 {
		return 1, nil // 默认方向
	}

	return exifData.Orientation, nil
}

// CorrectOrientation 根据EXIF方向信息校正图片
func (ep *EXIFProcessor) CorrectOrientation(inputPath, outputPath string) error {
	orientation, err := ep.GetImageOrientation(inputPath)
	if err != nil {
		return err
	}

	// 如果方向正常，直接复制文件
	if orientation == 1 {
		return ep.copyFile(inputPath, outputPath)
	}

	// 打开图片
	file, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open image: %v", err)
	}
	defer file.Close()

	img, format, err := image.Decode(file)
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 根据方向进行变换
	var transformedImg image.Image
	switch orientation {
	case 2:
		transformedImg = ep.flipHorizontal(img)
	case 3:
		transformedImg = ep.rotate180(img)
	case 4:
		transformedImg = ep.flipVertical(img)
	case 5:
		transformedImg = ep.flipHorizontal(ep.rotate90(img))
	case 6:
		transformedImg = ep.rotate90(img)
	case 7:
		transformedImg = ep.flipHorizontal(ep.rotate270(img))
	case 8:
		transformedImg = ep.rotate270(img)
	default:
		transformedImg = img
	}

	// 保存变换后的图片
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	switch strings.ToLower(format) {
	case "jpeg":
		return jpeg.Encode(outputFile, transformedImg, &jpeg.Options{Quality: 95})
	case "png":
		return png.Encode(outputFile, transformedImg)
	case "gif":
		return gif.Encode(outputFile, transformedImg, nil)
	default:
		return jpeg.Encode(outputFile, transformedImg, &jpeg.Options{Quality: 95})
	}
}

// 辅助函数
func (ep *EXIFProcessor) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}

func (ep *EXIFProcessor) flipHorizontal(img image.Image) image.Image {
	bounds := img.Bounds()
	flipped := image.NewRGBA(bounds)

	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			newX := bounds.Max.X - 1 - (x - bounds.Min.X)
			flipped.Set(newX, y, img.At(x, y))
		}
	}

	return flipped
}

func (ep *EXIFProcessor) flipVertical(img image.Image) image.Image {
	bounds := img.Bounds()
	flipped := image.NewRGBA(bounds)

	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			newY := bounds.Max.Y - 1 - (y - bounds.Min.Y)
			flipped.Set(x, newY, img.At(x, y))
		}
	}

	return flipped
}

func (ep *EXIFProcessor) rotate90(img image.Image) image.Image {
	bounds := img.Bounds()
	rotated := image.NewRGBA(image.Rect(0, 0, bounds.Dy(), bounds.Dx()))

	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			rotated.Set(bounds.Dy()-1-(y-bounds.Min.Y), x-bounds.Min.X, img.At(x, y))
		}
	}

	return rotated
}

func (ep *EXIFProcessor) rotate180(img image.Image) image.Image {
	bounds := img.Bounds()
	rotated := image.NewRGBA(bounds)

	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			newX := bounds.Max.X - 1 - (x - bounds.Min.X)
			newY := bounds.Max.Y - 1 - (y - bounds.Min.Y)
			rotated.Set(newX, newY, img.At(x, y))
		}
	}

	return rotated
}

func (ep *EXIFProcessor) rotate270(img image.Image) image.Image {
	bounds := img.Bounds()
	rotated := image.NewRGBA(image.Rect(0, 0, bounds.Dy(), bounds.Dx()))

	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			rotated.Set(y-bounds.Min.Y, bounds.Dx()-1-(x-bounds.Min.X), img.At(x, y))
		}
	}

	return rotated
}
