<template>
  <div class="virtual-grid-container" ref="containerRef" @scroll="onScroll">
    <div class="virtual-grid-content" :style="{ height: totalHeight + 'px' }">
      <div
        class="virtual-grid-viewport"
        :style="{
          transform: `translateY(${offsetY}px)`,
          height: viewportHeight + 'px'
        }"
      >
        <div
          class="virtual-grid-row"
          v-for="(row, rowIndex) in visibleRows"
          :key="startRow + rowIndex"
          :style="{
            height: itemHeight + 'px',
            display: 'flex',
            gap: gap + 'px'
          }"
        >
          <div
            class="virtual-grid-item"
            v-for="(item, colIndex) in row"
            :key="item ? item.id || (startRow + rowIndex) * itemsPerRow + colIndex : 'empty'"
            :style="{
              width: itemWidth + 'px',
              height: itemHeight + 'px'
            }"
          >
            <slot
              v-if="item"
              name="item"
              :item="item"
              :index="(startRow + rowIndex) * itemsPerRow + colIndex"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 加载更多指示器 -->
    <div v-if="loading" class="loading-indicator">
      <i class="bi bi-arrow-repeat spin"></i>
      <span>加载中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

interface Props {
  items: any[]
  itemHeight: number
  itemsPerRow?: number
  gap?: number
  overscan?: number
  loading?: boolean
  hasMore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  itemsPerRow: 4,
  gap: 16,
  overscan: 3,
  loading: false,
  hasMore: false
})

const emit = defineEmits<{
  loadMore: []
  scroll: [scrollTop: number]
}>()

const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)
const containerHeight = ref(0)
const containerWidth = ref(0)

// 计算每行的实际项目数（考虑容器宽度）
const actualItemsPerRow = computed(() => {
  if (containerWidth.value === 0) return props.itemsPerRow
  
  const availableWidth = containerWidth.value - props.gap
  const itemWidthWithGap = props.itemHeight + props.gap
  const maxItems = Math.floor(availableWidth / itemWidthWithGap)
  
  return Math.max(1, Math.min(maxItems, props.itemsPerRow))
})

// 计算项目宽度
const itemWidth = computed(() => {
  const totalGap = (actualItemsPerRow.value - 1) * props.gap
  return (containerWidth.value - totalGap) / actualItemsPerRow.value
})

// 计算总行数
const totalRows = computed(() => {
  return Math.ceil(props.items.length / actualItemsPerRow.value)
})

// 计算总高度
const totalHeight = computed(() => {
  return totalRows.value * (props.itemHeight + props.gap) - props.gap
})

// 计算可见区域
const visibleRange = computed(() => {
  const start = Math.floor(scrollTop.value / (props.itemHeight + props.gap))
  const visibleRows = Math.ceil(containerHeight.value / (props.itemHeight + props.gap))
  
  const startRow = Math.max(0, start - props.overscan)
  const endRow = Math.min(totalRows.value - 1, start + visibleRows + props.overscan)
  
  return { startRow, endRow }
})

// 当前显示的行
const startRow = computed(() => visibleRange.value.startRow)
const endRow = computed(() => visibleRange.value.endRow)

// 偏移量
const offsetY = computed(() => {
  return startRow.value * (props.itemHeight + props.gap)
})

// 视口高度
const viewportHeight = computed(() => {
  return (endRow.value - startRow.value + 1) * (props.itemHeight + props.gap)
})

// 可见的行数据
const visibleRows = computed(() => {
  const rows: any[][] = []
  
  for (let rowIndex = startRow.value; rowIndex <= endRow.value; rowIndex++) {
    const row: any[] = []
    
    for (let colIndex = 0; colIndex < actualItemsPerRow.value; colIndex++) {
      const itemIndex = rowIndex * actualItemsPerRow.value + colIndex
      row.push(props.items[itemIndex] || null)
    }
    
    rows.push(row)
  }
  
  return rows
})

// 滚动处理
const onScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
  emit('scroll', target.scrollTop)
  
  // 检查是否需要加载更多
  const scrollBottom = target.scrollTop + target.clientHeight
  const threshold = target.scrollHeight - 200 // 提前200px触发
  
  if (scrollBottom >= threshold && props.hasMore && !props.loading) {
    emit('loadMore')
  }
}

// 更新容器尺寸
const updateContainerSize = () => {
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect()
    containerHeight.value = rect.height
    containerWidth.value = rect.width
  }
}

// 滚动到指定项目
const scrollToItem = (index: number) => {
  if (!containerRef.value) return
  
  const row = Math.floor(index / actualItemsPerRow.value)
  const targetScrollTop = row * (props.itemHeight + props.gap)
  
  containerRef.value.scrollTop = targetScrollTop
}

// 滚动到顶部
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0
  }
}

// 获取可见项目的索引范围
const getVisibleItemRange = () => {
  const startIndex = startRow.value * actualItemsPerRow.value
  const endIndex = Math.min(
    props.items.length - 1,
    (endRow.value + 1) * actualItemsPerRow.value - 1
  )
  
  return { startIndex, endIndex }
}

// 监听窗口大小变化
const resizeObserver = ref<ResizeObserver>()

onMounted(() => {
  updateContainerSize()
  
  // 使用 ResizeObserver 监听容器大小变化
  if (containerRef.value) {
    resizeObserver.value = new ResizeObserver(() => {
      updateContainerSize()
    })
    resizeObserver.value.observe(containerRef.value)
  }
})

onUnmounted(() => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
})

// 监听items变化，重置滚动位置
watch(() => props.items.length, (newLength, oldLength) => {
  // 如果是新的数据集（长度变小），滚动到顶部
  if (newLength < oldLength) {
    nextTick(() => {
      scrollToTop()
    })
  }
})

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  getVisibleItemRange,
  updateContainerSize
})
</script>

<style scoped>
.virtual-grid-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.virtual-grid-content {
  position: relative;
  width: 100%;
}

.virtual-grid-viewport {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.virtual-grid-row {
  width: 100%;
  margin-bottom: var(--gap, 16px);
}

.virtual-grid-item {
  flex-shrink: 0;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #6c757d;
}

.loading-indicator i {
  font-size: 1.2rem;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 */
.virtual-grid-container::-webkit-scrollbar {
  width: 8px;
}

.virtual-grid-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.virtual-grid-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.virtual-grid-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
