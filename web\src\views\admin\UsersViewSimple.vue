<template>
  <div class="users-simple">
    <h1>用户管理 - 简化版</h1>
    
    <div class="debug-section">
      <h3>调试信息</h3>
      <p>Loading: {{ loading }}</p>
      <p>Error: {{ error }}</p>
      <p>Users Count: {{ users.length }}</p>
      <button @click="testFetch" class="btn">测试获取数据</button>
    </div>

    <div class="users-section">
      <h3>用户列表</h3>
      <div v-if="loading">加载中...</div>
      <div v-else-if="error">错误: {{ error }}</div>
      <div v-else-if="users.length === 0">没有用户数据</div>
      <div v-else>
        <div v-for="(user, index) in users" :key="index" class="user-item">
          <p>用户 {{ index + 1 }}: {{ JSON.stringify(user) }}</p>
        </div>
      </div>
    </div>

    <div class="raw-data">
      <h3>原始数据</h3>
      <pre>{{ JSON.stringify(users, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import api from '../../services/api';

const loading = ref(false);
const error = ref('');
const users = ref<any[]>([]);

const testFetch = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    console.log('开始获取用户数据...');
    const response = await api.getAllUsersWithStats();
    console.log('API响应:', response);
    
    users.value = response.users || [];
    console.log('用户数据:', users.value);
  } catch (err: any) {
    console.error('获取用户数据失败:', err);
    error.value = err.message || '获取数据失败';
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  console.log('组件已挂载，开始获取数据');
  testFetch();
});
</script>

<style scoped>
.users-simple {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.debug-section, .users-section, .raw-data {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.user-item {
  padding: 10px;
  margin: 5px 0;
  background: #f5f5f5;
  border-radius: 3px;
}

.btn {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn:hover {
  background: #0056b3;
}

pre {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 3px;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}
</style>
