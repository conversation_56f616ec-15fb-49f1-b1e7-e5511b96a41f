import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import AuthView from '../views/AuthView.vue';
import DashboardView from '../views/DashboardView.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/auth'
  },
  {
    path: '/auth',
    name: 'Auth',
    component: AuthView
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: DashboardView,
    redirect: '/user/photos',
    children: [
      // 用户个人功能
      {
        path: '/user/photos',
        name: 'UserPhotos',
        component: () => import('../views/MyImagesView.vue')
      },
      {
        path: '/user/albums',
        name: 'UserAlbums',
        component: () => import('../views/MyAlbumsView.vue')
      },
      {
        path: '/user/profile',
        name: 'UserProfile',
        component: () => import('../views/ProfileView.vue')
      },
      {
        path: '/user/settings',
        name: 'UserSettings',
        component: () => import('../views/SettingsView.vue')
      },

      // 兼容旧路径（重定向）
      {
        path: '/my-images',
        redirect: '/user/photos'
      },
      {
        path: '/my-albums',
        redirect: '/user/albums'
      },
      {
        path: '/profile',
        redirect: '/user/profile'
      },
      {
        path: '/settings',
        redirect: '/user/settings'
      },
      {
        path: '/admin/users',
        name: 'AdminUsers',
        component: () => import('../views/admin/UsersViewFixed.vue'),
        meta: { requiresAdmin: true }
      },
      {
        path: '/admin/groups',
        name: 'AdminGroups',
        component: () => import('../views/admin/GroupsView.vue'),
        meta: { requiresAdmin: true }
      },
      {
        path: '/admin/images',
        name: 'AdminImages',
        component: () => import('../views/admin/ImagesView.vue'),
        meta: { requiresAdmin: true }
      },
      {
        path: '/admin/roles',
        name: 'AdminRoles',
        component: () => import('../views/admin/RolesView.vue'),
        meta: { requiresAdmin: true }
      },
      {
        path: '/admin/storage',
        name: 'AdminStorage',
        component: () => import('../views/admin/StorageManagementView.vue'),
        meta: { requiresAdmin: true }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 路由守卫
router.beforeEach((to, _from, next) => {
  const authStore = useAuthStore();
  authStore.initAuth();

  const publicPages = ['/auth'];
  const authRequired = !publicPages.includes(to.path);

  if (authRequired && !authStore.isAuthenticated) {
    next('/auth');
  } else if (to.path === '/auth' && authStore.isAuthenticated) {
    next('/dashboard');
  } else if (to.meta?.requiresAdmin) {
    // 检查管理员权限
    const user = authStore.getUser;
    if (!user?.role || (user.role.name !== 'admin' && user.role.name !== 'super_admin')) {
      next('/dashboard'); // 重定向到仪表盘
      return;
    }
    next();
  } else {
    next();
  }
});

export default router;