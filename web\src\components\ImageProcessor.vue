<template>
  <div class="image-processor">
    <div class="processor-header">
      <h3>图片处理</h3>
      <button class="close-btn" @click="$emit('close')">
        <i class="bi bi-x"></i>
      </button>
    </div>

    <div class="processor-content">
      <!-- 预览区域 -->
      <div class="preview-section">
        <div class="original-preview">
          <h4>原图</h4>
          <div class="image-container">
            <img :src="image.url" :alt="image.name" class="preview-image" />
            <div class="image-info">
              <p>{{ image.name }}</p>
              <p>{{ formatFileSize(image.size) }}</p>
              <p v-if="image.width && image.height">{{ image.width }} × {{ image.height }}</p>
            </div>
          </div>
        </div>

        <div class="processed-preview" v-if="processedImage">
          <h4>处理后</h4>
          <div class="image-container">
            <img :src="processedImage.url" :alt="processedImage.name" class="preview-image" />
            <div class="image-info">
              <p>{{ processedImage.name }}</p>
              <p>{{ formatFileSize(processedImage.size) }}</p>
              <p v-if="processedImage.width && processedImage.height">
                {{ processedImage.width }} × {{ processedImage.height }}
              </p>
              <p v-if="compressionRatio" class="compression-info">
                压缩率: {{ (compressionRatio * 100).toFixed(1) }}%
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 处理选项 -->
      <div class="options-section">
        <!-- 预设选项 -->
        <div class="preset-section">
          <h4>快速预设</h4>
          <div class="preset-buttons">
            <button
              v-for="(preset, key) in presets"
              :key="key"
              class="preset-btn"
              @click="applyPreset(key, preset)"
            >
              {{ getPresetName(key) }}
            </button>
          </div>
        </div>

        <!-- 详细选项 -->
        <div class="detailed-options">
          <div class="option-tabs">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              class="tab-btn"
              :class="{ active: activeTab === tab.id }"
              @click="activeTab = tab.id"
            >
              <i :class="tab.icon"></i>
              {{ tab.name }}
            </button>
          </div>

          <div class="tab-content">
            <!-- 格式转换 -->
            <div v-if="activeTab === 'format'" class="tab-panel">
              <div class="form-group">
                <label>输出格式</label>
                <select v-model="options.outputFormat" class="form-control">
                  <option value="">保持原格式</option>
                  <option value="jpeg">JPEG</option>
                  <option value="png">PNG</option>
                  <option value="webp">WebP</option>
                  <option value="gif">GIF</option>
                </select>
              </div>
              <div class="form-group" v-if="options.outputFormat === 'jpeg' || !options.outputFormat">
                <label>质量 ({{ options.quality }}%)</label>
                <input
                  type="range"
                  v-model="options.quality"
                  min="1"
                  max="100"
                  class="form-range"
                />
              </div>
              <div class="form-group">
                <label>
                  <input type="checkbox" v-model="options.compress" />
                  启用压缩
                </label>
              </div>
              <div class="form-group" v-if="options.compress">
                <label>最大文件大小 (KB)</label>
                <input
                  type="number"
                  v-model="options.maxFileSize"
                  min="10"
                  max="10000"
                  class="form-control"
                />
              </div>
            </div>

            <!-- 尺寸调整 -->
            <div v-if="activeTab === 'resize'" class="tab-panel">
              <div class="form-group">
                <label>调整模式</label>
                <select v-model="options.mode" class="form-control">
                  <option value="fit">适应 (保持比例)</option>
                  <option value="fill">填充 (可能裁剪)</option>
                  <option value="crop">裁剪</option>
                  <option value="stretch">拉伸</option>
                </select>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>宽度</label>
                  <input
                    type="number"
                    v-model="options.width"
                    min="1"
                    max="5000"
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label>高度</label>
                  <input
                    type="number"
                    v-model="options.height"
                    min="1"
                    max="5000"
                    class="form-control"
                  />
                </div>
              </div>
              <div class="form-group">
                <button class="btn btn-secondary" @click="resetToOriginalSize">
                  重置为原始尺寸
                </button>
              </div>
            </div>

            <!-- 旋转翻转 -->
            <div v-if="activeTab === 'transform'" class="tab-panel">
              <div class="form-group">
                <label>旋转</label>
                <div class="rotation-buttons">
                  <button
                    v-for="angle in [0, 90, 180, 270]"
                    :key="angle"
                    class="rotation-btn"
                    :class="{ active: options.rotation === angle }"
                    @click="options.rotation = angle"
                  >
                    {{ angle }}°
                  </button>
                </div>
              </div>
              <div class="form-group">
                <label>
                  <input type="checkbox" v-model="options.flipH" />
                  水平翻转
                </label>
              </div>
              <div class="form-group">
                <label>
                  <input type="checkbox" v-model="options.flipV" />
                  垂直翻转
                </label>
              </div>
            </div>

            <!-- 裁剪 -->
            <div v-if="activeTab === 'crop'" class="tab-panel">
              <div class="form-row">
                <div class="form-group">
                  <label>X坐标</label>
                  <input
                    type="number"
                    v-model="options.cropX"
                    min="0"
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label>Y坐标</label>
                  <input
                    type="number"
                    v-model="options.cropY"
                    min="0"
                    class="form-control"
                  />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>裁剪宽度</label>
                  <input
                    type="number"
                    v-model="options.cropWidth"
                    min="1"
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label>裁剪高度</label>
                  <input
                    type="number"
                    v-model="options.cropHeight"
                    min="1"
                    class="form-control"
                  />
                </div>
              </div>
              <div class="form-group">
                <button class="btn btn-secondary" @click="resetCrop">
                  重置裁剪
                </button>
              </div>
            </div>

            <!-- 水印 -->
            <div v-if="activeTab === 'watermark'" class="tab-panel">
              <div class="form-group">
                <label>水印文字</label>
                <input
                  type="text"
                  v-model="options.watermarkText"
                  placeholder="输入水印文字"
                  class="form-control"
                />
              </div>
              <div class="form-group">
                <label>位置</label>
                <select v-model="options.watermarkPosition" class="form-control">
                  <option value="top-left">左上角</option>
                  <option value="top-right">右上角</option>
                  <option value="bottom-left">左下角</option>
                  <option value="bottom-right">右下角</option>
                  <option value="center">居中</option>
                </select>
              </div>
              <div class="form-group">
                <label>透明度 ({{ (options.watermarkOpacity * 100).toFixed(0) }}%)</label>
                <input
                  type="range"
                  v-model="options.watermarkOpacity"
                  min="0"
                  max="1"
                  step="0.1"
                  class="form-range"
                />
              </div>
              <div class="form-group">
                <label>字体大小</label>
                <input
                  type="number"
                  v-model="options.watermarkSize"
                  min="8"
                  max="72"
                  class="form-control"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="processor-actions">
      <button class="btn btn-secondary" @click="resetOptions">
        重置选项
      </button>
      <button class="btn btn-primary" @click="processImage" :disabled="processing">
        <i class="bi bi-arrow-repeat spin" v-if="processing"></i>
        {{ processing ? '处理中...' : '开始处理' }}
      </button>
    </div>

    <!-- 处理进度 -->
    <div v-if="processing" class="processing-progress">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progress + '%' }"></div>
      </div>
      <p>正在处理图片，请稍候...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

interface Props {
  image: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  processed: [processedImage: any]
}>()

// 响应式数据
const processing = ref(false)
const progress = ref(0)
const processedImage = ref<any>(null)
const compressionRatio = ref<number | null>(null)
const activeTab = ref('format')

// 预设选项
const presets = ref<any>({})

// 处理选项
const options = reactive({
  outputFormat: '',
  quality: 85,
  width: 0,
  height: 0,
  mode: 'fit',
  rotation: 0,
  flipH: false,
  flipV: false,
  cropX: 0,
  cropY: 0,
  cropWidth: 0,
  cropHeight: 0,
  watermarkText: '',
  watermarkPosition: 'bottom-right',
  watermarkOpacity: 0.5,
  watermarkSize: 16,
  compress: false,
  maxFileSize: 500,
  progressiveJPEG: false
})

// 标签页配置
const tabs = [
  { id: 'format', name: '格式', icon: 'bi bi-file-earmark' },
  { id: 'resize', name: '尺寸', icon: 'bi bi-arrows-angle-expand' },
  { id: 'transform', name: '变换', icon: 'bi bi-arrow-clockwise' },
  { id: 'crop', name: '裁剪', icon: 'bi bi-crop' },
  { id: 'watermark', name: '水印', icon: 'bi bi-type' }
]

// 方法
const loadPresets = async () => {
  try {
    // TODO: 调用API获取预设
    // const response = await api.getProcessingPresets()
    // presets.value = response.data.presets
    
    // 模拟数据
    presets.value = {
      thumbnail_small: { width: 150, height: 150, mode: 'crop', quality: 85 },
      thumbnail_medium: { width: 300, height: 300, mode: 'crop', quality: 85 },
      web_optimized: { width: 1200, mode: 'fit', quality: 80, compress: true, maxFileSize: 500 },
      print_quality: { quality: 95, outputFormat: 'png' },
      social_media: { width: 1080, height: 1080, mode: 'crop', quality: 85 },
      compress_heavy: { quality: 60, compress: true, maxFileSize: 200 }
    }
  } catch (error) {
    console.error('Failed to load presets:', error)
  }
}

const getPresetName = (key: string) => {
  const names: Record<string, string> = {
    thumbnail_small: '小缩略图',
    thumbnail_medium: '中缩略图',
    web_optimized: '网页优化',
    print_quality: '打印质量',
    social_media: '社交媒体',
    compress_heavy: '高压缩'
  }
  return names[key] || key
}

const applyPreset = (key: string, preset: any) => {
  Object.assign(options, preset)
}

const resetOptions = () => {
  Object.assign(options, {
    outputFormat: '',
    quality: 85,
    width: 0,
    height: 0,
    mode: 'fit',
    rotation: 0,
    flipH: false,
    flipV: false,
    cropX: 0,
    cropY: 0,
    cropWidth: 0,
    cropHeight: 0,
    watermarkText: '',
    watermarkPosition: 'bottom-right',
    watermarkOpacity: 0.5,
    watermarkSize: 16,
    compress: false,
    maxFileSize: 500,
    progressiveJPEG: false
  })
}

const resetToOriginalSize = () => {
  options.width = props.image.width || 0
  options.height = props.image.height || 0
}

const resetCrop = () => {
  options.cropX = 0
  options.cropY = 0
  options.cropWidth = props.image.width || 0
  options.cropHeight = props.image.height || 0
}

const processImage = async () => {
  processing.value = true
  progress.value = 0
  
  try {
    // 模拟进度
    const progressInterval = setInterval(() => {
      if (progress.value < 90) {
        progress.value += 10
      }
    }, 200)

    // TODO: 调用API处理图片
    // const response = await api.processImage({
    //   image_id: props.image.id,
    //   ...options
    // })
    
    // 模拟处理结果
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    clearInterval(progressInterval)
    progress.value = 100
    
    // 模拟处理结果
    const result = {
      processed_image: {
        id: Date.now(),
        name: `processed_${props.image.name}`,
        url: props.image.url, // 实际应该是新的URL
        size: Math.floor(props.image.size * 0.7), // 模拟压缩
        width: options.width || props.image.width,
        height: options.height || props.image.height,
        format: options.outputFormat || props.image.format
      },
      processing_result: {
        compression_ratio: 0.7,
        operations: ['resize', 'compress']
      }
    }
    
    processedImage.value = result.processed_image
    compressionRatio.value = result.processing_result.compression_ratio
    
    emit('processed', result.processed_image)
    
  } catch (error) {
    console.error('Failed to process image:', error)
  } finally {
    processing.value = false
    progress.value = 0
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  loadPresets()
  resetToOriginalSize()
  resetCrop()
})
</script>

<style scoped>
.image-processor {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 1200px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
}

.processor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.processor-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.processor-content {
  padding: 24px;
}

.preview-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.original-preview,
.processed-preview {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.original-preview h4,
.processed-preview h4 {
  margin: 0;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.9rem;
  color: #495057;
}

.image-container {
  padding: 16px;
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-info {
  margin-top: 12px;
  font-size: 0.8rem;
  color: #6c757d;
}

.image-info p {
  margin: 4px 0;
}

.compression-info {
  color: #28a745 !important;
  font-weight: 500;
}

.preset-section {
  margin-bottom: 24px;
}

.preset-section h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  color: #495057;
}

.preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preset-btn {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.preset-btn:hover {
  background: #f8f9fa;
  border-color: #007bff;
  color: #007bff;
}

.option-tabs {
  display: flex;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.tab-btn {
  padding: 12px 20px;
  border: none;
  background: none;
  color: #6c757d;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.tab-btn:hover {
  color: #007bff;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.tab-panel {
  display: grid;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
}

.form-control,
.form-range {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-control:focus,
.form-range:focus {
  outline: none;
  border-color: #007bff;
}

.rotation-buttons {
  display: flex;
  gap: 8px;
}

.rotation-btn {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.rotation-btn:hover {
  background: #f8f9fa;
}

.rotation-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.processor-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.processing-progress {
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
