<template>
  <!-- 全局拖拽上传组件 -->
  <div
    v-if="isDragOver"
    class="global-drag-overlay"
    @dragover.prevent
    @drop.prevent="handleDrop"
    @dragleave="handleDragLeave"
  >
    <div class="drag-content">
      <div class="drag-icon">
        <i class="bi bi-cloud-upload"></i>
      </div>
      <div class="drag-text">
        <h3>拖拽图片到此处上传</h3>
        <p>支持 JPG、PNG、GIF、WebP 格式</p>
      </div>
    </div>
  </div>

  <!-- 上传进度提示 -->
  <div v-if="uploadQueue.length > 0" class="upload-progress-container">
    <div class="upload-progress-header">
      <h4>
        <i class="bi bi-upload"></i>
        上传进度 ({{ completedUploads }}/{{ uploadQueue.length }})
        <span v-if="allUploadsFinished && autoHideCountdown > 0" class="auto-hide-countdown">
          {{ autoHideCountdown }}秒后自动关闭
        </span>
      </h4>
      <button class="close-btn" @click="clearCompletedUploads">
        <i class="bi bi-x"></i>
      </button>
    </div>
    <div class="upload-items">
      <div
        v-for="item in uploadQueue"
        :key="item.id"
        class="upload-item"
        :class="{ 'completed': item.status === 'completed', 'error': item.status === 'error' }"
      >
        <div class="upload-info">
          <div class="file-name">{{ item.file.name }}</div>
          <div class="file-size">{{ formatBytes(item.file.size) }}</div>
        </div>
        <div class="upload-status">
          <div v-if="item.status === 'uploading'" class="progress-bar">
            <div class="progress-fill" :style="{ width: item.progress + '%' }"></div>
          </div>
          <i v-else-if="item.status === 'completed'" class="bi bi-check-circle-fill success-icon"></i>
          <i v-else-if="item.status === 'error'" class="bi bi-x-circle-fill error-icon"></i>
          <span v-if="item.status === 'error'" class="error-message">{{ item.error }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 全局通知 -->
  <div v-if="notification.show" :class="['global-notification', notification.type]">
    <i class="bi" :class="notification.type === 'success' ? 'bi-check-circle' : 'bi-exclamation-circle'"></i>
    <span>{{ notification.message }}</span>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue';
import { useImageStore } from '../stores/image';
import { useStorageStore } from '../stores/storage';

interface UploadItem {
  id: string;
  file: File;
  status: 'uploading' | 'completed' | 'error';
  progress: number;
  error?: string;
}

// 响应式数据
const isDragOver = ref(false);
const uploadQueue = ref<UploadItem[]>([]);
const dragCounter = ref(0);

const notification = reactive({
  show: false,
  message: '',
  type: 'success' as 'success' | 'error'
});

// 自动消失相关
const autoHideTimer = ref<number | null>(null);
const countdownTimer = ref<number | null>(null);
const autoHideCountdown = ref(0);
const AUTO_HIDE_DELAY = 3000; // 3秒后自动消失

// 使用stores
const imageStore = useImageStore();
const storageStore = useStorageStore();

// 计算属性
const completedUploads = computed(() => {
  return uploadQueue.value.filter(item => item.status === 'completed').length;
});

const allUploadsFinished = computed(() => {
  return uploadQueue.value.length > 0 &&
         uploadQueue.value.every(item => item.status === 'completed' || item.status === 'error');
});

// 全局拖拽事件处理
const handleGlobalDragEnter = (e: DragEvent) => {
  e.preventDefault();
  dragCounter.value++;
  
  // 检查是否包含文件
  if (e.dataTransfer?.types.includes('Files')) {
    isDragOver.value = true;
  }
};

const handleGlobalDragOver = (e: DragEvent) => {
  e.preventDefault();
};

const handleGlobalDragLeave = (e: DragEvent) => {
  e.preventDefault();
  dragCounter.value--;
  
  if (dragCounter.value === 0) {
    isDragOver.value = false;
  }
};

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault();
  // 检查是否真的离开了拖拽区域
  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
  const x = e.clientX;
  const y = e.clientY;
  
  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false;
    dragCounter.value = 0;
  }
};

const handleDrop = async (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = false;
  dragCounter.value = 0;

  const files = Array.from(e.dataTransfer?.files || []);
  const imageFiles = files.filter(file => file.type.startsWith('image/'));

  if (imageFiles.length === 0) {
    showNotification('请拖拽图片文件', 'error');
    return;
  }

  if (imageFiles.length !== files.length) {
    showNotification(`已过滤 ${files.length - imageFiles.length} 个非图片文件`, 'error');
  }

  // 添加到上传队列
  for (const file of imageFiles) {
    const uploadItem: UploadItem = {
      id: Date.now() + Math.random().toString(36),
      file,
      status: 'uploading',
      progress: 0
    };
    
    uploadQueue.value.push(uploadItem);
    uploadFile(uploadItem);
  }
};

// 上传文件
const uploadFile = async (uploadItem: UploadItem) => {
  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadItem.progress < 90) {
        const index = uploadQueue.value.findIndex(item => item.id === uploadItem.id);
        if (index !== -1) {
          uploadQueue.value[index].progress += Math.random() * 20;
          uploadItem.progress = uploadQueue.value[index].progress;
        }
      }
    }, 200);

    const result = await imageStore.uploadImage(uploadItem.file);

    clearInterval(progressInterval);
    uploadItem.progress = 100;

    if (result.success) {
      // 确保响应式更新
      const index = uploadQueue.value.findIndex(item => item.id === uploadItem.id);
      if (index !== -1) {
        uploadQueue.value[index].status = 'completed';
        uploadQueue.value[index].progress = 100;
      }

      // 更新存储使用量
      storageStore.updateStorageUsed(uploadItem.file.size);

      showNotification(`${uploadItem.file.name} 上传成功`);
    } else {
      // 确保响应式更新
      const index = uploadQueue.value.findIndex(item => item.id === uploadItem.id);
      if (index !== -1) {
        uploadQueue.value[index].status = 'error';
        uploadQueue.value[index].error = result.message || '上传失败';
      }
      showNotification(`${uploadItem.file.name} 上传失败: ${result.message || '上传失败'}`, 'error');
    }
  } catch (error: any) {
    // 确保响应式更新
    const index = uploadQueue.value.findIndex(item => item.id === uploadItem.id);
    if (index !== -1) {
      uploadQueue.value[index].status = 'error';
      uploadQueue.value[index].error = error.message || '上传失败';
    }
    showNotification(`${uploadItem.file.name} 上传失败: ${error.message || '上传失败'}`, 'error');
  }
};

// 清除已完成的上传
const clearCompletedUploads = () => {
  uploadQueue.value = uploadQueue.value.filter(item => item.status === 'uploading');
  clearAutoHideTimer();
};

// 清除自动隐藏计时器
const clearAutoHideTimer = () => {
  if (autoHideTimer.value) {
    clearTimeout(autoHideTimer.value);
    autoHideTimer.value = null;
  }
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
  autoHideCountdown.value = 0;
};

// 自动隐藏上传进度容器
const autoHideUploadProgress = () => {
  clearAutoHideTimer();

  // 开始倒计时显示
  autoHideCountdown.value = Math.ceil(AUTO_HIDE_DELAY / 1000);

  // 倒计时更新
  countdownTimer.value = setInterval(() => {
    autoHideCountdown.value--;
    if (autoHideCountdown.value <= 0) {
      clearInterval(countdownTimer.value!);
      countdownTimer.value = null;
    }
  }, 1000);

  // 自动隐藏计时器
  autoHideTimer.value = setTimeout(() => {
    uploadQueue.value = [];
    autoHideCountdown.value = 0;
  }, AUTO_HIDE_DELAY);
};

// 显示通知
const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  notification.message = message;
  notification.type = type;
  notification.show = true;
  setTimeout(() => {
    notification.show = false;
  }, 3000);
};

// 格式化文件大小
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 移除了快捷键功能，只保留拖拽上传

// 监听上传完成状态
watch(allUploadsFinished, (finished) => {
  if (finished) {
    autoHideUploadProgress();
  }
});

// 生命周期
onMounted(() => {
  // 添加全局拖拽事件监听
  document.addEventListener('dragenter', handleGlobalDragEnter);
  document.addEventListener('dragover', handleGlobalDragOver);
  document.addEventListener('dragleave', handleGlobalDragLeave);
});

onUnmounted(() => {
  // 移除全局拖拽事件监听
  document.removeEventListener('dragenter', handleGlobalDragEnter);
  document.removeEventListener('dragover', handleGlobalDragOver);
  document.removeEventListener('dragleave', handleGlobalDragLeave);
  clearAutoHideTimer();
});
</script>

<style scoped>
/* 全局拖拽覆盖层 */
.global-drag-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 123, 255, 0.1);
  backdrop-filter: blur(2px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px dashed #007bff;
  animation: dragOverlay 0.3s ease-in-out;
}

@keyframes dragOverlay {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.drag-content {
  text-align: center;
  color: #007bff;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 123, 255, 0.2);
}

.drag-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.drag-text h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.drag-text p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* 移除了快捷键相关样式 */

/* 上传进度容器 */
.upload-progress-container {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400px;
  overflow: hidden;
}

.upload-progress-header {
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-progress-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.auto-hide-countdown {
  font-size: 12px;
  color: #666;
  font-weight: normal;
  margin-left: 8px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-btn:hover {
  background: #e9ecef;
}

.upload-items {
  max-height: 300px;
  overflow-y: auto;
}

.upload-item {
  padding: 12px 20px;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-item:last-child {
  border-bottom: none;
}

.upload-item.completed {
  background: #f8fff8;
}

.upload-item.error {
  background: #fff8f8;
}

.upload-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 11px;
  color: #666;
  margin-top: 2px;
}

.upload-status {
  flex-shrink: 0;
  margin-left: 12px;
}

.progress-bar {
  width: 60px;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

.success-icon {
  color: #28a745;
  font-size: 16px;
}

.error-icon {
  color: #dc3545;
  font-size: 16px;
}

.error-message {
  font-size: 11px;
  color: #dc3545;
  margin-left: 8px;
}

/* 全局通知 */
.global-notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 20px;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  z-index: 10000;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.global-notification.success {
  background: #28a745;
}

.global-notification.error {
  background: #dc3545;
}
</style>
