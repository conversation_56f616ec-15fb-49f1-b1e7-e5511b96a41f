<template>
  <div class="my-images-container">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2><i class="bi bi-images"></i> 我的图片</h2>
          <p class="page-subtitle">查看和管理您的所有图片</p>
        </div>
        <div class="header-right">
          <button
            class="btn btn-secondary"
            :class="{ active: batchMode }"
            @click="toggleBatchMode"
          >
            <i class="bi bi-check-square"></i>
            {{ batchMode ? '退出批量' : '批量操作' }}
          </button>
          <button class="btn btn-primary" @click="showUploadModal = true">
            <i class="bi bi-cloud-upload"></i> 上传图片
          </button>
        </div>
      </div>
    </div>

    <!-- 图片网格视图 -->
    <div class="images-view">
      <ImageGrid
        :images="images"
        :loading="loading"
        :has-more="hasMore"
        :selectable="batchMode"
        :selected-images="selectedImages"
        @image-click="viewImage"
        @image-view="viewImage"
        @image-download="downloadImage"
        @image-delete="deleteImage"
        @load-more="loadMoreImages"
        @upload="showUploadModal = true"
        @scroll="onScroll"
        @selection-change="handleSelectionChange"
      />
    </div>

    <!-- 上传模态框 -->
    <div v-if="showUploadModal" class="modal-overlay" @click="showUploadModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>上传图片</h3>
          <button class="close-btn" @click="showUploadModal = false">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="upload-area" @click="triggerFileUpload">
            <i class="bi bi-cloud-upload"></i>
            <p>点击选择图片文件</p>
            <p class="upload-hint">支持 JPG、PNG、GIF 格式</p>
          </div>
          <input
            ref="fileInput"
            type="file"
            multiple
            accept="image/*"
            style="display: none"
            @change="handleFileUpload"
          />
        </div>
      </div>
    </div>

    <!-- 通知 -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>

    <!-- 批量操作工具栏 -->
    <BatchOperations
      v-if="batchMode"
      :selected-items="selectedImages"
      :all-selected="selectedImages.length === images.length && images.length > 0"
      @select-all="selectAllImages"
      @clear-selection="clearSelection"
      @items-deleted="handleItemsDeleted"
      @items-moved="handleItemsMoved"
      @refresh="refreshImages"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useImageStore } from '../stores/image';
import { useStorageStore } from '../stores/storage';
import type { Image } from '../types/image';
import { useDialog } from '../utils/dialog';
import ImageGrid from '../components/ImageGrid.vue';
import BatchOperations from '../components/BatchOperations.vue';

const imageStore = useImageStore();
const storageStore = useStorageStore();
const { confirmDeleteAsync, notify } = useDialog();

// 响应式数据
const loading = ref(false);
const images = ref<Image[]>([]);
const showUploadModal = ref(false);
const fileInput = ref<HTMLInputElement | null>(null);
const hasMore = ref(false);
const currentPage = ref(1);
const pageSize = ref(20);
const batchMode = ref(false);
const selectedImages = ref<number[]>([]);

const notification = reactive({
  show: false,
  message: '',
  type: 'success' as 'success' | 'error'
});

// 生命周期
onMounted(() => {
  loadImages();
});

// 方法
const loadImages = async (reset = true) => {
  loading.value = true;
  try {
    if (reset) {
      currentPage.value = 1;
      images.value = [];
    }

    const result = await imageStore.fetchImagesPaginated({
      page: currentPage.value,
      page_size: pageSize.value
    });

    if (result.success && result.data) {
      if (reset) {
        images.value = result.data.data;
      } else {
        images.value.push(...result.data.data);
      }
      hasMore.value = result.data.has_next;
    } else {
      showNotification('加载图片失败', 'error');
    }
  } catch (error) {
    showNotification('加载图片失败', 'error');
  } finally {
    loading.value = false;
  }
};

const loadMoreImages = async () => {
  if (loading.value || !hasMore.value) return;

  currentPage.value++;
  await loadImages(false);
};

const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  notification.message = message;
  notification.type = type;
  notification.show = true;
  setTimeout(() => {
    notification.show = false;
  }, 3000);
};

const triggerFileUpload = () => {
  fileInput.value?.click();
};

const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;

  if (!files || files.length === 0) {
    return;
  }

  let totalUploadedSize = 0;
  for (const file of Array.from(files)) {
    const result = await imageStore.uploadImage(file);
    if (result.success) {
      showNotification(`图片 ${file.name} 上传成功`);
      totalUploadedSize += file.size;
    } else {
      showNotification(`图片 ${file.name} 上传失败: ${result.message}`, 'error');
    }
  }

  // 清空文件输入
  if (target) {
    target.value = '';
  }

  showUploadModal.value = false;
  // 重新加载图片列表
  loadImages();

  // 更新存储使用量
  if (totalUploadedSize > 0) {
    storageStore.updateStorageUsed(totalUploadedSize);
  }
};

const viewImage = (image: Image) => {
  // TODO: 实现图片查看功能
  console.log('查看图片:', image);
};

const deleteImage = async (image: Image) => {
  confirmDeleteAsync(image.name, async () => {
    const result = await imageStore.deleteImage(image.id);
    if (result.success) {
      notify.success('图片删除成功');
      loadImages(); // 重新加载列表

      // 更新存储使用量
      storageStore.updateStorageUsed(-image.size);
    } else {
      notify.error(result.message || '删除失败');
    }
  });
};

const downloadImage = (image: Image) => {
  // 创建下载链接
  const link = document.createElement('a');
  link.href = image.url;
  link.download = image.name;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const onScroll = (scrollTop: number) => {
  // 可以在这里添加滚动相关的逻辑
  console.log('Scrolled to:', scrollTop);
};

// 批量操作相关方法
const toggleBatchMode = () => {
  batchMode.value = !batchMode.value;
  if (!batchMode.value) {
    selectedImages.value = [];
  }
};

const handleSelectionChange = (newSelection: number[]) => {
  selectedImages.value = newSelection;
};

const selectAllImages = () => {
  selectedImages.value = images.value.map(img => img.id);
};

const clearSelection = () => {
  selectedImages.value = [];
};

const handleItemsDeleted = (deletedIds: number[]) => {
  // 从本地列表中移除已删除的图片
  images.value = images.value.filter(img => !deletedIds.includes(img.id));
  selectedImages.value = [];
};

const handleItemsMoved = (movedIds: number[], albumId: number | null) => {
  // 重新加载图片列表
  loadImages();
  selectedImages.value = [];
};

const refreshImages = () => {
  loadImages();
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};
</script>

<style scoped>
.my-images-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.header-left {
  flex: 1;
}

.header-right {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
  padding-top: 4px; /* 微调对齐 */
}

.page-header h2 {
  color: #333;
  margin-bottom: 8px;
}

.page-subtitle {
  color: #666;
  margin: 0;
}

.action-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.loading-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #007bff;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.image-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
}

.image-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.image-thumbnail {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.image-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-info {
  padding: 16px;
}

.image-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-meta {
  margin: 0;
  font-size: 12px;
  color: #666;
  display: flex;
  justify-content: space-between;
}

.image-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.image-card:hover .image-actions {
  opacity: 1;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
}

.delete-btn {
  background-color: rgba(220, 53, 69, 0.9);
  color: white;
}

.delete-btn:hover {
  background-color: #dc3545;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #666;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 20px;
  color: #ddd;
}

.empty-state h3 {
  margin-bottom: 12px;
  color: #333;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;
}

.upload-area:hover {
  border-color: #007bff;
}

.upload-area i {
  font-size: 48px;
  color: #007bff;
  margin-bottom: 16px;
}

.upload-area p {
  margin: 8px 0;
  color: #333;
}

.upload-hint {
  font-size: 12px;
  color: #666;
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  z-index: 1001;
}

.notification.success {
  background-color: #28a745;
}

.notification.error {
  background-color: #dc3545;
}
</style>
