# CloudBed System

一个功能完整的云端图片存储和管理系统，支持多用户、相册管理、权限控制和多种存储后端。

## ✨ 主要功能

- 🖼️ **图片管理**：上传、查看、删除、批量操作
- 📁 **相册系统**：创建相册、图片分类管理
- 👥 **用户管理**：多用户支持、角色权限控制
- 🔐 **安全认证**：JWT认证、密码加密
- 💾 **多存储支持**：本地存储、阿里云OSS、腾讯云COS、七牛云等
- 📊 **管理后台**：用户管理、存储配额、系统统计
- 🎨 **现代界面**：响应式设计、暗色主题支持

## 🏗️ 技术架构

### 后端
- **语言**：Go 1.21+
- **框架**：Gin Web Framework
- **数据库**：MySQL / SQLite (GORM)
- **认证**：JWT
- **存储**：多种云存储支持

### 前端
- **框架**：Vue 3 + TypeScript
- **构建工具**：Vite
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **样式**：CSS3 + Bootstrap Icons

## 🚀 快速开始

### 环境要求

- Go 1.21+
- Node.js 16+
- MySQL 8.0+ 或 SQLite 3
- Git

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd cloudbed
```

2. **后端设置**
```bash
# 安装Go依赖
go mod download

# 复制配置文件
cp config/config.json.example config/config.json

# 编辑配置文件
vim config/config.json
```

3. **数据库设置**
```bash
# MySQL
mysql -u root -p
CREATE DATABASE cloudbed CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 或使用SQLite（修改config.json中的数据库类型为sqlite）
```

4. **前端设置**
```bash
cd web
npm install
npm run build
cd ../..
```

5. **启动服务**
```bash
# 开发模式
go run server.go

# 或构建后运行
go build -o cloudbed cmd/server/main.go
./cloudbed
```

6. **访问应用**
```
http://localhost:18080
```

### 默认管理员账户

首次启动时会自动创建管理员账户：
- 邮箱：<EMAIL>
- 密码：admin123

**⚠️ 请立即修改默认密码！**

## 📁 项目结构

```
cloudbed/
├── cmd/                # 主应用程序
├── internal/           # 私有应用代码
│   ├── api/           # API层
│   ├── core/          # 业务逻辑
│   ├── config/        # 配置
│   └── database/      # 数据库
├── pkg/               # 公共库代码
├── web/               # Vue.js前端
├── storage/           # 应用数据
└── bin/               # 编译产物
```

## ⚙️ 配置说明

### 数据库配置
```json
{
  "database": {
    "type": "mysql",
    "mysql": {
      "host": "localhost",
      "port": "3306",
      "user": "root",
      "password": "your_password",
      "name": "cloudbed",
      "charset": "utf8mb4"
    }
  }
}
```

### 存储配置
支持多种存储后端：
- 本地存储
- 阿里云OSS
- 腾讯云COS
- 七牛云Kodo
- AWS S3
- FTP/SFTP

## 🔧 开发指南

### 后端开发
```bash
# 安装开发工具
go install github.com/cosmtrek/air@latest

# 热重载开发
air
```

### 前端开发
```bash
cd web

# 开发模式
npm run dev

# 构建生产版本
npm run build
```

### API文档
启动服务后访问：`http://localhost:18080/docs`

## 🧪 测试

```bash
# 运行后端测试
go test ./...

# 运行前端测试
cd web
npm run test
```

## 📦 部署

### Docker部署
```bash
# 构建镜像
docker build -t cloudbed .

# 运行容器
docker run -d -p 8080:8080 \
  -v ./storage:/app/storage \
  -v ./configs:/app/configs \
  cloudbed
```

### 传统部署
```bash
# 构建前端
cd web
npm run build
cd ..

# 构建后端
go build -o cloudbed cmd/server/main.go

# 运行
./cloudbed
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持

如果您遇到问题或有建议，请：
1. 查看 [常见问题](docs/FAQ.md)
2. 搜索现有的 [Issues](../../issues)
3. 创建新的 Issue

## 🗺️ 路线图

- [ ] 图片处理功能（缩略图、压缩）
- [ ] 标签系统
- [ ] 高级搜索
- [ ] 批量操作优化
- [ ] 移动端适配
- [ ] API限流
- [ ] 审计日志
- [ ] 数据备份恢复

## 📊 统计

![GitHub stars](https://img.shields.io/github/stars/username/image-backup)
![GitHub forks](https://img.shields.io/github/forks/username/image-backup)
![GitHub issues](https://img.shields.io/github/issues/username/image-backup)
![GitHub license](https://img.shields.io/github/license/username/image-backup)

---

⭐ 如果这个项目对您有帮助，请给它一个星标！
