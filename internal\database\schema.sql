-- Image Backup System Database Schema
-- MySQL Version

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `image_backup` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE `image_backup`;

-- 角色表
CREATE TABLE IF NOT EXISTS `roles` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
    `display_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `is_system` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_roles_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 权限表
CREATE TABLE IF NOT EXISTS `permissions` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
    `display_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `resource` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
    `action` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_permissions_name` (`name`),
    KEY `idx_permissions_resource_action` (`resource`, `action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS `role_permissions` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `role_id` bigint unsigned NOT NULL,
    `permission_id` bigint unsigned NOT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_role_permissions_unique` (`role_id`, `permission_id`),
    KEY `idx_role_permissions_role_id` (`role_id`),
    KEY `idx_role_permissions_permission_id` (`permission_id`),
    CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_role_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户表（增加角色支持、用户名和存储配额）
CREATE TABLE IF NOT EXISTS `users` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
    `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
    `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
    `role_id` bigint unsigned NOT NULL DEFAULT 3,
    `status` enum('active','inactive','suspended') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
    `last_login_at` datetime(3) DEFAULT NULL,
    `storage_quota` bigint NOT NULL DEFAULT 1073741824 COMMENT '存储配额（字节），默认1GB',
    `storage_used` bigint NOT NULL DEFAULT 0 COMMENT '已使用存储（字节）',
    `default_album_id` bigint unsigned DEFAULT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_users_username` (`username`),
    UNIQUE KEY `idx_users_email` (`email`),
    KEY `idx_users_role_id` (`role_id`),
    KEY `idx_users_status` (`status`),
    KEY `idx_users_storage_quota` (`storage_quota`),
    KEY `idx_users_storage_used` (`storage_used`),
    KEY `idx_users_default_album_id` (`default_album_id`),
    CONSTRAINT `fk_users_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户权限关联表（用于特殊权限分配）
CREATE TABLE IF NOT EXISTS `user_permissions` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint unsigned NOT NULL,
    `permission_id` bigint unsigned NOT NULL,
    `granted` tinyint(1) NOT NULL DEFAULT 1,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_user_permissions_unique` (`user_id`, `permission_id`),
    KEY `idx_user_permissions_user_id` (`user_id`),
    KEY `idx_user_permissions_permission_id` (`permission_id`),
    CONSTRAINT `fk_user_permissions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_user_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户组表
CREATE TABLE IF NOT EXISTS `groups` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
    `display_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `is_system` tinyint(1) NOT NULL DEFAULT 0,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认用户组',

    -- 上传限制配置
    `max_file_size` bigint NOT NULL DEFAULT 10240 COMMENT '最大文件大小(KB)',
    `concurrent_uploads` int NOT NULL DEFAULT 3 COMMENT '并发上传限制',
    `upload_limit_minute` int NOT NULL DEFAULT 10 COMMENT '每分钟上传限制',
    `upload_limit_hour` int NOT NULL DEFAULT 100 COMMENT '每小时上传限制',
    `upload_limit_day` int NOT NULL DEFAULT 500 COMMENT '每天上传限制',
    `upload_limit_week` int NOT NULL DEFAULT 2000 COMMENT '每周上传限制',
    `upload_limit_month` int NOT NULL DEFAULT 5000 COMMENT '每月上传限制',

    -- 文件命名和路径配置
    `path_naming_rule` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '{Y}/{m}/{d}' COMMENT '路径命名规则',
    `file_naming_rule` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '{uniqid}' COMMENT '文件命名规则',

    -- 图片处理配置
    `image_quality` int NOT NULL DEFAULT 85 COMMENT '图片保存质量(1-100)',
    `image_format` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'original' COMMENT '图片转换格式',
    `allowed_image_types` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'jpg,jpeg,png,gif,webp' COMMENT '允许的图片类型',

    `created_by` bigint unsigned DEFAULT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_groups_name` (`name`),
    KEY `idx_groups_is_active` (`is_active`),
    KEY `idx_groups_is_default` (`is_default`),
    KEY `idx_groups_created_by` (`created_by`),
    CONSTRAINT `fk_groups_creator` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户组权限关联表
CREATE TABLE IF NOT EXISTS `group_permissions` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `group_id` bigint unsigned NOT NULL,
    `permission_id` bigint unsigned NOT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_group_permissions_group_permission` (`group_id`, `permission_id`),
    KEY `idx_group_permissions_group_id` (`group_id`),
    KEY `idx_group_permissions_permission_id` (`permission_id`),
    CONSTRAINT `fk_group_permissions_group` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_group_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户组成员关联表
CREATE TABLE IF NOT EXISTS `user_groups` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint unsigned NOT NULL,
    `group_id` bigint unsigned NOT NULL,
    `joined_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3),
    `added_by` bigint unsigned DEFAULT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_user_groups_user_group` (`user_id`, `group_id`),
    KEY `idx_user_groups_user_id` (`user_id`),
    KEY `idx_user_groups_group_id` (`group_id`),
    KEY `idx_user_groups_added_by` (`added_by`),
    CONSTRAINT `fk_user_groups_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_user_groups_group` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_user_groups_adder` FOREIGN KEY (`added_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 相册表
CREATE TABLE IF NOT EXISTS `albums` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `cover_image` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `user_id` bigint unsigned NOT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_albums_user_id` (`user_id`),
    KEY `idx_albums_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 图片表（包含元数据字段）
CREATE TABLE IF NOT EXISTS `images` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
    `url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
    `size` bigint DEFAULT NULL,
    `width` int DEFAULT 0 COMMENT '图片宽度',
    `height` int DEFAULT 0 COMMENT '图片高度',
    `format` varchar(10) DEFAULT '' COMMENT '图片格式',
    `thumbnails` text COMMENT '缩略图路径信息(JSON格式)',
    `user_id` bigint unsigned NOT NULL,
    `album_id` bigint unsigned DEFAULT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_images_user_id` (`user_id`),
    KEY `idx_images_album_id` (`album_id`),
    KEY `idx_images_name` (`name`),
    KEY `idx_images_created_at` (`created_at`),
    KEY `idx_images_format` (`format`),
    KEY `idx_images_dimensions` (`width`, `height`),
    KEY `idx_images_size` (`size`),
    KEY `idx_images_user_album` (`user_id`, `album_id`),
    KEY `idx_images_user_created` (`user_id`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加外键约束
ALTER TABLE `users` 
ADD CONSTRAINT `fk_users_default_album` 
FOREIGN KEY (`default_album_id`) REFERENCES `albums` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `albums` 
ADD CONSTRAINT `fk_albums_user` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `images` 
ADD CONSTRAINT `fk_images_user` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `images` 
ADD CONSTRAINT `fk_images_album` 
FOREIGN KEY (`album_id`) REFERENCES `albums` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- 插入系统角色
INSERT IGNORE INTO `roles` (`id`, `name`, `display_name`, `description`, `is_system`, `created_at`, `updated_at`) VALUES
(1, 'super_admin', '超级管理员', '拥有系统所有权限的超级管理员', 1, NOW(), NOW()),
(2, 'admin', '管理员', '拥有大部分管理权限的管理员', 1, NOW(), NOW()),
(3, 'user', '普通用户', '普通用户，只能管理自己的内容', 1, NOW(), NOW()),
(4, 'guest', '访客', '只读权限的访客用户', 1, NOW(), NOW());

-- 插入系统权限
INSERT IGNORE INTO `permissions` (`name`, `display_name`, `description`, `resource`, `action`, `created_at`, `updated_at`) VALUES
-- 用户管理权限
('user.view', '查看用户', '查看用户列表和详情', 'user', 'view', NOW(), NOW()),
('user.create', '创建用户', '创建新用户账号', 'user', 'create', NOW(), NOW()),
('user.update', '编辑用户', '编辑用户信息', 'user', 'update', NOW(), NOW()),
('user.delete', '删除用户', '删除用户账号', 'user', 'delete', NOW(), NOW()),
('user.manage_role', '管理用户角色', '分配和修改用户角色', 'user', 'manage_role', NOW(), NOW()),

-- 角色管理权限
('role.view', '查看角色', '查看角色列表和详情', 'role', 'view', NOW(), NOW()),
('role.create', '创建角色', '创建新角色', 'role', 'create', NOW(), NOW()),
('role.update', '编辑角色', '编辑角色信息和权限', 'role', 'update', NOW(), NOW()),
('role.delete', '删除角色', '删除自定义角色', 'role', 'delete', NOW(), NOW()),

-- 权限管理权限
('permission.view', '查看权限', '查看权限列表', 'permission', 'view', NOW(), NOW()),
('permission.assign', '分配权限', '为角色分配权限', 'permission', 'assign', NOW(), NOW()),

-- 图片管理权限
('image.view_own', '查看自己的图片', '查看自己上传的图片', 'image', 'view_own', NOW(), NOW()),
('image.view_all', '查看所有图片', '查看所有用户的图片', 'image', 'view_all', NOW(), NOW()),
('image.upload', '上传图片', '上传新图片', 'image', 'upload', NOW(), NOW()),
('image.update_own', '编辑自己的图片', '编辑自己的图片信息', 'image', 'update_own', NOW(), NOW()),
('image.update_all', '编辑所有图片', '编辑任何用户的图片', 'image', 'update_all', NOW(), NOW()),
('image.delete_own', '删除自己的图片', '删除自己的图片', 'image', 'delete_own', NOW(), NOW()),
('image.delete_all', '删除所有图片', '删除任何用户的图片', 'image', 'delete_all', NOW(), NOW()),

-- 相册管理权限
('album.view_own', '查看自己的相册', '查看自己创建的相册', 'album', 'view_own', NOW(), NOW()),
('album.view_all', '查看所有相册', '查看所有用户的相册', 'album', 'view_all', NOW(), NOW()),
('album.create', '创建相册', '创建新相册', 'album', 'create', NOW(), NOW()),
('album.update_own', '编辑自己的相册', '编辑自己的相册', 'album', 'update_own', NOW(), NOW()),
('album.update_all', '编辑所有相册', '编辑任何用户的相册', 'album', 'update_all', NOW(), NOW()),
('album.delete_own', '删除自己的相册', '删除自己的相册', 'album', 'delete_own', NOW(), NOW()),
('album.delete_all', '删除所有相册', '删除任何用户的相册', 'album', 'delete_all', NOW(), NOW()),

-- 系统管理权限
('system.view_logs', '查看系统日志', '查看系统操作日志', 'system', 'view_logs', NOW(), NOW()),
('system.manage_settings', '管理系统设置', '修改系统配置', 'system', 'manage_settings', NOW(), NOW()),
('system.backup', '系统备份', '执行系统备份操作', 'system', 'backup', NOW(), NOW()),

-- 用户组管理权限
('group.view', '查看用户组', '查看用户组列表和详情', 'group', 'view', NOW(), NOW()),
('group.create', '创建用户组', '创建新用户组', 'group', 'create', NOW(), NOW()),
('group.update', '编辑用户组', '编辑用户组信息', 'group', 'update', NOW(), NOW()),
('group.delete', '删除用户组', '删除用户组', 'group', 'delete', NOW(), NOW()),
('group.manage_members', '管理组成员', '添加和移除用户组成员', 'group', 'manage_members', NOW(), NOW()),
('group.manage_permissions', '管理组权限', '分配和修改用户组权限', 'group', 'manage_permissions', NOW(), NOW());

-- 为超级管理员分配所有权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT 1, p.id, NOW() FROM `permissions` p;

-- 为管理员分配管理权限（除了超级管理员专有权限）
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT 2, p.id, NOW() FROM `permissions` p
WHERE p.name IN (
    'user.view', 'user.create', 'user.update', 'user.manage_role',
    'role.view', 'image.view_all', 'image.update_all', 'image.delete_all',
    'album.view_all', 'album.update_all', 'album.delete_all',
    'group.view', 'group.create', 'group.update', 'group.delete',
    'group.manage_members', 'group.manage_permissions',
    'system.view_logs'
);

-- 为普通用户分配基本权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT 3, p.id, NOW() FROM `permissions` p
WHERE p.name IN (
    'image.view_own', 'image.upload', 'image.update_own', 'image.delete_own',
    'album.view_own', 'album.create', 'album.update_own', 'album.delete_own'
);

-- 为访客分配只读权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`, `created_at`)
SELECT 4, p.id, NOW() FROM `permissions` p
WHERE p.name IN ('image.view_own', 'album.view_own');

-- 存储配置表
CREATE TABLE IF NOT EXISTS `storage_configs` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名称',
    `provider` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储提供商',
    `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为默认配置',
    `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
    `access_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '访问域名',
    `storage_path` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '存储路径',
    `config` text COLLATE utf8mb4_unicode_ci COMMENT 'JSON配置信息',
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_storage_configs_provider` (`provider`),
    KEY `idx_storage_configs_is_default` (`is_default`),
    KEY `idx_storage_configs_is_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 标签表
CREATE TABLE IF NOT EXISTS `tags` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL COMMENT '标签名称',
    `color` varchar(7) NOT NULL DEFAULT '#007bff' COMMENT '标签颜色',
    `description` varchar(255) DEFAULT '' COMMENT '标签描述',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
    `usage_count` int NOT NULL DEFAULT 0 COMMENT '使用次数',
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_user_tag_name` (`user_id`, `name`),
    KEY `idx_tags_user_id` (`user_id`),
    KEY `idx_tags_usage_count` (`usage_count`),
    KEY `idx_tags_name` (`name`),
    KEY `idx_tags_user_usage` (`user_id`, `usage_count` DESC),
    CONSTRAINT `fk_tags_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';

-- 图片标签关联表
CREATE TABLE IF NOT EXISTS `image_tags` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `image_id` bigint unsigned NOT NULL COMMENT '图片ID',
    `tag_id` bigint unsigned NOT NULL COMMENT '标签ID',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID（冗余字段）',
    `created_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_image_tag` (`image_id`, `tag_id`),
    KEY `idx_image_tags_image_id` (`image_id`),
    KEY `idx_image_tags_tag_id` (`tag_id`),
    KEY `idx_image_tags_user_id` (`user_id`),
    KEY `idx_image_tags_user_image` (`user_id`, `image_id`),
    KEY `idx_image_tags_user_tag` (`user_id`, `tag_id`),
    CONSTRAINT `fk_image_tags_image` FOREIGN KEY (`image_id`) REFERENCES `images` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_image_tags_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_image_tags_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片标签关联表';

-- 标签分类表
CREATE TABLE IF NOT EXISTS `tag_categories` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL COMMENT '分类名称',
    `color` varchar(7) NOT NULL DEFAULT '#6c757d' COMMENT '分类颜色',
    `description` varchar(255) DEFAULT '' COMMENT '分类描述',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
    `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_tag_categories_user_id` (`user_id`),
    KEY `idx_tag_categories_sort_order` (`sort_order`),
    CONSTRAINT `fk_tag_categories_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签分类表';

-- 标签分类关联表
CREATE TABLE IF NOT EXISTS `tag_category_relations` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `tag_id` bigint unsigned NOT NULL COMMENT '标签ID',
    `category_id` bigint unsigned NOT NULL COMMENT '分类ID',
    `created_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tag_category` (`tag_id`, `category_id`),
    KEY `idx_tag_category_relations_tag_id` (`tag_id`),
    KEY `idx_tag_category_relations_category_id` (`category_id`),
    CONSTRAINT `fk_tag_category_relations_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_tag_category_relations_category` FOREIGN KEY (`category_id`) REFERENCES `tag_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签分类关联表';

-- 分享表
CREATE TABLE IF NOT EXISTS `shares` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `share_token` varchar(64) UNIQUE NOT NULL COMMENT '分享令牌',
    `share_type` varchar(20) NOT NULL COMMENT '分享类型：image, album',
    `resource_id` bigint unsigned NOT NULL COMMENT '资源ID',
    `owner_id` bigint unsigned NOT NULL COMMENT '分享者ID',

    -- 分享设置
    `title` varchar(200) COMMENT '分享标题',
    `description` text COMMENT '分享描述',
    `password` varchar(100) COMMENT '访问密码（加密存储）',
    `has_password` boolean DEFAULT FALSE COMMENT '是否有密码',

    -- 权限设置
    `permissions` json COMMENT '权限列表',
    `allow_guest` boolean DEFAULT TRUE COMMENT '允许游客访问',

    -- 时效设置
    `expires_at` timestamp NULL COMMENT '过期时间',
    `max_views` int DEFAULT 0 COMMENT '最大访问次数（0为无限制）',
    `view_count` int DEFAULT 0 COMMENT '当前访问次数',

    -- 状态
    `status` varchar(20) DEFAULT 'active' COMMENT '分享状态：active, expired, disabled, revoked',
    `is_public` boolean DEFAULT FALSE COMMENT '是否公开（搜索引擎可索引）',

    -- 统计信息
    `download_count` int DEFAULT 0 COMMENT '下载次数',
    `comment_count` int DEFAULT 0 COMMENT '评论数量',
    `like_count` int DEFAULT 0 COMMENT '点赞数量',

    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (`id`),
    KEY `idx_share_token` (`share_token`),
    KEY `idx_owner_id` (`owner_id`),
    KEY `idx_resource` (`share_type`, `resource_id`),
    KEY `idx_status` (`status`),
    KEY `idx_expires_at` (`expires_at`),
    KEY `idx_shares_owner_status` (`owner_id`, `status`),

    CONSTRAINT `fk_shares_owner` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享表';

-- 分享访问记录表
CREATE TABLE IF NOT EXISTS `share_access` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `share_id` bigint unsigned NOT NULL COMMENT '分享ID',
    `user_id` bigint unsigned NULL COMMENT '访问用户ID（可为空，游客访问）',

    -- 访问信息
    `ip_address` varchar(45) COMMENT 'IP地址',
    `user_agent` text COMMENT '用户代理',
    `referer` varchar(500) COMMENT '来源页面',
    `country` varchar(50) COMMENT '国家',
    `city` varchar(100) COMMENT '城市',

    -- 访问行为
    `action` varchar(50) COMMENT '操作类型：view, download, comment',
    `duration` int DEFAULT 0 COMMENT '访问时长（秒）',

    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (`id`),
    KEY `idx_share_id` (`share_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_ip_address` (`ip_address`),
    KEY `idx_action` (`action`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_share_access_date` (`share_id`, DATE(`created_at`)),

    CONSTRAINT `fk_share_access_share` FOREIGN KEY (`share_id`) REFERENCES `shares` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_share_access_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享访问记录表';

-- 分享协作者表
CREATE TABLE IF NOT EXISTS `share_collaborators` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `share_id` bigint unsigned NOT NULL COMMENT '分享ID',
    `user_id` bigint unsigned NOT NULL COMMENT '协作者ID',

    -- 权限设置
    `permissions` json COMMENT '权限列表',
    `role` varchar(50) DEFAULT 'viewer' COMMENT '角色：viewer, editor, admin',

    -- 邀请信息
    `invited_by` bigint unsigned NOT NULL COMMENT '邀请者ID',
    `invite_token` varchar(64) COMMENT '邀请令牌',
    `accepted_at` timestamp NULL COMMENT '接受时间',

    -- 状态
    `status` varchar(20) DEFAULT 'pending' COMMENT '状态：pending, accepted, declined, removed',

    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_share_user` (`share_id`, `user_id`),
    KEY `idx_invite_token` (`invite_token`),
    KEY `idx_invited_by` (`invited_by`),
    KEY `idx_status` (`status`),

    CONSTRAINT `fk_share_collaborators_share` FOREIGN KEY (`share_id`) REFERENCES `shares` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_share_collaborators_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_share_collaborators_inviter` FOREIGN KEY (`invited_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享协作者表';

-- 评论表
CREATE TABLE IF NOT EXISTS `comments` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `share_id` bigint unsigned NOT NULL COMMENT '分享ID',
    `user_id` bigint unsigned NULL COMMENT '评论用户ID（可为空，游客评论）',

    -- 评论内容
    `content` text NOT NULL COMMENT '评论内容',
    `author_name` varchar(100) COMMENT '作者名称（游客评论时使用）',
    `author_email` varchar(200) COMMENT '作者邮箱（游客评论时使用）',

    -- 回复关系
    `parent_id` bigint unsigned NULL COMMENT '父评论ID',

    -- 位置信息（图片标注）
    `position_x` decimal(5,4) NULL COMMENT 'X坐标（相对位置 0-1）',
    `position_y` decimal(5,4) NULL COMMENT 'Y坐标（相对位置 0-1）',

    -- 状态
    `status` varchar(20) DEFAULT 'active' COMMENT '状态：active, hidden, deleted',
    `like_count` int DEFAULT 0 COMMENT '点赞数',

    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (`id`),
    KEY `idx_share_id` (`share_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_comments_share_parent` (`share_id`, `parent_id`, `status`),

    CONSTRAINT `fk_comments_share` FOREIGN KEY (`share_id`) REFERENCES `shares` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_comments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_comments_parent` FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';

-- 评论点赞表
CREATE TABLE IF NOT EXISTS `comment_likes` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `comment_id` bigint unsigned NOT NULL COMMENT '评论ID',
    `user_id` bigint unsigned NULL COMMENT '点赞用户ID',
    `ip_address` varchar(45) COMMENT 'IP地址（游客点赞）',

    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (`id`),
    KEY `idx_comment_id` (`comment_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_ip_address` (`ip_address`),

    CONSTRAINT `fk_comment_likes_comment` FOREIGN KEY (`comment_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_comment_likes_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论点赞表';

-- 分享点赞表
CREATE TABLE IF NOT EXISTS `share_likes` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `share_id` bigint unsigned NOT NULL COMMENT '分享ID',
    `user_id` bigint unsigned NULL COMMENT '点赞用户ID',
    `ip_address` varchar(45) COMMENT 'IP地址（游客点赞）',

    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (`id`),
    KEY `idx_share_id` (`share_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_ip_address` (`ip_address`),

    CONSTRAINT `fk_share_likes_share` FOREIGN KEY (`share_id`) REFERENCES `shares` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_share_likes_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享点赞表';

-- 插入示例数据（可选）
-- 创建默认超级管理员用户（密码：123456，已加密）
INSERT IGNORE INTO `users` (`username`, `email`, `password`, `role_id`, `status`, `created_at`, `updated_at`)
VALUES ('admin', '<EMAIL>', '$2a$08$RDPgNLCMZiBiOGQOhhKzguqtziCs5o.gPs6nkgmfMpCKzZZ0qgSZi', 1, 'active', NOW(), NOW());

-- 插入系统默认用户组
INSERT IGNORE INTO `groups` (`id`, `name`, `display_name`, `description`, `is_system`, `is_active`, `is_default`,
    `max_file_size`, `concurrent_uploads`, `upload_limit_minute`, `upload_limit_hour`, `upload_limit_day`,
    `upload_limit_week`, `upload_limit_month`, `path_naming_rule`, `file_naming_rule`, `image_quality`,
    `image_format`, `allowed_image_types`, `created_at`, `updated_at`) VALUES
(1, 'default_group', '系统默认组', '系统默认用户组，新用户自动加入此组，拥有基本的使用权限', 1, 1, 1,
    10240, 3, 10, 100, 500, 2000, 5000, '{Y}/{m}/{d}', '{uniqid}', 85, 'original', 'jpg,jpeg,png,gif,webp', NOW(), NOW());

-- 为系统默认组分配基本权限
INSERT IGNORE INTO `group_permissions` (`group_id`, `permission_id`, `created_at`)
SELECT 1, p.id, NOW() FROM `permissions` p
WHERE p.name IN (
    'image.view_own', 'image.upload', 'image.update_own', 'image.delete_own',
    'album.view_own', 'album.create', 'album.update_own', 'album.delete_own'
);

-- 将所有现有用户添加到系统默认组（如果有用户的话）
INSERT IGNORE INTO `user_groups` (`user_id`, `group_id`, `joined_at`, `added_by`, `created_at`, `updated_at`)
SELECT u.id, 1, NOW(), 1, NOW(), NOW() FROM `users` u;

-- 插入一些默认标签分类（可选）
INSERT IGNORE INTO `tag_categories` (`name`, `color`, `description`, `user_id`, `sort_order`, `created_at`, `updated_at`) VALUES
('通用', '#007bff', '通用标签分类', 1, 1, NOW(), NOW()),
('主题', '#28a745', '主题相关标签', 1, 2, NOW(), NOW()),
('地点', '#17a2b8', '地点相关标签', 1, 3, NOW(), NOW()),
('人物', '#ffc107', '人物相关标签', 1, 4, NOW(), NOW()),
('事件', '#dc3545', '事件相关标签', 1, 5, NOW(), NOW()),
('情感', '#6f42c1', '情感相关标签', 1, 6, NOW(), NOW());

-- 插入默认本地存储配置
INSERT IGNORE INTO `storage_configs` (`name`, `provider`, `is_default`, `is_enabled`, `access_url`, `storage_path`, `config`, `created_at`, `updated_at`)
VALUES (
    '默认本地存储',
    'local',
    1,
    1,
    'http://localhost:18080',
    './uploads',
    '{"upload_path":"./uploads","max_size":104857600}',
    NOW(),
    NOW()
);

-- 不创建默认相册，让用户根据需要自己创建

-- 创建视图：用户统计信息
CREATE OR REPLACE VIEW `user_stats` AS
SELECT
    u.id,
    u.email,
    COUNT(DISTINCT a.id) as album_count,
    COUNT(DISTINCT i.id) as image_count,
    COALESCE(SUM(i.size), 0) as total_size,
    u.created_at as user_created_at
FROM users u
LEFT JOIN albums a ON u.id = a.user_id
LEFT JOIN images i ON u.id = i.user_id
GROUP BY u.id, u.email, u.created_at;

-- 创建视图：相册统计信息
CREATE OR REPLACE VIEW `album_stats` AS
SELECT
    a.id,
    a.name,
    a.description,
    a.cover_image,
    a.user_id,
    u.email as user_email,
    COUNT(i.id) as image_count,
    COALESCE(SUM(i.size), 0) as total_size,
    a.created_at,
    a.updated_at
FROM albums a
LEFT JOIN images i ON a.id = i.album_id
LEFT JOIN users u ON a.user_id = u.id
GROUP BY a.id, a.name, a.description, a.cover_image, a.user_id, u.email, a.created_at, a.updated_at;

-- 创建视图：用户组统计信息（当前系统只有一个默认组）
CREATE OR REPLACE VIEW `group_stats` AS
SELECT
    g.id,
    g.name,
    g.display_name,
    g.description,
    g.is_system,
    g.is_active,
    g.is_default,
    g.max_file_size,
    g.concurrent_uploads,
    g.upload_limit_minute,
    g.upload_limit_hour,
    g.upload_limit_day,
    g.upload_limit_week,
    g.upload_limit_month,
    g.path_naming_rule,
    g.file_naming_rule,
    g.image_quality,
    g.image_format,
    g.allowed_image_types,
    COUNT(DISTINCT ug.user_id) as member_count,
    COUNT(DISTINCT gp.permission_id) as permission_count,
    g.created_at,
    g.updated_at
FROM groups g
LEFT JOIN user_groups ug ON g.id = ug.group_id
LEFT JOIN group_permissions gp ON g.id = gp.group_id
GROUP BY g.id, g.name, g.display_name, g.description, g.is_system, g.is_active, g.is_default,
         g.max_file_size, g.concurrent_uploads, g.upload_limit_minute, g.upload_limit_hour,
         g.upload_limit_day, g.upload_limit_week, g.upload_limit_month, g.path_naming_rule,
         g.file_naming_rule, g.image_quality, g.image_format, g.allowed_image_types,
         g.created_at, g.updated_at;

-- ========================================
-- 用户组系统说明
-- ========================================
-- 当前系统采用简化的用户组设计：
-- 1. 只有一个系统默认组（ID=1, name='default_group', display_name='系统默认组'）
-- 2. 所有新注册用户自动加入此组
-- 3. 该组拥有基本的图片和相册操作权限
-- 4. 管理员权限通过角色系统（roles）管理，不依赖用户组
-- 5. 用户组功能保留是为了未来扩展的灵活性
--
-- 用户组配置功能：
-- - 上传限制：文件大小、并发数量、时间段限制
-- - 文件命名：路径规则、文件名规则
-- - 图片处理：质量、格式转换、允许类型
-- - 系统组可以修改配置参数，但不能修改基本属性
-- ========================================

-- 创建分享统计视图
CREATE OR REPLACE VIEW `share_statistics` AS
SELECT
    s.id as share_id,
    s.share_token,
    s.title,
    s.view_count,
    s.download_count,
    s.comment_count,
    s.like_count,
    COUNT(DISTINCT sa.ip_address) as unique_visitors,
    COUNT(sa.id) as total_visits,
    MAX(sa.created_at) as last_visit,
    s.created_at as share_created_at
FROM shares s
LEFT JOIN share_access sa ON s.id = sa.share_id
WHERE s.status = 'active'
GROUP BY s.id, s.share_token, s.title, s.view_count, s.download_count, s.comment_count, s.like_count, s.created_at;

-- 创建标签使用统计视图
CREATE OR REPLACE VIEW `tag_usage_stats` AS
SELECT
    t.id,
    t.name,
    t.color,
    t.description,
    t.user_id,
    t.usage_count,
    COUNT(it.image_id) as actual_usage_count,
    t.created_at,
    t.updated_at
FROM tags t
LEFT JOIN image_tags it ON t.id = it.tag_id
GROUP BY t.id, t.name, t.color, t.description, t.user_id, t.usage_count, t.created_at, t.updated_at;

-- 创建存储过程：清理未使用的图片记录
DELIMITER //
CREATE PROCEDURE CleanupOrphanedImages()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE image_name VARCHAR(191);
    DECLARE cur CURSOR FOR 
        SELECT name FROM images 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
        AND album_id IS NULL;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO image_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 这里可以添加检查文件是否存在的逻辑
        -- 如果文件不存在，则删除数据库记录
        -- DELETE FROM images WHERE name = image_name;
        
    END LOOP;
    CLOSE cur;
END //
DELIMITER ;

-- 创建触发器：自动更新相册封面
DELIMITER //
CREATE TRIGGER update_album_cover_after_insert
AFTER INSERT ON images
FOR EACH ROW
BEGIN
    IF NEW.album_id IS NOT NULL THEN
        UPDATE albums 
        SET cover_image = NEW.url, updated_at = NOW()
        WHERE id = NEW.album_id AND cover_image IS NULL;
    END IF;
END //
DELIMITER ;

-- 创建触发器：删除图片时更新相册封面
DELIMITER //
CREATE TRIGGER update_album_cover_after_delete
AFTER DELETE ON images
FOR EACH ROW
BEGIN
    IF OLD.album_id IS NOT NULL THEN
        -- 如果删除的图片是封面图片，则选择该相册的第一张图片作为新封面
        UPDATE albums a
        SET cover_image = (
            SELECT url FROM images
            WHERE album_id = OLD.album_id
            ORDER BY created_at ASC
            LIMIT 1
        ), updated_at = NOW()
        WHERE a.id = OLD.album_id AND a.cover_image = OLD.url;
    END IF;
END //
DELIMITER ;

-- 创建触发器：更新标签使用次数
DELIMITER //
CREATE TRIGGER update_tag_usage_after_insert
AFTER INSERT ON image_tags
FOR EACH ROW
BEGIN
    UPDATE tags SET usage_count = usage_count + 1 WHERE id = NEW.tag_id;
END //
DELIMITER ;

CREATE TRIGGER update_tag_usage_after_delete
AFTER DELETE ON image_tags
FOR EACH ROW
BEGIN
    UPDATE tags SET usage_count = GREATEST(usage_count - 1, 0) WHERE id = OLD.tag_id;
END //
DELIMITER ;

-- 创建触发器：更新用户存储使用量
DELIMITER //
CREATE TRIGGER update_user_storage_after_insert
AFTER INSERT ON images
FOR EACH ROW
BEGIN
    UPDATE users SET storage_used = storage_used + COALESCE(NEW.size, 0) WHERE id = NEW.user_id;
END //
DELIMITER ;

CREATE TRIGGER update_user_storage_after_delete
AFTER DELETE ON images
FOR EACH ROW
BEGIN
    UPDATE users SET storage_used = GREATEST(storage_used - COALESCE(OLD.size, 0), 0) WHERE id = OLD.user_id;
END //
DELIMITER ;

-- 设置字符集和排序规则
ALTER DATABASE `image_backup` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 显示创建完成信息
SELECT 'Database schema created successfully!' as message;
