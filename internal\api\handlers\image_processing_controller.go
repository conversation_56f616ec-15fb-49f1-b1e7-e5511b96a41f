package controllers

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"
	imageprocessor "cloudbed/pkg/image"
	"cloudbed/pkg/response"

	"github.com/gin-gonic/gin"
)

// ProcessImageRequest 图片处理请求
type ProcessImageRequest struct {
	ImageID uint `json:"image_id" binding:"required"`

	// 格式转换
	OutputFormat string `json:"output_format"` // jpeg, png, webp, gif
	Quality      int    `json:"quality"`       // 1-100

	// 尺寸调整
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Mode   string `json:"mode"` // fit, fill, crop, stretch

	// 旋转和翻转
	Rotation int  `json:"rotation"` // 0, 90, 180, 270
	FlipH    bool `json:"flip_h"`   // 水平翻转
	FlipV    bool `json:"flip_v"`   // 垂直翻转

	// 裁剪
	CropX      int `json:"crop_x"`
	CropY      int `json:"crop_y"`
	CropWidth  int `json:"crop_width"`
	CropHeight int `json:"crop_height"`

	// 水印
	WatermarkText     string  `json:"watermark_text"`
	WatermarkPosition string  `json:"watermark_position"` // top-left, top-right, bottom-left, bottom-right, center
	WatermarkOpacity  float64 `json:"watermark_opacity"`  // 0.0-1.0
	WatermarkSize     int     `json:"watermark_size"`     // 字体大小

	// 压缩
	Compress        bool `json:"compress"`
	MaxFileSize     int  `json:"max_file_size"` // 最大文件大小（KB）
	ProgressiveJPEG bool `json:"progressive_jpeg"`
}

// ProcessImage 处理图片
func ProcessImage(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req ProcessImageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 验证图片权限
	image, err := dao.GetImageByIDAndUserID(req.ImageID, userID.(uint))
	if err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 构建处理选项
	options := imageprocessor.ProcessingOptions{
		OutputFormat:      req.OutputFormat,
		Quality:           req.Quality,
		Width:             req.Width,
		Height:            req.Height,
		Mode:              req.Mode,
		Rotation:          req.Rotation,
		FlipH:             req.FlipH,
		FlipV:             req.FlipV,
		CropX:             req.CropX,
		CropY:             req.CropY,
		CropWidth:         req.CropWidth,
		CropHeight:        req.CropHeight,
		WatermarkText:     req.WatermarkText,
		WatermarkPosition: req.WatermarkPosition,
		WatermarkOpacity:  req.WatermarkOpacity,
		WatermarkSize:     req.WatermarkSize,
		Compress:          req.Compress,
		MaxFileSize:       req.MaxFileSize,
		ProgressiveJPEG:   req.ProgressiveJPEG,
	}

	// 创建高级处理器
	processor := imageprocessor.NewAdvancedProcessor(imageprocessor.DefaultConfig())

	// 构建输入和输出路径
	inputPath := "./uploads/" + image.Name

	// 生成输出文件名
	timestamp := time.Now().Format("20060102_150405")
	ext := filepath.Ext(image.Name)
	nameWithoutExt := image.Name[:len(image.Name)-len(ext)]

	outputFormat := req.OutputFormat
	if outputFormat == "" {
		outputFormat = ext[1:] // 移除点号
	}

	outputName := fmt.Sprintf("%s_processed_%s.%s", nameWithoutExt, timestamp, outputFormat)
	outputPath := "./uploads/" + outputName

	// 处理图片
	result, err := processor.ProcessImage(inputPath, outputPath, options)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "图片处理失败", err.Error())
		return
	}

	// 创建新的图片记录
	processedImage := &models.Image{
		Name:    outputName,
		URL:     fmt.Sprintf("/uploads/%s", outputName),
		Size:    result.ProcessedSize,
		UserID:  userID.(uint),
		AlbumID: image.AlbumID,
	}

	// 设置处理后的图片尺寸
	if req.Width > 0 {
		processedImage.Width = req.Width
	}
	if req.Height > 0 {
		processedImage.Height = req.Height
	}

	if format, ok := result.Metadata["processed_format"]; ok {
		processedImage.Format = format
	}

	// 保存到数据库
	if err := dao.CreateImage(processedImage); err != nil {
		response.InternalServerErrorWithDetail(c, "保存处理后图片失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "图片处理成功", gin.H{
		"original_image":  image,
		"processed_image": processedImage,
		"processing_result": gin.H{
			"operations":        result.Operations,
			"compression_ratio": result.CompressionRatio,
			"original_size":     result.OriginalSize,
			"processed_size":    result.ProcessedSize,
			"metadata":          result.Metadata,
		},
	})
}

// GetImageEXIF 获取图片EXIF信息
func GetImageEXIF(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	imageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "图片ID格式错误")
		return
	}

	// 验证图片权限
	image, err := dao.GetImageByIDAndUserID(uint(imageID), userID.(uint))
	if err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 提取EXIF数据
	exifProcessor := imageprocessor.NewEXIFProcessor()
	imagePath := "./uploads/" + image.Name

	exifData, err := exifProcessor.ExtractEXIF(imagePath)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "提取EXIF数据失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"image":     image,
		"exif_data": exifData,
	})
}

// RemoveImageEXIF 移除图片EXIF信息
func RemoveImageEXIF(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	imageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "图片ID格式错误")
		return
	}

	// 验证图片权限
	image, err := dao.GetImageByIDAndUserID(uint(imageID), userID.(uint))
	if err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 移除EXIF数据
	exifProcessor := imageprocessor.NewEXIFProcessor()
	inputPath := "./uploads/" + image.Name

	// 生成输出文件名
	timestamp := time.Now().Format("20060102_150405")
	ext := filepath.Ext(image.Name)
	nameWithoutExt := image.Name[:len(image.Name)-len(ext)]
	outputName := fmt.Sprintf("%s_no_exif_%s%s", nameWithoutExt, timestamp, ext)
	outputPath := "./uploads/" + outputName

	if err := exifProcessor.RemoveEXIF(inputPath, outputPath); err != nil {
		response.InternalServerErrorWithDetail(c, "移除EXIF数据失败", err.Error())
		return
	}

	// 创建新的图片记录
	processedImage := &models.Image{
		Name:    outputName,
		URL:     fmt.Sprintf("/uploads/%s", outputName),
		Size:    0, // 将在下面获取
		Width:   image.Width,
		Height:  image.Height,
		Format:  image.Format,
		UserID:  userID.(uint),
		AlbumID: image.AlbumID,
	}

	// 获取处理后文件大小
	if info, err := os.Stat(outputPath); err == nil {
		processedImage.Size = info.Size()
	}

	// 保存到数据库
	if err := dao.CreateImage(processedImage); err != nil {
		response.InternalServerErrorWithDetail(c, "保存处理后图片失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "EXIF数据移除成功", gin.H{
		"original_image":  image,
		"processed_image": processedImage,
	})
}

// CorrectImageOrientation 校正图片方向
func CorrectImageOrientation(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	imageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "图片ID格式错误")
		return
	}

	// 验证图片权限
	image, err := dao.GetImageByIDAndUserID(uint(imageID), userID.(uint))
	if err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 校正图片方向
	exifProcessor := imageprocessor.NewEXIFProcessor()
	inputPath := "./uploads/" + image.Name

	// 生成输出文件名
	timestamp := time.Now().Format("20060102_150405")
	ext := filepath.Ext(image.Name)
	nameWithoutExt := image.Name[:len(image.Name)-len(ext)]
	outputName := fmt.Sprintf("%s_corrected_%s%s", nameWithoutExt, timestamp, ext)
	outputPath := "./uploads/" + outputName

	if err := exifProcessor.CorrectOrientation(inputPath, outputPath); err != nil {
		response.InternalServerErrorWithDetail(c, "校正图片方向失败", err.Error())
		return
	}

	// 创建新的图片记录
	processedImage := &models.Image{
		Name:    outputName,
		URL:     fmt.Sprintf("/uploads/%s", outputName),
		Size:    0, // 将在下面获取
		Width:   image.Width,
		Height:  image.Height,
		Format:  image.Format,
		UserID:  userID.(uint),
		AlbumID: image.AlbumID,
	}

	// 获取处理后文件大小
	if info, err := os.Stat(outputPath); err == nil {
		processedImage.Size = info.Size()
	}

	// 保存到数据库
	if err := dao.CreateImage(processedImage); err != nil {
		response.InternalServerErrorWithDetail(c, "保存处理后图片失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "图片方向校正成功", gin.H{
		"original_image":  image,
		"processed_image": processedImage,
	})
}

// GetProcessingPresets 获取预设的处理选项
func GetProcessingPresets(c *gin.Context) {
	presets := map[string]imageprocessor.ProcessingOptions{
		"thumbnail_small": {
			Width:   150,
			Height:  150,
			Mode:    "crop",
			Quality: 85,
		},
		"thumbnail_medium": {
			Width:   300,
			Height:  300,
			Mode:    "crop",
			Quality: 85,
		},
		"web_optimized": {
			Width:       1200,
			Height:      0, // 保持宽高比
			Mode:        "fit",
			Quality:     80,
			Compress:    true,
			MaxFileSize: 500, // 500KB
		},
		"print_quality": {
			Quality:      95,
			OutputFormat: "png",
		},
		"social_media": {
			Width:   1080,
			Height:  1080,
			Mode:    "crop",
			Quality: 85,
		},
		"compress_heavy": {
			Quality:     60,
			Compress:    true,
			MaxFileSize: 200, // 200KB
		},
	}

	response.Success(c, gin.H{
		"presets": presets,
	})
}
