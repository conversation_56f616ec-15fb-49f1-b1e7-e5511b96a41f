package graphql

import (
	"context"
	"fmt"
	"strconv"

	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"

	"github.com/graphql-go/graphql"
)

// GraphQLSchema GraphQL模式
type GraphQLSchema struct {
	Schema graphql.Schema
}

// NewGraphQLSchema 创建GraphQL模式
func NewGraphQLSchema() (*GraphQLSchema, error) {
	// 定义类型
	userType := graphql.NewObject(graphql.ObjectConfig{
		Name: "User",
		Fields: graphql.Fields{
			"id": &graphql.Field{
				Type: graphql.NewNonNull(graphql.ID),
			},
			"username": &graphql.Field{
				Type: graphql.NewNonNull(graphql.String),
			},
			"email": &graphql.Field{
				Type: graphql.NewNonNull(graphql.String),
			},
			"nickname": &graphql.Field{
				Type: graphql.String,
			},
			"avatar": &graphql.Field{
				Type: graphql.String,
			},
			"createdAt": &graphql.Field{
				Type: graphql.DateTime,
			},
			"updatedAt": &graphql.Field{
				Type: graphql.DateTime,
			},
		},
	})

	imageType := graphql.NewObject(graphql.ObjectConfig{
		Name: "Image",
		Fields: graphql.Fields{
			"id": &graphql.Field{
				Type: graphql.NewNonNull(graphql.ID),
			},
			"name": &graphql.Field{
				Type: graphql.NewNonNull(graphql.String),
			},
			"url": &graphql.Field{
				Type: graphql.NewNonNull(graphql.String),
			},
			"size": &graphql.Field{
				Type: graphql.Int,
			},
			"width": &graphql.Field{
				Type: graphql.Int,
			},
			"height": &graphql.Field{
				Type: graphql.Int,
			},
			"format": &graphql.Field{
				Type: graphql.String,
			},
			"description": &graphql.Field{
				Type: graphql.String,
			},
			"user": &graphql.Field{
				Type: userType,
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					if image, ok := p.Source.(*models.Image); ok {
						return dao.GetUserByID(image.UserID)
					}
					return nil, nil
				},
			},
			"album": &graphql.Field{
				Type: graphql.NewObject(graphql.ObjectConfig{
					Name: "Album",
					Fields: graphql.Fields{
						"id": &graphql.Field{Type: graphql.ID},
						"name": &graphql.Field{Type: graphql.String},
						"description": &graphql.Field{Type: graphql.String},
					},
				}),
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					if image, ok := p.Source.(*models.Image); ok && image.AlbumID != nil {
						return dao.GetAlbumByID(*image.AlbumID)
					}
					return nil, nil
				},
			},
			"tags": &graphql.Field{
				Type: graphql.NewList(graphql.NewObject(graphql.ObjectConfig{
					Name: "Tag",
					Fields: graphql.Fields{
						"id": &graphql.Field{Type: graphql.ID},
						"name": &graphql.Field{Type: graphql.String},
						"color": &graphql.Field{Type: graphql.String},
					},
				})),
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					if image, ok := p.Source.(*models.Image); ok {
						return dao.GetTagsByImageID(image.ID)
					}
					return nil, nil
				},
			},
			"createdAt": &graphql.Field{
				Type: graphql.DateTime,
			},
			"updatedAt": &graphql.Field{
				Type: graphql.DateTime,
			},
		},
	})

	albumType := graphql.NewObject(graphql.ObjectConfig{
		Name: "Album",
		Fields: graphql.Fields{
			"id": &graphql.Field{
				Type: graphql.NewNonNull(graphql.ID),
			},
			"name": &graphql.Field{
				Type: graphql.NewNonNull(graphql.String),
			},
			"description": &graphql.Field{
				Type: graphql.String,
			},
			"coverImage": &graphql.Field{
				Type: graphql.String,
			},
			"imageCount": &graphql.Field{
				Type: graphql.Int,
			},
			"user": &graphql.Field{
				Type: userType,
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					if album, ok := p.Source.(*models.Album); ok {
						return dao.GetUserByID(album.UserID)
					}
					return nil, nil
				},
			},
			"images": &graphql.Field{
				Type: graphql.NewList(imageType),
				Args: graphql.FieldConfigArgument{
					"first": &graphql.ArgumentConfig{
						Type:         graphql.Int,
						DefaultValue: 10,
					},
					"offset": &graphql.ArgumentConfig{
						Type:         graphql.Int,
						DefaultValue: 0,
					},
				},
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					if album, ok := p.Source.(*models.Album); ok {
						first := p.Args["first"].(int)
						offset := p.Args["offset"].(int)
						page := (offset / first) + 1
						images, _, err := dao.GetImagesByAlbumID(album.ID, page, first)
						return images, err
					}
					return nil, nil
				},
			},
			"createdAt": &graphql.Field{
				Type: graphql.DateTime,
			},
			"updatedAt": &graphql.Field{
				Type: graphql.DateTime,
			},
		},
	})

	shareType := graphql.NewObject(graphql.ObjectConfig{
		Name: "Share",
		Fields: graphql.Fields{
			"id": &graphql.Field{
				Type: graphql.NewNonNull(graphql.ID),
			},
			"shareToken": &graphql.Field{
				Type: graphql.NewNonNull(graphql.String),
			},
			"shareType": &graphql.Field{
				Type: graphql.NewNonNull(graphql.String),
			},
			"title": &graphql.Field{
				Type: graphql.String,
			},
			"description": &graphql.Field{
				Type: graphql.String,
			},
			"permissions": &graphql.Field{
				Type: graphql.NewList(graphql.String),
			},
			"viewCount": &graphql.Field{
				Type: graphql.Int,
			},
			"downloadCount": &graphql.Field{
				Type: graphql.Int,
			},
			"commentCount": &graphql.Field{
				Type: graphql.Int,
			},
			"likeCount": &graphql.Field{
				Type: graphql.Int,
			},
			"status": &graphql.Field{
				Type: graphql.String,
			},
			"expiresAt": &graphql.Field{
				Type: graphql.DateTime,
			},
			"createdAt": &graphql.Field{
				Type: graphql.DateTime,
			},
		},
	})

	// 定义查询
	queryType := graphql.NewObject(graphql.ObjectConfig{
		Name: "Query",
		Fields: graphql.Fields{
			"user": &graphql.Field{
				Type: userType,
				Args: graphql.FieldConfigArgument{
					"id": &graphql.ArgumentConfig{
						Type: graphql.NewNonNull(graphql.ID),
					},
				},
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					id, err := strconv.ParseUint(p.Args["id"].(string), 10, 32)
					if err != nil {
						return nil, err
					}
					return dao.GetUserByID(uint(id))
				},
			},
			"me": &graphql.Field{
				Type: userType,
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					userID := getUserIDFromContext(p.Context)
					if userID == 0 {
						return nil, fmt.Errorf("unauthorized")
					}
					return dao.GetUserByID(userID)
				},
			},
			"image": &graphql.Field{
				Type: imageType,
				Args: graphql.FieldConfigArgument{
					"id": &graphql.ArgumentConfig{
						Type: graphql.NewNonNull(graphql.ID),
					},
				},
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					id, err := strconv.ParseUint(p.Args["id"].(string), 10, 32)
					if err != nil {
						return nil, err
					}
					return dao.GetImageByID(uint(id))
				},
			},
			"images": &graphql.Field{
				Type: graphql.NewList(imageType),
				Args: graphql.FieldConfigArgument{
					"first": &graphql.ArgumentConfig{
						Type:         graphql.Int,
						DefaultValue: 10,
					},
					"offset": &graphql.ArgumentConfig{
						Type:         graphql.Int,
						DefaultValue: 0,
					},
					"albumId": &graphql.ArgumentConfig{
						Type: graphql.ID,
					},
					"tagId": &graphql.ArgumentConfig{
						Type: graphql.ID,
					},
				},
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					userID := getUserIDFromContext(p.Context)
					if userID == 0 {
						return nil, fmt.Errorf("unauthorized")
					}

					first := p.Args["first"].(int)
					offset := p.Args["offset"].(int)
					page := (offset / first) + 1

					if albumID, ok := p.Args["albumId"]; ok && albumID != nil {
						id, err := strconv.ParseUint(albumID.(string), 10, 32)
						if err != nil {
							return nil, err
						}
						images, _, err := dao.GetImagesByAlbumID(uint(id), page, first)
						return images, err
					}

					if tagID, ok := p.Args["tagId"]; ok && tagID != nil {
						id, err := strconv.ParseUint(tagID.(string), 10, 32)
						if err != nil {
							return nil, err
						}
						images, _, err := dao.GetImagesByTagID(uint(id), page, first)
						return images, err
					}

					images, _, err := dao.GetImagesByUserID(userID, page, first)
					return images, err
				},
			},
			"album": &graphql.Field{
				Type: albumType,
				Args: graphql.FieldConfigArgument{
					"id": &graphql.ArgumentConfig{
						Type: graphql.NewNonNull(graphql.ID),
					},
				},
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					id, err := strconv.ParseUint(p.Args["id"].(string), 10, 32)
					if err != nil {
						return nil, err
					}
					return dao.GetAlbumByID(uint(id))
				},
			},
			"albums": &graphql.Field{
				Type: graphql.NewList(albumType),
				Args: graphql.FieldConfigArgument{
					"first": &graphql.ArgumentConfig{
						Type:         graphql.Int,
						DefaultValue: 10,
					},
					"offset": &graphql.ArgumentConfig{
						Type:         graphql.Int,
						DefaultValue: 0,
					},
				},
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					userID := getUserIDFromContext(p.Context)
					if userID == 0 {
						return nil, fmt.Errorf("unauthorized")
					}

					first := p.Args["first"].(int)
					offset := p.Args["offset"].(int)
					page := (offset / first) + 1

					albums, _, err := dao.GetAlbumsByUserID(userID, page, first)
					return albums, err
				},
			},
			"shares": &graphql.Field{
				Type: graphql.NewList(shareType),
				Args: graphql.FieldConfigArgument{
					"first": &graphql.ArgumentConfig{
						Type:         graphql.Int,
						DefaultValue: 10,
					},
					"offset": &graphql.ArgumentConfig{
						Type:         graphql.Int,
						DefaultValue: 0,
					},
				},
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					userID := getUserIDFromContext(p.Context)
					if userID == 0 {
						return nil, fmt.Errorf("unauthorized")
					}

					first := p.Args["first"].(int)
					offset := p.Args["offset"].(int)
					page := (offset / first) + 1

					shares, _, err := dao.GetSharesByOwner(userID, page, first)
					return shares, err
				},
			},
		},
	})

	// 定义变更
	mutationType := graphql.NewObject(graphql.ObjectConfig{
		Name: "Mutation",
		Fields: graphql.Fields{
			"createAlbum": &graphql.Field{
				Type: albumType,
				Args: graphql.FieldConfigArgument{
					"input": &graphql.ArgumentConfig{
						Type: graphql.NewInputObject(graphql.InputObjectConfig{
							Name: "CreateAlbumInput",
							Fields: graphql.InputObjectConfigFieldMap{
								"name": &graphql.InputObjectFieldConfig{
									Type: graphql.NewNonNull(graphql.String),
								},
								"description": &graphql.InputObjectFieldConfig{
									Type: graphql.String,
								},
							},
						}),
					},
				},
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					userID := getUserIDFromContext(p.Context)
					if userID == 0 {
						return nil, fmt.Errorf("unauthorized")
					}

					input := p.Args["input"].(map[string]interface{})
					album := &models.Album{
						Name:        input["name"].(string),
						Description: getStringFromMap(input, "description"),
						UserID:      userID,
					}

					err := dao.CreateAlbum(album)
					return album, err
				},
			},
			"updateAlbum": &graphql.Field{
				Type: albumType,
				Args: graphql.FieldConfigArgument{
					"id": &graphql.ArgumentConfig{
						Type: graphql.NewNonNull(graphql.ID),
					},
					"input": &graphql.ArgumentConfig{
						Type: graphql.NewInputObject(graphql.InputObjectConfig{
							Name: "UpdateAlbumInput",
							Fields: graphql.InputObjectConfigFieldMap{
								"name": &graphql.InputObjectFieldConfig{
									Type: graphql.String,
								},
								"description": &graphql.InputObjectFieldConfig{
									Type: graphql.String,
								},
							},
						}),
					},
				},
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					userID := getUserIDFromContext(p.Context)
					if userID == 0 {
						return nil, fmt.Errorf("unauthorized")
					}

					id, err := strconv.ParseUint(p.Args["id"].(string), 10, 32)
					if err != nil {
						return nil, err
					}

					album, err := dao.GetAlbumByIDAndUserID(uint(id), userID)
					if err != nil {
						return nil, err
					}

					input := p.Args["input"].(map[string]interface{})
					if name, ok := input["name"]; ok {
						album.Name = name.(string)
					}
					if description, ok := input["description"]; ok {
						album.Description = description.(string)
					}

					err = dao.UpdateAlbum(album)
					return album, err
				},
			},
			"deleteAlbum": &graphql.Field{
				Type: graphql.Boolean,
				Args: graphql.FieldConfigArgument{
					"id": &graphql.ArgumentConfig{
						Type: graphql.NewNonNull(graphql.ID),
					},
				},
				Resolve: func(p graphql.ResolveParams) (interface{}, error) {
					userID := getUserIDFromContext(p.Context)
					if userID == 0 {
						return false, fmt.Errorf("unauthorized")
					}

					id, err := strconv.ParseUint(p.Args["id"].(string), 10, 32)
					if err != nil {
						return false, err
					}

					// 验证权限
					_, err = dao.GetAlbumByIDAndUserID(uint(id), userID)
					if err != nil {
						return false, err
					}

					err = dao.DeleteAlbum(uint(id))
					return err == nil, err
				},
			},
		},
	})

	// 创建模式
	schema, err := graphql.NewSchema(graphql.SchemaConfig{
		Query:    queryType,
		Mutation: mutationType,
	})

	if err != nil {
		return nil, err
	}

	return &GraphQLSchema{Schema: schema}, nil
}

// 辅助函数
func getUserIDFromContext(ctx context.Context) uint {
	if userID, ok := ctx.Value("user_id").(uint); ok {
		return userID
	}
	return 0
}

func getStringFromMap(m map[string]interface{}, key string) string {
	if value, ok := m[key]; ok && value != nil {
		return value.(string)
	}
	return ""
}
