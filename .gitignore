# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build artifacts
bin/
dist/

# Configuration files with sensitive data
configs/config.json
configs/database.yaml
.env
.env.local
.env.production

# Storage directories
storage/uploads/*
storage/thumbnails/*
storage/logs/*
storage/temp/*

# Keep directory structure but ignore contents
!storage/uploads/.gitkeep
!storage/thumbnails/.gitkeep
!storage/logs/.gitkeep
!storage/temp/.gitkeep

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# Frontend
node_modules/
web/dist/
web/node_modules/
*.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.backup
*-backup-*

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Test files
test_*.go
*_test_temp.go
debug.html
diagnose_*.go
quick_test.go
