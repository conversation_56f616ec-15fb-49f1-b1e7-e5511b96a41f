package main

import (
	"flag"
	"fmt"
	"log"

	"cloudbed/internal/config"
	"cloudbed/internal/database"
)

func main() {
	var (
		action = flag.String("action", "migrate", "Action to perform: migrate, check, reset")
		file   = flag.String("file", "", "Specific migration file to run")
	)
	flag.Parse()

	fmt.Println("🔧 Database Migration Tool")
	fmt.Println("==========================")

	// Load configuration
	cfg := config.LoadConfig()
	fmt.Printf("Database Type: %s\n", cfg.Database.Type)

	// Initialize database
	if err := database.InitDatabase(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer database.CloseDB()

	// Create migration manager
	migrator := database.NewMigrationManager(database.GetDB())

	switch *action {
	case "migrate":
		fmt.Println("\n📦 Running database migrations...")
		if *file != "" {
			if err := migrator.ApplyMigration(*file); err != nil {
				log.Fatalf("Migration failed: %v", err)
			}
			fmt.Printf("✅ Migration %s applied successfully\n", *file)
		} else {
			if err := migrator.QuickSetup(); err != nil {
				log.Printf("⚠️  Migration warnings: %v", err)
			}
			fmt.Println("✅ All migrations completed")
		}

	case "check":
		fmt.Println("\n🔍 Checking database status...")
		if err := migrator.CheckTables(); err != nil {
			log.Fatalf("Database check failed: %v", err)
		}
		fmt.Println("✅ Database check passed")

	case "reset":
		fmt.Println("\n⚠️  This will reset the database. Are you sure? (y/N)")
		var confirm string
		fmt.Scanln(&confirm)
		if confirm == "y" || confirm == "Y" {
			fmt.Println("🔄 Resetting database...")
			// Add reset logic here if needed
			fmt.Println("✅ Database reset completed")
		} else {
			fmt.Println("❌ Reset cancelled")
		}

	default:
		fmt.Printf("❌ Unknown action: %s\n", *action)
		fmt.Println("Available actions: migrate, check, reset")
	}

	fmt.Println("\n🎉 Migration tool completed!")
}
