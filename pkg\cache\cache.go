package cache

import (
	"context"
	"fmt"
	"log"
	"time"
)

// Cache 缓存接口
type Cache interface {
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string, dest interface{}) error
	Delete(ctx context.Context, keys ...string) error
	Exists(ctx context.Context, key string) (bool, error)
	Expire(ctx context.Context, key string, expiration time.Duration) error
	TTL(ctx context.Context, key string) (time.Duration, error)
	Increment(ctx context.Context, key string) (int64, error)
	Decrement(ctx context.Context, key string) (int64, error)
	FlushAll(ctx context.Context) error
}

// Manager 缓存管理器
type Manager struct {
	cache Cache
}

// Config 缓存配置
type Config struct {
	Type   string       `json:"type"`   // redis 或 memory
	Redis  CacheConfig  `json:"redis"`  // Redis配置
	Memory MemoryConfig `json:"memory"` // 内存缓存配置
}

// MemoryConfig 内存缓存配置
type MemoryConfig struct {
	Prefix string `json:"prefix"`
}

var globalManager *Manager

// InitCache 初始化缓存
func InitCache(config Config) error {
	var cache Cache

	switch config.Type {
	case "redis":
		cache = NewRedisCache(config.Redis)
		if cache == nil {
			log.Println("Redis cache initialization failed, falling back to memory cache")
			cache = NewMemoryCache(config.Memory.Prefix)
		}
	case "memory":
		cache = NewMemoryCache(config.Memory.Prefix)
	default:
		return fmt.Errorf("unsupported cache type: %s", config.Type)
	}

	globalManager = &Manager{cache: cache}
	log.Printf("Cache initialized with type: %s", config.Type)
	return nil
}

// GetManager 获取全局缓存管理器
func GetManager() *Manager {
	if globalManager == nil {
		// 如果没有初始化，使用默认内存缓存
		log.Println("Cache not initialized, using default memory cache")
		globalManager = &Manager{cache: NewMemoryCache("image-backup")}
	}
	return globalManager
}

// 缓存键常量
const (
	// 用户相关
	KeyUserInfo         = "user:info:%d"
	KeyUserStorageStats = "user:storage:%d"
	KeyUserImages       = "user:images:%d:page:%d"
	KeyUserAlbums       = "user:albums:%d:page:%d"

	// 图片相关
	KeyImageInfo     = "image:info:%d"
	KeyImagesByAlbum = "album:images:%d:page:%d"
	KeyImageStats    = "image:stats"

	// 相册相关
	KeyAlbumInfo  = "album:info:%d"
	KeyAlbumStats = "album:stats:%d"

	// 角色权限相关
	KeyRoleInfo        = "role:info:%d"
	KeyRolePermissions = "role:permissions:%d"
	KeyUserPermissions = "user:permissions:%d"

	// 系统统计
	KeySystemStats    = "system:stats"
	KeyStorageConfigs = "storage:configs"

	// 搜索结果
	KeySearchImages = "search:images:%s:page:%d"
	KeySearchAlbums = "search:albums:%s:page:%d"
)

// 缓存过期时间常量
const (
	TTLShort  = 5 * time.Minute    // 短期缓存
	TTLMedium = 30 * time.Minute   // 中期缓存
	TTLLong   = 2 * time.Hour      // 长期缓存
	TTLDaily  = 24 * time.Hour     // 日缓存
	TTLWeekly = 7 * 24 * time.Hour // 周缓存
)

// Set 设置缓存
func (m *Manager) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return m.cache.Set(ctx, key, value, expiration)
}

// Get 获取缓存
func (m *Manager) Get(ctx context.Context, key string, dest interface{}) error {
	return m.cache.Get(ctx, key, dest)
}

// Delete 删除缓存
func (m *Manager) Delete(ctx context.Context, keys ...string) error {
	return m.cache.Delete(ctx, keys...)
}

// Exists 检查键是否存在
func (m *Manager) Exists(ctx context.Context, key string) (bool, error) {
	return m.cache.Exists(ctx, key)
}

// GetOrSet 获取缓存，如果不存在则设置
func (m *Manager) GetOrSet(ctx context.Context, key string, dest interface{}, expiration time.Duration, setter func() (interface{}, error)) error {
	// 先尝试获取缓存
	err := m.cache.Get(ctx, key, dest)
	if err == nil {
		return nil // 缓存命中
	}

	// 缓存未命中，调用setter获取数据
	value, err := setter()
	if err != nil {
		return err
	}

	// 设置缓存
	if err := m.cache.Set(ctx, key, value, expiration); err != nil {
		log.Printf("Failed to set cache for key %s: %v", key, err)
	}

	// 将值复制到dest
	return m.cache.Get(ctx, key, dest)
}

// InvalidatePattern 根据模式删除缓存（简单实现）
func (m *Manager) InvalidatePattern(ctx context.Context, pattern string) error {
	// 这是一个简化实现，实际应用中可能需要更复杂的模式匹配
	// 对于Redis，可以使用SCAN命令
	log.Printf("Invalidating cache pattern: %s", pattern)
	return nil
}

// CacheUserInfo 缓存用户信息
func (m *Manager) CacheUserInfo(ctx context.Context, userID uint, userInfo interface{}) error {
	key := fmt.Sprintf(KeyUserInfo, userID)
	return m.cache.Set(ctx, key, userInfo, TTLMedium)
}

// GetUserInfo 获取用户信息缓存
func (m *Manager) GetUserInfo(ctx context.Context, userID uint, dest interface{}) error {
	key := fmt.Sprintf(KeyUserInfo, userID)
	return m.cache.Get(ctx, key, dest)
}

// InvalidateUserCache 清除用户相关缓存
func (m *Manager) InvalidateUserCache(ctx context.Context, userID uint) error {
	keys := []string{
		fmt.Sprintf(KeyUserInfo, userID),
		fmt.Sprintf(KeyUserStorageStats, userID),
		fmt.Sprintf(KeyUserPermissions, userID),
	}
	return m.cache.Delete(ctx, keys...)
}

// CacheImageList 缓存图片列表
func (m *Manager) CacheImageList(ctx context.Context, userID uint, page int, images interface{}) error {
	key := fmt.Sprintf(KeyUserImages, userID, page)
	return m.cache.Set(ctx, key, images, TTLShort)
}

// GetImageList 获取图片列表缓存
func (m *Manager) GetImageList(ctx context.Context, userID uint, page int, dest interface{}) error {
	key := fmt.Sprintf(KeyUserImages, userID, page)
	return m.cache.Get(ctx, key, dest)
}

// InvalidateImageCache 清除图片相关缓存
func (m *Manager) InvalidateImageCache(ctx context.Context, userID uint) error {
	// 这里应该删除所有相关的图片缓存
	// 简化实现，实际应用中需要更精确的缓存失效策略
	log.Printf("Invalidating image cache for user %d", userID)
	return nil
}

// CacheAlbumList 缓存相册列表
func (m *Manager) CacheAlbumList(ctx context.Context, userID uint, page int, albums interface{}) error {
	key := fmt.Sprintf(KeyUserAlbums, userID, page)
	return m.cache.Set(ctx, key, albums, TTLMedium)
}

// GetAlbumList 获取相册列表缓存
func (m *Manager) GetAlbumList(ctx context.Context, userID uint, page int, dest interface{}) error {
	key := fmt.Sprintf(KeyUserAlbums, userID, page)
	return m.cache.Get(ctx, key, dest)
}

// InvalidateAlbumCache 清除相册相关缓存
func (m *Manager) InvalidateAlbumCache(ctx context.Context, userID uint) error {
	log.Printf("Invalidating album cache for user %d", userID)
	return nil
}

// CacheSystemStats 缓存系统统计信息
func (m *Manager) CacheSystemStats(ctx context.Context, stats interface{}) error {
	return m.cache.Set(ctx, KeySystemStats, stats, TTLLong)
}

// GetSystemStats 获取系统统计信息缓存
func (m *Manager) GetSystemStats(ctx context.Context, dest interface{}) error {
	return m.cache.Get(ctx, KeySystemStats, dest)
}

// GetCacheStats 获取缓存统计信息
func (m *Manager) GetCacheStats() map[string]interface{} {
	stats := make(map[string]interface{})

	// 如果是内存缓存，可以获取更多统计信息
	if memCache, ok := m.cache.(*MemoryCache); ok {
		stats["type"] = "memory"
		stats["size"] = memCache.Size()
		stats["keys"] = len(memCache.Keys())
	} else {
		stats["type"] = "redis"
	}

	return stats
}
