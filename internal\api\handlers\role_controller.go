package controllers

import (
	"net/http"
	"strconv"

	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"

	"github.com/gin-gonic/gin"
)

// GetRoles 获取角色列表
func GetRoles(c *gin.Context) {
	roles, err := dao.GetRolesWithUserCount()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get roles: " + err.Error()})
		return
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{"roles": roles})
}

// GetRole 获取单个角色信息
func GetRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role ID"})
		return
	}

	role, err := dao.GetRoleByID(uint(id))
	if err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}

	// 获取用户数量
	userCount, _ := dao.CountUsersByRole(role.ID)

	// 转换权限格式
	var permissions []models.PermissionResponse
	for _, perm := range role.Permissions {
		permissions = append(permissions, models.PermissionResponse{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Description: perm.Description,
			Resource:    perm.Resource,
			Action:      perm.Action,
		})
	}

	response := models.RoleResponse{
		ID:          role.ID,
		Name:        role.Name,
		DisplayName: role.DisplayName,
		Description: role.Description,
		IsSystem:    role.IsSystem,
		Permissions: permissions,
		UserCount:   userCount,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// CreateRole 创建新角色
func CreateRole(c *gin.Context) {
	var req models.RoleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查角色名是否已存在
	if _, err := dao.GetRoleByName(req.Name); err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Role name already exists"})
		return
	}

	// 创建角色
	role := models.Role{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		IsSystem:    false, // 用户创建的角色不是系统角色
	}

	if err := dao.CreateRole(&role); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create role: " + err.Error()})
		return
	}

	// 分配权限
	if len(req.PermissionIDs) > 0 {
		if err := dao.AssignPermissionsToRole(role.ID, req.PermissionIDs); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign permissions: " + err.Error()})
			return
		}
	}

	// 获取创建后的角色信息
	createdRole, err := dao.GetRoleByID(role.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get created role"})
		return
	}

	// 转换权限格式
	var permissions []models.PermissionResponse
	for _, perm := range createdRole.Permissions {
		permissions = append(permissions, models.PermissionResponse{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Description: perm.Description,
			Resource:    perm.Resource,
			Action:      perm.Action,
		})
	}

	response := models.RoleResponse{
		ID:          createdRole.ID,
		Name:        createdRole.Name,
		DisplayName: createdRole.DisplayName,
		Description: createdRole.Description,
		IsSystem:    createdRole.IsSystem,
		Permissions: permissions,
		UserCount:   0,
		CreatedAt:   createdRole.CreatedAt,
		UpdatedAt:   createdRole.UpdatedAt,
	}

	c.JSON(http.StatusCreated, response)
}

// UpdateRole 更新角色信息
func UpdateRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role ID"})
		return
	}

	var req models.RoleUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取角色
	role, err := dao.GetRoleByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}

	// 检查是否为系统角色
	if role.IsSystem {
		c.JSON(http.StatusForbidden, gin.H{"error": "Cannot modify system role"})
		return
	}

	// 更新角色信息
	role.DisplayName = req.DisplayName
	role.Description = req.Description

	if err := dao.UpdateRole(role); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update role: " + err.Error()})
		return
	}

	// 更新权限
	if err := dao.AssignPermissionsToRole(role.ID, req.PermissionIDs); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update permissions: " + err.Error()})
		return
	}

	// 获取更新后的角色信息
	updatedRole, err := dao.GetRoleByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get updated role"})
		return
	}

	// 获取用户数量
	userCount, _ := dao.CountUsersByRole(updatedRole.ID)

	// 转换权限格式
	var permissions []models.PermissionResponse
	for _, perm := range updatedRole.Permissions {
		permissions = append(permissions, models.PermissionResponse{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Description: perm.Description,
			Resource:    perm.Resource,
			Action:      perm.Action,
		})
	}

	response := models.RoleResponse{
		ID:          updatedRole.ID,
		Name:        updatedRole.Name,
		DisplayName: updatedRole.DisplayName,
		Description: updatedRole.Description,
		IsSystem:    updatedRole.IsSystem,
		Permissions: permissions,
		UserCount:   userCount,
		CreatedAt:   updatedRole.CreatedAt,
		UpdatedAt:   updatedRole.UpdatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// DeleteRole 删除角色
func DeleteRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role ID"})
		return
	}

	if err := dao.DeleteRole(uint(id)); err != nil {
		if err.Error() == "system role cannot be deleted" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Cannot delete system role"})
			return
		}
		if err.Error() == "role is in use by users" {
			c.JSON(http.StatusConflict, gin.H{"error": "Cannot delete role that is in use"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete role: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Role deleted successfully"})
}

// GetPermissions 获取所有权限列表
func GetPermissions(c *gin.Context) {
	permissions, err := dao.GetAllPermissions()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get permissions: " + err.Error()})
		return
	}

	// 按资源分组
	permissionsByResource := make(map[string][]models.PermissionResponse)
	for _, perm := range permissions {
		permResp := models.PermissionResponse{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Description: perm.Description,
			Resource:    perm.Resource,
			Action:      perm.Action,
		}
		permissionsByResource[perm.Resource] = append(permissionsByResource[perm.Resource], permResp)
	}

	c.JSON(http.StatusOK, gin.H{"permissions": permissionsByResource})
}

// AssignUserRole 为用户分配角色
func AssignUserRole(c *gin.Context) {
	var req models.UserRoleAssignRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查用户是否存在
	if _, err := dao.GetUserByID(req.UserID); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 检查角色是否存在
	if _, err := dao.GetRoleByID(req.RoleID); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}

	if err := dao.AssignUserRole(req.UserID, req.RoleID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign role: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Role assigned successfully"})
}

// GrantUserPermission 为用户授予特殊权限
func GrantUserPermission(c *gin.Context) {
	var req models.UserPermissionAssignRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查用户是否存在
	if _, err := dao.GetUserByID(req.UserID); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 检查权限是否存在
	if _, err := dao.GetPermissionByName(""); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Permission not found"})
		return
	}

	if err := dao.GrantUserPermission(req.UserID, req.PermissionID, req.Granted); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to grant permission: " + err.Error()})
		return
	}

	action := "granted"
	if !req.Granted {
		action = "revoked"
	}

	c.JSON(http.StatusOK, gin.H{"message": "Permission " + action + " successfully"})
}
