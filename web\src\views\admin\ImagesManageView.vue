<template>
  <div class="images-manage-container">
    <div class="page-header">
      <h2><i class="bi bi-images"></i> 图片管理</h2>
      <p class="page-subtitle">管理系统中所有用户的图片</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-images"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalImages }}</div>
          <div class="stat-label">总图片数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-hdd"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ formatFileSize(totalSize) }}</div>
          <div class="stat-label">总存储大小</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-people"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ activeUsers }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-calendar-today"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ todayUploads }}</div>
          <div class="stat-label">今日上传</div>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="search-filters">
        <div class="search-box">
          <i class="bi bi-search"></i>
          <input 
            type="text" 
            placeholder="搜索图片名称..." 
            v-model="searchQuery"
            @input="handleSearch"
          />
        </div>
        <select v-model="filterUser" @change="handleSearch" class="filter-select">
          <option value="">所有用户</option>
          <option v-for="user in users" :key="user.id" :value="user.id">
            {{ user.email }}
          </option>
        </select>
        <select v-model="filterAlbum" @change="handleSearch" class="filter-select">
          <option value="">所有相册</option>
          <option value="uncategorized">无相册</option>
          <option v-for="album in albums" :key="album.id" :value="album.id">
            {{ album.name }}
          </option>
        </select>
        <select v-model="sortBy" @change="handleSearch" class="filter-select">
          <option value="created_at">按上传时间</option>
          <option value="name">按名称</option>
          <option value="size">按大小</option>
        </select>
      </div>
      <div class="action-buttons">
        <button class="btn btn-outline" @click="refreshData">
          <i class="bi bi-arrow-clockwise"></i> 刷新
        </button>
        <button class="btn btn-danger" @click="bulkDelete" :disabled="selectedImages.length === 0">
          <i class="bi bi-trash"></i> 批量删除 ({{ selectedImages.length }})
        </button>
      </div>
    </div>

    <!-- 图片网格 -->
    <div class="images-grid-container">
      <div class="grid-header">
        <label class="select-all">
          <input 
            type="checkbox" 
            :checked="isAllSelected"
            @change="toggleSelectAll"
          />
          <span>全选</span>
        </label>
        <div class="view-controls">
          <button 
            class="view-btn" 
            :class="{ active: viewMode === 'grid' }"
            @click="viewMode = 'grid'"
          >
            <i class="bi bi-grid-3x3"></i>
          </button>
          <button 
            class="view-btn" 
            :class="{ active: viewMode === 'list' }"
            @click="viewMode = 'list'"
          >
            <i class="bi bi-list"></i>
          </button>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="images-grid">
        <div 
          v-for="image in images" 
          :key="image.id"
          class="image-card"
          :class="{ selected: selectedImages.includes(image.id) }"
        >
          <div class="image-checkbox">
            <input 
              type="checkbox" 
              :value="image.id"
              v-model="selectedImages"
            />
          </div>
          <div class="image-thumbnail" @click="viewImage(image)">
            <img :src="image.url" :alt="image.name" />
          </div>
          <div class="image-info">
            <h4 class="image-name">{{ image.name }}</h4>
            <div class="image-meta">
              <span class="image-size">{{ formatFileSize(image.size) }}</span>
              <span class="image-date">{{ formatDate(image.created_at) }}</span>
            </div>
            <div class="image-owner">
              <i class="bi bi-person"></i>
              {{ image.user?.email }}
            </div>
            <div class="image-album" v-if="image.album">
              <i class="bi bi-collection"></i>
              {{ image.album.name }}
            </div>
          </div>
          <div class="image-actions">
            <button 
              class="action-btn" 
              @click="downloadImage(image)"
              title="下载"
            >
              <i class="bi bi-download"></i>
            </button>
            <button 
              class="action-btn" 
              @click="moveImage(image)"
              title="移动到相册"
            >
              <i class="bi bi-folder"></i>
            </button>
            <button 
              class="action-btn btn-danger" 
              @click="deleteImage(image)"
              title="删除"
            >
              <i class="bi bi-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="images-table-container">
        <table class="images-table">
          <thead>
            <tr>
              <th width="40">
                <input 
                  type="checkbox" 
                  :checked="isAllSelected"
                  @change="toggleSelectAll"
                />
              </th>
              <th>图片</th>
              <th>用户</th>
              <th>相册</th>
              <th>大小</th>
              <th>上传时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="image in images" :key="image.id">
              <td>
                <input 
                  type="checkbox" 
                  :value="image.id"
                  v-model="selectedImages"
                />
              </td>
              <td>
                <div class="table-image-info">
                  <img :src="image.url" :alt="image.name" class="table-thumbnail" />
                  <div class="table-image-details">
                    <div class="table-image-name">{{ image.name }}</div>
                    <div class="table-image-id">ID: {{ image.id }}</div>
                  </div>
                </div>
              </td>
              <td>{{ image.user?.email }}</td>
              <td>
                <span v-if="image.album" class="album-tag">{{ image.album.name }}</span>
                <span v-else class="uncategorized-tag">未分类</span>
              </td>
              <td>{{ formatFileSize(image.size) }}</td>
              <td>{{ formatDate(image.created_at) }}</td>
              <td>
                <div class="table-actions">
                  <button 
                    class="btn-icon" 
                    @click="viewImage(image)"
                    title="查看"
                  >
                    <i class="bi bi-eye"></i>
                  </button>
                  <button 
                    class="btn-icon" 
                    @click="downloadImage(image)"
                    title="下载"
                  >
                    <i class="bi bi-download"></i>
                  </button>
                  <button 
                    class="btn-icon" 
                    @click="moveImage(image)"
                    title="移动"
                  >
                    <i class="bi bi-folder"></i>
                  </button>
                  <button 
                    class="btn-icon btn-danger" 
                    @click="deleteImage(image)"
                    title="删除"
                  >
                    <i class="bi bi-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-if="images.length === 0 && !loading" class="empty-state">
        <i class="bi bi-images"></i>
        <h3>暂无图片</h3>
        <p>系统中还没有任何图片</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <i class="bi bi-arrow-repeat spin"></i>
        <p>加载中...</p>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="totalPages > 1">
      <div class="pagination-info">
        显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalImages) }} 
        共 {{ totalImages }} 张图片
      </div>
      <div class="pagination">
        <button 
          class="btn btn-sm" 
          @click="changePage(currentPage - 1)"
          :disabled="currentPage <= 1"
        >
          上一页
        </button>
        <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
        <button 
          class="btn btn-sm" 
          @click="changePage(currentPage + 1)"
          :disabled="currentPage >= totalPages"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 图片预览模态框 -->
    <div v-if="showPreviewModal" class="modal-overlay" @click="closePreview">
      <div class="preview-modal" @click.stop>
        <div class="preview-header">
          <h3>{{ previewImage?.name }}</h3>
          <button class="close-btn" @click="closePreview">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="preview-body">
          <img :src="previewImage?.url" :alt="previewImage?.name" />
        </div>
        <div class="preview-footer">
          <div class="preview-info">
            <span>用户: {{ previewImage?.user?.email }}</span>
            <span>大小: {{ formatFileSize(previewImage?.size || 0) }}</span>
            <span>上传: {{ formatDate(previewImage?.created_at || '') }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知 -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue';
import { useDialog } from '../../utils/dialog';
import api from '../../services/api';
import { useStorageStore } from '../../stores/storage';

// 响应式数据
const images = ref<any[]>([]);
const users = ref<any[]>([]);
const albums = ref<any[]>([]);
const loading = ref(false);
const selectedImages = ref<number[]>([]);
const { confirmDelete, confirm, notify } = useDialog();
const storageStore = useStorageStore();
const viewMode = ref<'grid' | 'list'>('grid');
const showPreviewModal = ref(false);
const previewImage = ref<any>(null);

// 搜索和过滤
const searchQuery = ref('');
const filterUser = ref('');
const filterAlbum = ref('');
const sortBy = ref('created_at');

// 分页
const currentPage = ref(1);
const pageSize = ref(20);
const totalImages = ref(0);
const totalPages = ref(0);

// 统计数据
const totalSize = ref(0);
const activeUsers = ref(0);
const todayUploads = ref(0);

// 通知
const notification = reactive({
  show: false,
  message: '',
  type: 'success'
});

// 计算属性
const isAllSelected = computed(() => {
  return images.value.length > 0 && selectedImages.value.length === images.value.length;
});

// 方法
const fetchImages = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value,
      user_id: filterUser.value,
      album_id: filterAlbum.value,
      sort_by: sortBy.value,
      sort_desc: true
    };

    const response = await api.getAllImagesAdmin(params);
    images.value = response.images || [];
    totalImages.value = response.total || 0;
    totalPages.value = Math.ceil(totalImages.value / pageSize.value);
  } catch (error) {
    console.error('Failed to fetch images:', error);
    notify.error('获取图片列表失败');
  } finally {
    loading.value = false;
  }
};

const fetchUsers = async () => {
  // TODO: 实现获取用户列表的API调用
  console.log('Fetching users...');
};

const fetchAlbums = async () => {
  // TODO: 实现获取相册列表的API调用
  console.log('Fetching albums...');
};

const fetchStats = async () => {
  // TODO: 实现获取统计数据的API调用
  console.log('Fetching stats...');
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchImages();
};

const changePage = (page: number) => {
  currentPage.value = page;
  fetchImages();
};

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedImages.value = [];
  } else {
    selectedImages.value = images.value.map(img => img.id);
  }
};

const viewImage = (image: any) => {
  previewImage.value = image;
  showPreviewModal.value = true;
};

const closePreview = () => {
  showPreviewModal.value = false;
  previewImage.value = null;
};

const downloadImage = (image: any) => {
  // TODO: 实现下载图片功能
  console.log('Downloading image:', image.id);
};

const moveImage = (image: any) => {
  // TODO: 实现移动图片到相册功能
  console.log('Moving image:', image.id);
};

const deleteImage = async (image: any) => {
  const confirmed = await confirmDelete(image.name, {
    details: '删除后无法恢复，请确认操作。'
  });

  if (confirmed) {
    try {
      await api.deleteImageAdmin(image.id);
      notify.success('图片删除成功');
      fetchImages(); // 重新加载图片列表

      // 刷新存储信息（防抖）
      storageStore.debouncedRefresh();
    } catch (error) {
      console.error('删除图片失败:', error);
      notify.error('删除图片失败');
    }
  }
};

const bulkDelete = async () => {
  const confirmed = await confirm(`确定要删除选中的 ${selectedImages.value.length} 张图片吗？`, {
    title: '批量删除图片',
    details: '删除后无法恢复，请确认操作。',
    type: 'danger',
    confirmText: '删除'
  });

  if (confirmed) {
    try {
      await api.bulkDeleteImagesAdmin(selectedImages.value);
      notify.success(`成功删除 ${selectedImages.value.length} 张图片`);
      selectedImages.value = []; // 清空选择
      fetchImages(); // 重新加载图片列表

      // 刷新存储信息（防抖）
      storageStore.refreshStorageInfo();
    } catch (error) {
      console.error('批量删除图片失败:', error);
      notify.error('批量删除图片失败');
    }
  }
};

const refreshData = () => {
  fetchImages();
  fetchStats();
};

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

// 生命周期
onMounted(() => {
  fetchImages();
  fetchUsers();
  fetchAlbums();
  fetchStats();
});
</script>

<style scoped>
/* 样式将在下一个编辑中添加 */
</style>
