import { useDialogStore } from '../stores/dialog'

// 全局弹窗工具函数
export const useDialog = () => {
  const dialogStore = useDialogStore()

  // 确认弹窗
  const confirm = async (message: string, options?: {
    title?: string
    details?: string
    type?: 'warning' | 'danger' | 'info' | 'success'
    confirmText?: string
    cancelText?: string
  }): Promise<boolean> => {
    return await dialogStore.showConfirm({
      message,
      title: options?.title,
      details: options?.details,
      type: options?.type || 'warning',
      confirmText: options?.confirmText,
      cancelText: options?.cancelText
    })
  }

  // 异步确认弹窗
  const confirmAsync = (message: string, onConfirm: () => Promise<void>, options?: {
    title?: string
    details?: string
    type?: 'warning' | 'danger' | 'info' | 'success'
    confirmText?: string
    cancelText?: string
    loadingText?: string
  }): void => {
    dialogStore.showAsyncConfirm({
      message,
      onConfirm,
      title: options?.title,
      details: options?.details,
      type: options?.type || 'warning',
      confirmText: options?.confirmText,
      cancelText: options?.cancelText,
      loadingText: options?.loadingText
    })
  }

  // 删除确认
  const confirmDelete = async (itemName: string, options?: {
    details?: string
    confirmText?: string
  }): Promise<boolean> => {
    return await confirm(`确定要删除"${itemName}"吗？`, {
      title: '删除确认',
      details: options?.details || '此操作不可恢复。',
      type: 'danger',
      confirmText: options?.confirmText || '删除'
    })
  }

  // 异步删除确认
  const confirmDeleteAsync = (itemName: string, onConfirm: () => Promise<void>, options?: {
    details?: string
    confirmText?: string
    loadingText?: string
  }): void => {
    confirmAsync(`确定要删除"${itemName}"吗？`, onConfirm, {
      title: '删除确认',
      details: options?.details || '此操作不可恢复。',
      type: 'danger',
      confirmText: options?.confirmText || '删除',
      loadingText: options?.loadingText || '删除中...'
    })
  }

  // 通知
  const notify = {
    success: (title: string, message?: string) => dialogStore.showSuccess(title, message),
    error: (title: string, message?: string) => dialogStore.showError(title, message),
    warning: (title: string, message?: string) => dialogStore.showWarning(title, message),
    info: (title: string, message?: string) => dialogStore.showInfo(title, message)
  }

  return {
    confirm,
    confirmAsync,
    confirmDelete,
    confirmDeleteAsync,
    notify
  }
}

// 全局方法，可以在任何地方使用
export const $confirm = async (message: string, options?: {
  title?: string
  details?: string
  type?: 'warning' | 'danger' | 'info' | 'success'
  confirmText?: string
  cancelText?: string
}): Promise<boolean> => {
  const { confirm } = useDialog()
  return await confirm(message, options)
}

export const $notify = {
  success: (title: string, message?: string) => {
    const { notify } = useDialog()
    notify.success(title, message)
  },
  error: (title: string, message?: string) => {
    const { notify } = useDialog()
    notify.error(title, message)
  },
  warning: (title: string, message?: string) => {
    const { notify } = useDialog()
    notify.warning(title, message)
  },
  info: (title: string, message?: string) => {
    const { notify } = useDialog()
    notify.info(title, message)
  }
}

// 便捷的删除确认方法
export const $confirmDelete = async (itemName: string, options?: {
  details?: string
  confirmText?: string
}): Promise<boolean> => {
  const { confirmDelete } = useDialog()
  return await confirmDelete(itemName, options)
}

// 便捷的保存确认方法
export const $confirmSave = async (message: string = '确定要保存更改吗？'): Promise<boolean> => {
  const { confirm } = useDialog()
  return await confirm(message, {
    title: '保存确认',
    type: 'info',
    confirmText: '保存',
    cancelText: '取消'
  })
}

// 便捷的退出确认方法
export const $confirmExit = async (message: string = '确定要退出吗？未保存的更改将丢失。'): Promise<boolean> => {
  const { confirm } = useDialog()
  return await confirm(message, {
    title: '退出确认',
    type: 'warning',
    confirmText: '退出',
    cancelText: '取消'
  })
}
