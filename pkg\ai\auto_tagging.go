package ai

import (
	"fmt"
	"image"
	"math"
	"os"
	"sort"
	"strings"
)

// TagSuggestion 标签建议
type TagSuggestion struct {
	Tag        string  `json:"tag"`
	Confidence float64 `json:"confidence"` // 置信度 0-1
	Category   string  `json:"category"`   // 分类
	Source     string  `json:"source"`     // 来源：color, object, scene, text
}

// ImageAnalysis 图片分析结果
type ImageAnalysis struct {
	ColorAnalysis  ColorAnalysis  `json:"color_analysis"`
	ObjectAnalysis ObjectAnalysis `json:"object_analysis"`
	SceneAnalysis  SceneAnalysis  `json:"scene_analysis"`
	TextAnalysis   TextAnalysis   `json:"text_analysis"`
}

// ColorAnalysis 颜色分析
type ColorAnalysis struct {
	DominantColors []ColorInfo `json:"dominant_colors"`
	ColorScheme    string      `json:"color_scheme"` // warm, cool, neutral, vibrant, muted
	Brightness     float64     `json:"brightness"`   // 0-1
	Contrast       float64     `json:"contrast"`     // 0-1
	Saturation     float64     `json:"saturation"`   // 0-1
}

// ColorInfo 颜色信息
type ColorInfo struct {
	Color      string     `json:"color"`      // 十六进制颜色
	Name       string     `json:"name"`       // 颜色名称
	Percentage float64    `json:"percentage"` // 占比
	RGB        [3]int     `json:"rgb"`        // RGB值
	HSV        [3]float64 `json:"hsv"`        // HSV值
}

// ObjectAnalysis 物体分析
type ObjectAnalysis struct {
	DetectedObjects []DetectedObject `json:"detected_objects"`
	MainSubject     string           `json:"main_subject"`
	ObjectCount     int              `json:"object_count"`
}

// DetectedObject 检测到的物体
type DetectedObject struct {
	Name        string      `json:"name"`
	Confidence  float64     `json:"confidence"`
	BoundingBox BoundingBox `json:"bounding_box"`
	Category    string      `json:"category"`
}

// BoundingBox 边界框
type BoundingBox struct {
	X      int `json:"x"`
	Y      int `json:"y"`
	Width  int `json:"width"`
	Height int `json:"height"`
}

// SceneAnalysis 场景分析
type SceneAnalysis struct {
	SceneType   string  `json:"scene_type"`  // indoor, outdoor, nature, urban
	Environment string  `json:"environment"` // home, office, park, street
	Weather     string  `json:"weather"`     // sunny, cloudy, rainy
	TimeOfDay   string  `json:"time_of_day"` // morning, afternoon, evening, night
	Season      string  `json:"season"`      // spring, summer, autumn, winter
	Confidence  float64 `json:"confidence"`
}

// TextAnalysis 文本分析
type TextAnalysis struct {
	HasText    bool     `json:"has_text"`
	Languages  []string `json:"languages"`
	TextType   string   `json:"text_type"` // document, sign, caption, handwritten
	Keywords   []string `json:"keywords"`
	Confidence float64  `json:"confidence"`
}

// AutoTagger 自动标签器
type AutoTagger struct {
	colorNames map[string]string
	objectDB   map[string][]string
	sceneDB    map[string][]string
}

// NewAutoTagger 创建自动标签器
func NewAutoTagger() *AutoTagger {
	at := &AutoTagger{
		colorNames: make(map[string]string),
		objectDB:   make(map[string][]string),
		sceneDB:    make(map[string][]string),
	}

	at.initializeColorNames()
	at.initializeObjectDB()
	at.initializeSceneDB()

	return at
}

// AnalyzeImage 分析图片并生成标签建议
func (at *AutoTagger) AnalyzeImage(imagePath string) ([]TagSuggestion, *ImageAnalysis, error) {
	// 打开图片
	file, err := os.Open(imagePath)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to open image: %v", err)
	}
	defer file.Close()

	// 解码图片
	img, _, err := image.Decode(file)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to decode image: %v", err)
	}

	// 执行各种分析
	analysis := &ImageAnalysis{
		ColorAnalysis:  at.analyzeColors(img),
		ObjectAnalysis: at.analyzeObjects(img),
		SceneAnalysis:  at.analyzeScene(img),
		TextAnalysis:   at.analyzeText(img),
	}

	// 生成标签建议
	suggestions := at.generateTagSuggestions(analysis)

	return suggestions, analysis, nil
}

// analyzeColors 分析颜色
func (at *AutoTagger) analyzeColors(img image.Image) ColorAnalysis {
	bounds := img.Bounds()
	colorCount := make(map[string]int)
	var totalR, totalG, totalB, totalPixels int

	// 采样像素
	step := 5 // 每5个像素采样一次
	for y := bounds.Min.Y; y < bounds.Max.Y; y += step {
		for x := bounds.Min.X; x < bounds.Max.X; x += step {
			r, g, b, _ := img.At(x, y).RGBA()
			r8, g8, b8 := uint8(r>>8), uint8(g>>8), uint8(b>>8)

			totalR += int(r8)
			totalG += int(g8)
			totalB += int(b8)
			totalPixels++

			// 量化颜色
			quantizedColor := fmt.Sprintf("#%02x%02x%02x",
				(r8/32)*32, (g8/32)*32, (b8/32)*32)
			colorCount[quantizedColor]++
		}
	}

	// 计算主要颜色
	type colorFreq struct {
		color string
		count int
	}

	var colors []colorFreq
	for color, count := range colorCount {
		colors = append(colors, colorFreq{color, count})
	}

	sort.Slice(colors, func(i, j int) bool {
		return colors[i].count > colors[j].count
	})

	// 提取前5个主要颜色
	var dominantColors []ColorInfo
	for i := 0; i < min(5, len(colors)); i++ {
		colorHex := colors[i].color
		rgb := at.hexToRGB(colorHex)
		hsv := at.rgbToHSV(rgb[0], rgb[1], rgb[2])

		dominantColors = append(dominantColors, ColorInfo{
			Color:      colorHex,
			Name:       at.getColorName(colorHex),
			Percentage: float64(colors[i].count) / float64(totalPixels),
			RGB:        rgb,
			HSV:        hsv,
		})
	}

	// 计算整体特征
	avgR := float64(totalR) / float64(totalPixels)
	avgG := float64(totalG) / float64(totalPixels)
	avgB := float64(totalB) / float64(totalPixels)

	brightness := (avgR + avgG + avgB) / (3 * 255)

	// 计算对比度和饱和度
	contrast := at.calculateContrast(img)
	saturation := at.calculateSaturation(dominantColors)

	// 确定色彩方案
	colorScheme := at.determineColorScheme(dominantColors, brightness, saturation)

	return ColorAnalysis{
		DominantColors: dominantColors,
		ColorScheme:    colorScheme,
		Brightness:     brightness,
		Contrast:       contrast,
		Saturation:     saturation,
	}
}

// analyzeObjects 分析物体
func (at *AutoTagger) analyzeObjects(img image.Image) ObjectAnalysis {
	// 简化的物体检测（实际应使用YOLO、SSD等深度学习模型）
	var detectedObjects []DetectedObject

	// 基于颜色和形状的简单物体检测
	objects := at.detectSimpleObjects(img)

	for _, obj := range objects {
		detectedObjects = append(detectedObjects, DetectedObject{
			Name:        obj.name,
			Confidence:  obj.confidence,
			BoundingBox: obj.boundingBox,
			Category:    obj.category,
		})
	}

	// 确定主要主题
	mainSubject := "unknown"
	if len(detectedObjects) > 0 {
		mainSubject = detectedObjects[0].Name
	}

	return ObjectAnalysis{
		DetectedObjects: detectedObjects,
		MainSubject:     mainSubject,
		ObjectCount:     len(detectedObjects),
	}
}

// analyzeScene 分析场景
func (at *AutoTagger) analyzeScene(img image.Image) SceneAnalysis {
	// 基于颜色分布和纹理的场景分析
	colorAnalysis := at.analyzeColors(img)

	// 简化的场景分类
	sceneType := "unknown"
	environment := "unknown"
	weather := "unknown"
	timeOfDay := "unknown"
	season := "unknown"
	confidence := 0.5

	// 基于亮度判断时间
	if colorAnalysis.Brightness > 0.7 {
		timeOfDay = "day"
		weather = "sunny"
	} else if colorAnalysis.Brightness > 0.3 {
		timeOfDay = "afternoon"
		weather = "cloudy"
	} else {
		timeOfDay = "night"
	}

	// 基于颜色判断场景类型
	hasGreen := false
	hasBlue := false
	hasGray := false

	for _, color := range colorAnalysis.DominantColors {
		if strings.Contains(color.Name, "green") {
			hasGreen = true
		}
		if strings.Contains(color.Name, "blue") {
			hasBlue = true
		}
		if strings.Contains(color.Name, "gray") {
			hasGray = true
		}
	}

	if hasGreen && hasBlue {
		sceneType = "outdoor"
		environment = "nature"
		season = "summer"
	} else if hasGray {
		sceneType = "urban"
		environment = "city"
	} else {
		sceneType = "indoor"
		environment = "home"
	}

	return SceneAnalysis{
		SceneType:   sceneType,
		Environment: environment,
		Weather:     weather,
		TimeOfDay:   timeOfDay,
		Season:      season,
		Confidence:  confidence,
	}
}

// analyzeText 分析文本
func (at *AutoTagger) analyzeText(img image.Image) TextAnalysis {
	// 简化的文本检测
	hasText := at.detectText(img)

	return TextAnalysis{
		HasText:    hasText,
		Languages:  []string{"auto"},
		TextType:   "unknown",
		Keywords:   []string{},
		Confidence: 0.5,
	}
}

// generateTagSuggestions 生成标签建议
func (at *AutoTagger) generateTagSuggestions(analysis *ImageAnalysis) []TagSuggestion {
	var suggestions []TagSuggestion

	// 基于颜色的标签
	for _, color := range analysis.ColorAnalysis.DominantColors {
		if color.Percentage > 0.1 { // 占比超过10%
			suggestions = append(suggestions, TagSuggestion{
				Tag:        color.Name,
				Confidence: color.Percentage,
				Category:   "color",
				Source:     "color",
			})
		}
	}

	// 色彩方案标签
	suggestions = append(suggestions, TagSuggestion{
		Tag:        analysis.ColorAnalysis.ColorScheme,
		Confidence: 0.8,
		Category:   "style",
		Source:     "color",
	})

	// 基于物体的标签
	for _, obj := range analysis.ObjectAnalysis.DetectedObjects {
		suggestions = append(suggestions, TagSuggestion{
			Tag:        obj.Name,
			Confidence: obj.Confidence,
			Category:   obj.Category,
			Source:     "object",
		})
	}

	// 基于场景的标签
	scene := analysis.SceneAnalysis
	if scene.SceneType != "unknown" {
		suggestions = append(suggestions, TagSuggestion{
			Tag:        scene.SceneType,
			Confidence: scene.Confidence,
			Category:   "scene",
			Source:     "scene",
		})
	}

	if scene.Environment != "unknown" {
		suggestions = append(suggestions, TagSuggestion{
			Tag:        scene.Environment,
			Confidence: scene.Confidence,
			Category:   "environment",
			Source:     "scene",
		})
	}

	if scene.TimeOfDay != "unknown" {
		suggestions = append(suggestions, TagSuggestion{
			Tag:        scene.TimeOfDay,
			Confidence: scene.Confidence,
			Category:   "time",
			Source:     "scene",
		})
	}

	// 基于文本的标签
	if analysis.TextAnalysis.HasText {
		suggestions = append(suggestions, TagSuggestion{
			Tag:        "text",
			Confidence: analysis.TextAnalysis.Confidence,
			Category:   "content",
			Source:     "text",
		})
	}

	// 排序并过滤
	sort.Slice(suggestions, func(i, j int) bool {
		return suggestions[i].Confidence > suggestions[j].Confidence
	})

	// 去重和过滤低置信度标签
	var filtered []TagSuggestion
	seen := make(map[string]bool)

	for _, suggestion := range suggestions {
		if suggestion.Confidence >= 0.3 && !seen[suggestion.Tag] {
			filtered = append(filtered, suggestion)
			seen[suggestion.Tag] = true
		}
	}

	// 限制数量
	if len(filtered) > 10 {
		filtered = filtered[:10]
	}

	return filtered
}

// 初始化方法
func (at *AutoTagger) initializeColorNames() {
	at.colorNames = map[string]string{
		"#ff0000": "red",
		"#00ff00": "green",
		"#0000ff": "blue",
		"#ffff00": "yellow",
		"#ff00ff": "magenta",
		"#00ffff": "cyan",
		"#ffffff": "white",
		"#000000": "black",
		"#808080": "gray",
		"#ffa500": "orange",
		"#800080": "purple",
		"#ffc0cb": "pink",
		"#a52a2a": "brown",
		"#008000": "dark_green",
		"#000080": "navy",
	}
}

func (at *AutoTagger) initializeObjectDB() {
	at.objectDB = map[string][]string{
		"person":   {"人物", "人", "肖像"},
		"car":      {"汽车", "车辆", "交通"},
		"building": {"建筑", "房屋", "城市"},
		"tree":     {"树木", "植物", "自然"},
		"flower":   {"花朵", "植物", "自然"},
		"animal":   {"动物", "宠物", "生物"},
		"food":     {"食物", "美食", "餐饮"},
		"sky":      {"天空", "云朵", "自然"},
		"water":    {"水", "海洋", "湖泊"},
	}
}

func (at *AutoTagger) initializeSceneDB() {
	at.sceneDB = map[string][]string{
		"outdoor": {"户外", "室外", "自然"},
		"indoor":  {"室内", "房间", "家居"},
		"nature":  {"自然", "风景", "户外"},
		"urban":   {"城市", "都市", "建筑"},
		"home":    {"家", "家庭", "居家"},
		"office":  {"办公室", "工作", "商务"},
		"park":    {"公园", "绿地", "休闲"},
		"street":  {"街道", "道路", "城市"},
	}
}

// 辅助方法
func (at *AutoTagger) hexToRGB(hex string) [3]int {
	if len(hex) != 7 || hex[0] != '#' {
		return [3]int{0, 0, 0}
	}

	var r, g, b int
	fmt.Sscanf(hex[1:], "%02x%02x%02x", &r, &g, &b)
	return [3]int{r, g, b}
}

func (at *AutoTagger) rgbToHSV(r, g, b int) [3]float64 {
	rf, gf, bf := float64(r)/255.0, float64(g)/255.0, float64(b)/255.0

	max := math.Max(rf, math.Max(gf, bf))
	min := math.Min(rf, math.Min(gf, bf))
	diff := max - min

	var h, s, v float64

	// 计算色相
	if diff == 0 {
		h = 0
	} else if max == rf {
		h = 60 * ((gf - bf) / diff)
	} else if max == gf {
		h = 60 * (2 + (bf-rf)/diff)
	} else {
		h = 60 * (4 + (rf-gf)/diff)
	}

	if h < 0 {
		h += 360
	}

	// 计算饱和度
	if max == 0 {
		s = 0
	} else {
		s = diff / max
	}

	// 计算明度
	v = max

	return [3]float64{h, s, v}
}

func (at *AutoTagger) getColorName(hex string) string {
	if name, exists := at.colorNames[hex]; exists {
		return name
	}

	// 简化的颜色名称匹配
	rgb := at.hexToRGB(hex)
	r, g, b := rgb[0], rgb[1], rgb[2]

	if r > 200 && g < 100 && b < 100 {
		return "red"
	} else if g > 200 && r < 100 && b < 100 {
		return "green"
	} else if b > 200 && r < 100 && g < 100 {
		return "blue"
	} else if r > 200 && g > 200 && b < 100 {
		return "yellow"
	} else if r > 200 && g > 200 && b > 200 {
		return "white"
	} else if r < 50 && g < 50 && b < 50 {
		return "black"
	} else {
		return "gray"
	}
}

func (at *AutoTagger) calculateContrast(img image.Image) float64 {
	// 简化的对比度计算
	return 0.5 // 占位符
}

func (at *AutoTagger) calculateSaturation(colors []ColorInfo) float64 {
	if len(colors) == 0 {
		return 0
	}

	var totalSaturation float64
	for _, color := range colors {
		totalSaturation += color.HSV[1]
	}

	return totalSaturation / float64(len(colors))
}

func (at *AutoTagger) determineColorScheme(colors []ColorInfo, brightness, saturation float64) string {
	if saturation > 0.7 {
		return "vibrant"
	} else if saturation < 0.3 {
		return "muted"
	} else if brightness > 0.7 {
		return "bright"
	} else if brightness < 0.3 {
		return "dark"
	} else {
		return "neutral"
	}
}

type simpleObject struct {
	name        string
	confidence  float64
	boundingBox BoundingBox
	category    string
}

func (at *AutoTagger) detectSimpleObjects(img image.Image) []simpleObject {
	// 简化的物体检测
	var objects []simpleObject

	// 基于颜色的简单检测
	bounds := img.Bounds()

	// 检测天空（蓝色区域）
	if at.hasBlueRegion(img) {
		objects = append(objects, simpleObject{
			name:        "sky",
			confidence:  0.7,
			boundingBox: BoundingBox{0, 0, bounds.Dx(), bounds.Dy() / 3},
			category:    "nature",
		})
	}

	// 检测植物（绿色区域）
	if at.hasGreenRegion(img) {
		objects = append(objects, simpleObject{
			name:        "plant",
			confidence:  0.6,
			boundingBox: BoundingBox{0, bounds.Dy() / 2, bounds.Dx(), bounds.Dy() / 2},
			category:    "nature",
		})
	}

	return objects
}

func (at *AutoTagger) hasBlueRegion(img image.Image) bool {
	// 简化检测
	return true // 占位符
}

func (at *AutoTagger) hasGreenRegion(img image.Image) bool {
	// 简化检测
	return true // 占位符
}

func (at *AutoTagger) detectText(img image.Image) bool {
	// 简化的文本检测
	return false // 占位符
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
