import { ref, onMounted, onUnmounted } from 'vue';

export interface DragUploadOptions {
  onFilesDropped?: (files: File[]) => void;
  acceptedTypes?: string[];
  maxFileSize?: number;
  multiple?: boolean;
}

export function useGlobalDragUpload(options: DragUploadOptions = {}) {
  const {
    onFilesDropped,
    acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxFileSize = 10 * 1024 * 1024, // 10MB
    multiple = true
  } = options;

  const isDragOver = ref(false);
  const dragCounter = ref(0);

  // 检查文件类型
  const isValidFileType = (file: File): boolean => {
    return acceptedTypes.includes(file.type);
  };

  // 检查文件大小
  const isValidFileSize = (file: File): boolean => {
    return file.size <= maxFileSize;
  };

  // 过滤有效文件
  const filterValidFiles = (files: File[]): { valid: File[], invalid: File[] } => {
    const valid: File[] = [];
    const invalid: File[] = [];

    files.forEach(file => {
      if (isValidFileType(file) && isValidFileSize(file)) {
        valid.push(file);
      } else {
        invalid.push(file);
      }
    });

    return { valid, invalid };
  };

  // 全局拖拽事件处理
  const handleGlobalDragEnter = (e: DragEvent) => {
    e.preventDefault();
    dragCounter.value++;
    
    // 检查是否包含文件
    if (e.dataTransfer?.types.includes('Files')) {
      isDragOver.value = true;
    }
  };

  const handleGlobalDragOver = (e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleGlobalDragLeave = (e: DragEvent) => {
    e.preventDefault();
    dragCounter.value--;
    
    if (dragCounter.value === 0) {
      isDragOver.value = false;
    }
  };

  const handleGlobalDrop = (e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    isDragOver.value = false;
    dragCounter.value = 0;

    const files = Array.from(e.dataTransfer?.files || []);
    
    if (files.length === 0) return;

    const { valid, invalid } = filterValidFiles(files);

    // 如果不允许多文件上传，只取第一个有效文件
    const filesToUpload = multiple ? valid : valid.slice(0, 1);

    if (filesToUpload.length > 0 && onFilesDropped) {
      onFilesDropped(filesToUpload);
    }

    // 返回处理结果
    return {
      uploaded: filesToUpload,
      rejected: invalid,
      total: files.length
    };
  };

  // 启用全局拖拽监听
  const enableGlobalDragUpload = () => {
    document.addEventListener('dragenter', handleGlobalDragEnter);
    document.addEventListener('dragover', handleGlobalDragOver);
    document.addEventListener('dragleave', handleGlobalDragLeave);
    document.addEventListener('drop', handleGlobalDrop);
  };

  // 禁用全局拖拽监听
  const disableGlobalDragUpload = () => {
    document.removeEventListener('dragenter', handleGlobalDragEnter);
    document.removeEventListener('dragover', handleGlobalDragOver);
    document.removeEventListener('dragleave', handleGlobalDragLeave);
    document.removeEventListener('drop', handleGlobalDrop);
  };

  // 自动启用/禁用（在组件挂载/卸载时）
  const autoEnable = () => {
    onMounted(enableGlobalDragUpload);
    onUnmounted(disableGlobalDragUpload);
  };

  return {
    isDragOver,
    enableGlobalDragUpload,
    disableGlobalDragUpload,
    autoEnable,
    isValidFileType,
    isValidFileSize,
    filterValidFiles
  };
}
