package controllers

import (
	"strconv"
	"time"

	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"
	"cloudbed/pkg/response"

	"github.com/gin-gonic/gin"
)

// InviteCollaborator 邀请协作者
func InviteCollaborator(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req models.CollaboratorInviteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 获取分享信息
	share, err := dao.GetShareByID(req.ShareID)
	if err != nil {
		response.NotFound(c, "分享不存在")
		return
	}

	// 验证权限（只有分享者可以邀请协作者）
	if share.OwnerID != userID.(uint) {
		response.Forbidden(c, "无权限邀请协作者")
		return
	}

	// 查找被邀请用户
	invitedUser, err := dao.GetUserByEmail(req.UserEmail)
	if err != nil {
		response.NotFound(c, "用户不存在")
		return
	}

	// 检查是否已经是协作者
	collaborators, err := dao.GetShareCollaborators(req.ShareID)
	if err == nil {
		for _, collaborator := range collaborators {
			if collaborator.UserID == invitedUser.ID {
				response.BadRequest(c, "用户已经是协作者")
				return
			}
		}
	}

	// 创建协作者邀请
	collaborator := &models.ShareCollaborator{
		ShareID:     req.ShareID,
		UserID:      invitedUser.ID,
		Permissions: req.Permissions,
		Role:        req.Role,
		InvitedBy:   userID.(uint),
		Status:      "pending",
	}

	// 设置默认权限和角色
	if len(collaborator.Permissions) == 0 {
		collaborator.Permissions = []models.SharePermission{models.PermissionView, models.PermissionComment}
	}
	if collaborator.Role == "" {
		collaborator.Role = "viewer"
	}

	// 保存邀请
	if err := dao.CreateShareCollaborator(collaborator); err != nil {
		response.InternalServerErrorWithDetail(c, "创建邀请失败", err.Error())
		return
	}

	// TODO: 发送邀请邮件
	// sendInviteEmail(invitedUser.Email, collaborator.InviteToken, req.Message)

	response.SuccessWithMessage(c, "邀请发送成功", gin.H{
		"collaborator":   collaborator,
		"invited_user":   invitedUser,
		"invite_message": "邀请邮件已发送",
	})
}

// AcceptInvitation 接受邀请
func AcceptInvitation(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	token := c.Param("token")
	if token == "" {
		response.BadRequest(c, "邀请令牌不能为空")
		return
	}

	// 获取邀请信息
	collaborator, err := dao.GetCollaboratorByToken(token)
	if err != nil {
		response.NotFound(c, "邀请不存在或已失效")
		return
	}

	// 验证用户身份
	if collaborator.UserID != userID.(uint) {
		response.Forbidden(c, "无权限接受此邀请")
		return
	}

	// 检查邀请状态
	if collaborator.Status != "pending" {
		response.BadRequest(c, "邀请已处理")
		return
	}

	// 接受邀请
	now := time.Now()
	collaborator.AcceptedAt = &now
	collaborator.Status = "accepted"

	if err := dao.UpdateShareCollaborator(collaborator); err != nil {
		response.InternalServerErrorWithDetail(c, "接受邀请失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "邀请接受成功", collaborator)
}

// DeclineInvitation 拒绝邀请
func DeclineInvitation(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	token := c.Param("token")
	if token == "" {
		response.BadRequest(c, "邀请令牌不能为空")
		return
	}

	// 获取邀请信息
	collaborator, err := dao.GetCollaboratorByToken(token)
	if err != nil {
		response.NotFound(c, "邀请不存在或已失效")
		return
	}

	// 验证用户身份
	if collaborator.UserID != userID.(uint) {
		response.Forbidden(c, "无权限处理此邀请")
		return
	}

	// 拒绝邀请
	collaborator.Status = "declined"

	if err := dao.UpdateShareCollaborator(collaborator); err != nil {
		response.InternalServerErrorWithDetail(c, "拒绝邀请失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "邀请已拒绝", collaborator)
}

// GetCollaborators 获取协作者列表
func GetCollaborators(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	shareID, err := strconv.ParseUint(c.Param("shareId"), 10, 32)
	if err != nil {
		response.BadRequest(c, "分享ID格式错误")
		return
	}

	// 获取分享信息
	share, err := dao.GetShareByID(uint(shareID))
	if err != nil {
		response.NotFound(c, "分享不存在")
		return
	}

	// 验证权限（分享者或协作者可以查看）
	hasPermission := share.OwnerID == userID.(uint)
	if !hasPermission {
		collaborators, err := dao.GetShareCollaborators(uint(shareID))
		if err == nil {
			for _, collaborator := range collaborators {
				if collaborator.UserID == userID.(uint) && collaborator.Status == "accepted" {
					hasPermission = true
					break
				}
			}
		}
	}

	if !hasPermission {
		response.Forbidden(c, "无权限查看协作者列表")
		return
	}

	// 获取协作者列表
	collaborators, err := dao.GetShareCollaborators(uint(shareID))
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取协作者列表失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"share":         share,
		"collaborators": collaborators,
	})
}

// RemoveCollaborator 移除协作者
func RemoveCollaborator(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	collaboratorID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "协作者ID格式错误")
		return
	}

	// 获取协作者信息
	collaborators, err := dao.GetShareCollaborators(0) // 需要修改DAO方法支持按ID查询
	if err != nil {
		response.InternalServerError(c, "获取协作者信息失败")
		return
	}

	var targetCollaborator *models.ShareCollaborator
	for _, collaborator := range collaborators {
		if collaborator.ID == uint(collaboratorID) {
			targetCollaborator = &collaborator
			break
		}
	}

	if targetCollaborator == nil {
		response.NotFound(c, "协作者不存在")
		return
	}

	// 获取分享信息
	share, err := dao.GetShareByID(targetCollaborator.ShareID)
	if err != nil {
		response.NotFound(c, "分享不存在")
		return
	}

	// 验证权限（只有分享者可以移除协作者）
	if share.OwnerID != userID.(uint) {
		response.Forbidden(c, "无权限移除协作者")
		return
	}

	// 移除协作者
	if err := dao.DeleteShareCollaborator(uint(collaboratorID)); err != nil {
		response.InternalServerErrorWithDetail(c, "移除协作者失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "协作者移除成功", nil)
}

// CreateComment 创建评论
func CreateComment(c *gin.Context) {
	var req models.CommentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 获取分享信息
	share, err := dao.GetShareByToken(c.GetHeader("Share-Token"))
	if err != nil {
		response.NotFound(c, "分享不存在或已失效")
		return
	}

	// 检查评论权限
	if !share.HasPermission(models.PermissionComment) {
		response.Forbidden(c, "无评论权限")
		return
	}

	// 创建评论
	comment := &models.Comment{
		ShareID:   req.ShareID,
		Content:   req.Content,
		ParentID:  req.ParentID,
		PositionX: req.PositionX,
		PositionY: req.PositionY,
		Status:    "active",
	}

	// 设置评论者信息
	if userID, exists := c.Get("user_id"); exists {
		uid := userID.(uint)
		comment.UserID = &uid
	} else {
		// 游客评论
		if !share.AllowGuest {
			response.Forbidden(c, "不允许游客评论")
			return
		}
		comment.AuthorName = req.AuthorName
		comment.AuthorEmail = req.AuthorEmail
	}

	// 保存评论
	if err := dao.CreateComment(comment); err != nil {
		response.InternalServerErrorWithDetail(c, "创建评论失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "评论创建成功", comment)
}

// GetComments 获取评论列表
func GetComments(c *gin.Context) {
	shareID, err := strconv.ParseUint(c.Param("shareId"), 10, 32)
	if err != nil {
		response.BadRequest(c, "分享ID格式错误")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// 获取分享信息（验证访问权限）
	share, err := dao.GetShareByID(uint(shareID))
	if err != nil {
		response.NotFound(c, "分享不存在")
		return
	}

	if !share.IsActive() {
		response.BadRequest(c, "分享已过期或被禁用")
		return
	}

	// 获取评论列表
	comments, total, err := dao.GetCommentsByShare(uint(shareID), page, pageSize)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取评论列表失败", err.Error())
		return
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	response.Success(c, gin.H{
		"comments":    comments,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
	})
}

// LikeComment 点赞评论
func LikeComment(c *gin.Context) {
	commentID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "评论ID格式错误")
		return
	}

	// 创建点赞记录
	like := &models.CommentLike{
		CommentID: uint(commentID),
		IPAddress: getClientIP(c),
	}

	// 设置用户ID（如果已登录）
	if userID, exists := c.Get("user_id"); exists {
		uid := userID.(uint)
		like.UserID = &uid
	}

	// 保存点赞
	if err := dao.CreateCommentLike(like); err != nil {
		if err.Error() == "already liked" {
			response.BadRequest(c, "已经点赞过了")
		} else {
			response.InternalServerErrorWithDetail(c, "点赞失败", err.Error())
		}
		return
	}

	response.SuccessWithMessage(c, "点赞成功", nil)
}

// UnlikeComment 取消点赞评论
func UnlikeComment(c *gin.Context) {
	commentID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "评论ID格式错误")
		return
	}

	var userID *uint
	ipAddress := getClientIP(c)

	// 获取用户ID（如果已登录）
	if uid, exists := c.Get("user_id"); exists {
		u := uid.(uint)
		userID = &u
	}

	// 删除点赞
	if err := dao.DeleteCommentLike(uint(commentID), userID, ipAddress); err != nil {
		response.InternalServerErrorWithDetail(c, "取消点赞失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "取消点赞成功", nil)
}

// LikeShare 点赞分享
func LikeShare(c *gin.Context) {
	shareID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "分享ID格式错误")
		return
	}

	// 创建点赞记录
	like := &models.ShareLike{
		ShareID:   uint(shareID),
		IPAddress: getClientIP(c),
	}

	// 设置用户ID（如果已登录）
	if userID, exists := c.Get("user_id"); exists {
		uid := userID.(uint)
		like.UserID = &uid
	}

	// 保存点赞
	if err := dao.CreateShareLike(like); err != nil {
		if err.Error() == "already liked" {
			response.BadRequest(c, "已经点赞过了")
		} else {
			response.InternalServerErrorWithDetail(c, "点赞失败", err.Error())
		}
		return
	}

	response.SuccessWithMessage(c, "点赞成功", nil)
}

// UnlikeShare 取消点赞分享
func UnlikeShare(c *gin.Context) {
	shareID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "分享ID格式错误")
		return
	}

	var userID *uint
	ipAddress := getClientIP(c)

	// 获取用户ID（如果已登录）
	if uid, exists := c.Get("user_id"); exists {
		u := uid.(uint)
		userID = &u
	}

	// 删除点赞
	if err := dao.DeleteShareLike(uint(shareID), userID, ipAddress); err != nil {
		response.InternalServerErrorWithDetail(c, "取消点赞失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "取消点赞成功", nil)
}
