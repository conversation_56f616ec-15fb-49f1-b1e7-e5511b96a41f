package controllers

import (
	"strconv"
	"strings"

	"cloudbed/internal/core/repository"
	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
	"cloudbed/pkg/response"
	"cloudbed/pkg/search"

	"github.com/gin-gonic/gin"
)

// SearchImages 搜索图片
func SearchImages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 解析搜索参数
	params := &search.SearchParams{
		UserID: userID.(uint),
	}

	if err := c.ShouldBindQuery(params); err != nil {
		response.BadRequestWithDetail(c, "参数解析失败", err.Error())
		return
	}

	// 验证参数
	if err := search.ValidateSearchParams(params); err != nil {
		response.BadRequestWithDetail(c, "参数验证失败", err.Error())
		return
	}

	// 构建搜索查询
	builder := search.NewSearchBuilder(database.DB, params)
	query := builder.BuildImageSearch()

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerErrorWithDetail(c, "查询失败", err.Error())
		return
	}

	// 分页查询
	offset := (params.Page - 1) * params.PageSize
	var images []models.Image

	err := query.Preload("Album").
		Offset(offset).
		Limit(params.PageSize).
		Find(&images).Error

	if err != nil {
		response.InternalServerErrorWithDetail(c, "查询图片失败", err.Error())
		return
	}

	// 获取搜索建议
	suggestions := search.GetSearchSuggestions(database.DB, params.Query, userID.(uint), 5)

	// 构建结果
	totalPages := int((total + int64(params.PageSize) - 1) / int64(params.PageSize))
	result := &search.SearchResult{
		Items:       images,
		Total:       total,
		Page:        params.Page,
		PageSize:    params.PageSize,
		TotalPages:  totalPages,
		HasNext:     params.Page < totalPages,
		HasPrev:     params.Page > 1,
		Query:       params.Query,
		Filters:     buildAppliedFilters(params),
		Suggestions: suggestions,
	}

	response.Success(c, result)
}

// SearchAlbums 搜索相册
func SearchAlbums(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 解析搜索参数
	params := &search.SearchParams{
		UserID: userID.(uint),
	}

	if err := c.ShouldBindQuery(params); err != nil {
		response.BadRequestWithDetail(c, "参数解析失败", err.Error())
		return
	}

	// 验证参数
	if err := search.ValidateSearchParams(params); err != nil {
		response.BadRequestWithDetail(c, "参数验证失败", err.Error())
		return
	}

	// 构建搜索查询
	builder := search.NewSearchBuilder(database.DB, params)
	query := builder.BuildAlbumSearch()

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.InternalServerErrorWithDetail(c, "查询失败", err.Error())
		return
	}

	// 分页查询
	offset := (params.Page - 1) * params.PageSize
	var albums []models.Album

	err := query.Offset(offset).
		Limit(params.PageSize).
		Find(&albums).Error

	if err != nil {
		response.InternalServerErrorWithDetail(c, "查询相册失败", err.Error())
		return
	}

	// 转换为响应格式并获取每个相册的图片数量
	var albumResponses []models.AlbumResponse
	for _, album := range albums {
		var imageCount int64
		database.DB.Model(&models.Image{}).
			Where("album_id = ? AND user_id = ?", album.ID, userID.(uint)).
			Count(&imageCount)

		albumResponse := models.AlbumResponse{
			ID:          album.ID,
			Name:        album.Name,
			Description: album.Description,
			CoverImage:  album.CoverImage,
			ImageCount:  imageCount,
			CreatedAt:   album.CreatedAt,
			UpdatedAt:   album.UpdatedAt,
		}
		albumResponses = append(albumResponses, albumResponse)
	}

	// 获取搜索建议
	suggestions := search.GetSearchSuggestions(database.DB, params.Query, userID.(uint), 5)

	// 构建结果
	totalPages := int((total + int64(params.PageSize) - 1) / int64(params.PageSize))
	result := &search.SearchResult{
		Items:       albumResponses,
		Total:       total,
		Page:        params.Page,
		PageSize:    params.PageSize,
		TotalPages:  totalPages,
		HasNext:     params.Page < totalPages,
		HasPrev:     params.Page > 1,
		Query:       params.Query,
		Filters:     buildAppliedFilters(params),
		Suggestions: suggestions,
	}

	response.Success(c, result)
}

// GetSearchSuggestions 获取搜索建议
func GetSearchSuggestions(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	query := c.Query("q")
	if query == "" {
		response.Success(c, gin.H{"suggestions": []string{}})
		return
	}

	// 获取建议数量，默认10个
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 20 {
		limit = 20
	}

	suggestions := search.GetSearchSuggestions(database.DB, query, userID.(uint), limit)

	response.Success(c, gin.H{
		"suggestions": suggestions,
		"query":       query,
	})
}

// GetSearchFilters 获取搜索过滤器选项
func GetSearchFilters(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 获取用户的图片格式列表
	var formats []string
	database.DB.Model(&models.Image{}).
		Where("user_id = ? AND format != ''", userID.(uint)).
		Distinct("format").
		Pluck("format", &formats)

	// 获取用户的相册列表
	albums, err := dao.GetAlbumsByUserID(userID.(uint))
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取相册列表失败", err.Error())
		return
	}

	// 获取文件大小范围
	var sizeStats struct {
		MinSize int64 `json:"min_size"`
		MaxSize int64 `json:"max_size"`
	}
	database.DB.Model(&models.Image{}).
		Where("user_id = ?", userID.(uint)).
		Select("MIN(size) as min_size, MAX(size) as max_size").
		Scan(&sizeStats)

	// 获取图片尺寸范围
	var dimensionStats struct {
		MinWidth  int `json:"min_width"`
		MaxWidth  int `json:"max_width"`
		MinHeight int `json:"min_height"`
		MaxHeight int `json:"max_height"`
	}
	database.DB.Model(&models.Image{}).
		Where("user_id = ? AND width > 0 AND height > 0", userID.(uint)).
		Select("MIN(width) as min_width, MAX(width) as max_width, MIN(height) as min_height, MAX(height) as max_height").
		Scan(&dimensionStats)

	response.Success(c, gin.H{
		"formats": formats,
		"albums":  albums,
		"size_range": gin.H{
			"min": sizeStats.MinSize,
			"max": sizeStats.MaxSize,
		},
		"dimension_range": gin.H{
			"width": gin.H{
				"min": dimensionStats.MinWidth,
				"max": dimensionStats.MaxWidth,
			},
			"height": gin.H{
				"min": dimensionStats.MinHeight,
				"max": dimensionStats.MaxHeight,
			},
		},
	})
}

// buildAppliedFilters 构建已应用的过滤器信息
func buildAppliedFilters(params *search.SearchParams) map[string]interface{} {
	filters := make(map[string]interface{})

	if params.Query != "" {
		filters["query"] = params.Query
	}

	if len(params.Tags) > 0 {
		filters["tags"] = params.Tags
	}

	if params.AlbumID != nil {
		filters["album_id"] = *params.AlbumID
	}

	if params.Format != "" {
		filters["format"] = params.Format
	}

	if params.MinSize != nil || params.MaxSize != nil {
		sizeFilter := make(map[string]interface{})
		if params.MinSize != nil {
			sizeFilter["min"] = *params.MinSize
		}
		if params.MaxSize != nil {
			sizeFilter["max"] = *params.MaxSize
		}
		filters["size"] = sizeFilter
	}

	if params.MinWidth != nil || params.MaxWidth != nil {
		widthFilter := make(map[string]interface{})
		if params.MinWidth != nil {
			widthFilter["min"] = *params.MinWidth
		}
		if params.MaxWidth != nil {
			widthFilter["max"] = *params.MaxWidth
		}
		filters["width"] = widthFilter
	}

	if params.MinHeight != nil || params.MaxHeight != nil {
		heightFilter := make(map[string]interface{})
		if params.MinHeight != nil {
			heightFilter["min"] = *params.MinHeight
		}
		if params.MaxHeight != nil {
			heightFilter["max"] = *params.MaxHeight
		}
		filters["height"] = heightFilter
	}

	if params.StartDate != nil || params.EndDate != nil {
		dateFilter := make(map[string]interface{})
		if params.StartDate != nil {
			dateFilter["start"] = *params.StartDate
		}
		if params.EndDate != nil {
			dateFilter["end"] = *params.EndDate
		}
		filters["date"] = dateFilter
	}

	if params.SortBy != "" {
		filters["sort_by"] = params.SortBy
	}

	if params.SortOrder != "" {
		filters["sort_order"] = strings.ToUpper(params.SortOrder)
	}

	return filters
}
