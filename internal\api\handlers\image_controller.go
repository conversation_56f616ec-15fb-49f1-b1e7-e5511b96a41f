package controllers

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"

	"cloudbed/internal/config"
	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"
	imageprocessor "cloudbed/pkg/image"
	"cloudbed/pkg/pagination"
	"cloudbed/pkg/response"
	"cloudbed/pkg/upload"

	"github.com/gin-gonic/gin"
)

// UploadImage 上传图片
func UploadImage(c *gin.Context, cfg *config.Config) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	file, err := c.FormFile("image")
	if err != nil {
		response.BadRequest(c, "请选择要上传的图片文件")
		return
	}

	// 验证文件
	if err := upload.ValidateImageFile(file); err != nil {
		response.BadRequestWithDetail(c, "文件验证失败", err.Error())
		return
	}

	// 检查存储配额
	fileSize := file.Size
	hasQuota, err := CheckStorageQuota(userID.(uint), fileSize)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "检查存储配额失败", err.Error())
		return
	}
	if !hasQuota {
		response.Forbidden(c, "存储配额已满，请清理文件或联系管理员")
		return
	}

	// 生成安全的文件名
	filename := upload.GenerateUniqueFilename(file.Filename)

	// 保存文件
	filePath := "./uploads/" + filename
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		response.InternalServerErrorWithDetail(c, "保存文件失败", err.Error())
		return
	}

	// 获取相册ID（可选）
	var albumID *uint
	if albumIDStr := c.PostForm("album_id"); albumIDStr != "" {
		if id, err := strconv.ParseUint(albumIDStr, 10, 32); err == nil {
			albumIDUint := uint(id)
			albumID = &albumIDUint

			// 验证相册是否属于当前用户
			if owned, err := dao.CheckAlbumOwnership(*albumID, userID.(uint)); err != nil || !owned {
				response.Forbidden(c, "相册不存在或无权访问")
				return
			}
		}
	} else {
		// 如果没有指定相册，尝试使用用户的默认相册
		user, err := dao.GetUserWithDefaultAlbum(userID.(uint))
		if err == nil && user.DefaultAlbumID != nil {
			albumID = user.DefaultAlbumID
		}
	}

	// 获取默认存储配置的访问URL
	defaultConfig, err := dao.GetDefaultStorageConfig()
	var baseURL string
	if err == nil && defaultConfig.AccessURL != "" {
		// 使用存储配置中设置的访问URL
		baseURL = defaultConfig.AccessURL
	} else {
		// 如果没有配置，则使用动态生成的URL
		baseURL = config.GetBaseURL(c.Request.Host, c.Request.TLS != nil, cfg)
	}

	// 获取图片信息
	processor := imageprocessor.NewProcessor(imageprocessor.DefaultConfig())
	imageInfo, err := processor.GetImageInfo(filePath)
	if err != nil {
		log.Printf("Failed to get image info: %v", err)
		// 继续处理，不阻止上传
	}

	// 生成缩略图
	var thumbnailInfo *models.ThumbnailInfo
	if imageInfo != nil && imageprocessor.IsValidImageFormat(filename) {
		result, err := processor.GenerateThumbnails(filePath)
		if err != nil {
			log.Printf("Failed to generate thumbnails: %v", err)
		} else {
			// 转换缩略图路径为URL
			thumbnailInfo = &models.ThumbnailInfo{}
			for sizeName, thumbnailPath := range result.Thumbnails {
				// 将本地路径转换为URL
				relativePath := strings.Replace(thumbnailPath, "./uploads/", "", 1)
				thumbnailURL := fmt.Sprintf("%s/uploads/%s", baseURL, relativePath)

				switch sizeName {
				case "small":
					thumbnailInfo.Small = thumbnailURL
				case "medium":
					thumbnailInfo.Medium = thumbnailURL
				case "large":
					thumbnailInfo.Large = thumbnailURL
				case "xlarge":
					thumbnailInfo.XLarge = thumbnailURL
				}
			}
		}
	}

	// 创建图片记录
	imageRecord := models.Image{
		Name:    filename,
		URL:     fmt.Sprintf("%s/uploads/%s", baseURL, filename),
		Size:    file.Size,
		UserID:  userID.(uint),
		AlbumID: albumID,
	}

	// 设置图片信息
	if imageInfo != nil {
		imageRecord.Width = imageInfo.Width
		imageRecord.Height = imageInfo.Height
		imageRecord.Format = imageInfo.Format
	}

	// 设置缩略图信息
	if thumbnailInfo != nil {
		if err := imageRecord.SetThumbnails(thumbnailInfo); err != nil {
			log.Printf("Failed to set thumbnails: %v", err)
		}
	}

	if err := dao.CreateImage(&imageRecord); err != nil {
		response.InternalServerErrorWithDetail(c, "保存图片记录失败", err.Error())
		return
	}

	// 更新用户存储使用量
	if err := UpdateStorageUsage(userID.(uint), file.Size); err != nil {
		log.Printf("Failed to update storage usage for user %d: %v", userID.(uint), err)
		// 不返回错误，因为图片已经上传成功
	}

	response.SuccessWithMessage(c, "图片上传成功", gin.H{
		"url":        imageRecord.URL,
		"name":       imageRecord.Name,
		"image":      imageRecord,
		"thumbnails": imageRecord.GetThumbnails(),
	})
}

// GetImages 获取图片列表（分页）
func GetImages(c *gin.Context, cfg *config.Config) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 解析分页参数
	req := pagination.ParseImageRequest(c)

	// 特殊处理未分类图片
	if c.Query("album_id") == "uncategorized" {
		result, err := dao.GetUncategorizedImagesByUserIDPaginated(userID.(uint), req.Request)
		if err != nil {
			response.InternalServerErrorWithDetail(c, "获取未分类图片失败", err.Error())
			return
		}
		response.Success(c, result)
		return
	}

	// 如果指定了相册ID，验证相册权限
	if req.AlbumID != nil {
		if owned, err := dao.CheckAlbumOwnership(*req.AlbumID, userID.(uint)); err != nil || !owned {
			response.Forbidden(c, "相册不存在或无权访问")
			return
		}
	}

	// 分页获取图片
	result, err := dao.GetImagesByUserIDPaginated(userID.(uint), req)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取图片列表失败", err.Error())
		return
	}

	response.Success(c, result)
}

// DeleteImage 删除图片
func DeleteImage(c *gin.Context, cfg *config.Config) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	imageIDStr := c.Param("id")

	// 尝试按ID删除
	if imageID, err := strconv.ParseUint(imageIDStr, 10, 32); err == nil {
		// 检查图片是否属于当前用户
		image, err := dao.GetImageByIDAndUserID(uint(imageID), userID.(uint))
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Image not found"})
			return
		}

		// 删除文件和缩略图
		filePath := "./uploads/" + image.Name
		if err := removeFile(filePath); err != nil {
			response.InternalServerErrorWithDetail(c, "删除文件失败", err.Error())
			return
		}

		// 清理缩略图
		processor := imageprocessor.NewProcessor(imageprocessor.DefaultConfig())
		if err := processor.CleanupThumbnails(filePath); err != nil {
			log.Printf("Failed to cleanup thumbnails for %s: %v", filePath, err)
			// 不阻止删除操作
		}

		// 删除数据库记录
		if err := dao.DeleteImage(uint(imageID)); err != nil {
			response.InternalServerErrorWithDetail(c, "删除图片记录失败", err.Error())
			return
		}

		// 更新用户存储使用量
		if err := UpdateStorageUsage(userID.(uint), -image.Size); err != nil {
			log.Printf("Failed to update storage usage for user %d: %v", userID.(uint), err)
			// 不返回错误，因为图片已经删除成功
		}

		response.SuccessWithMessage(c, "图片删除成功", nil)
		return
	} else {
		// 按文件名删除（兼容旧版本）
		filename := imageIDStr

		// 检查图片是否属于当前用户
		image, err := dao.GetImageByName(filename)
		if err != nil || image.UserID != userID.(uint) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Image not found"})
			return
		}

		// 删除文件
		if err := removeFile("./uploads/" + filename); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete file"})
			return
		}

		// 删除数据库记录
		if err := dao.DeleteImageByName(filename); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete image record"})
			return
		}

		// 更新用户存储使用量
		if err := UpdateStorageUsage(userID.(uint), -image.Size); err != nil {
			log.Printf("Failed to update storage usage for user %d: %v", userID.(uint), err)
			// 不返回错误，因为图片已经删除成功
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Image deleted successfully",
	})
}

// MoveImageToAlbum 移动图片到相册
func MoveImageToAlbum(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	imageIDStr := c.Param("id")
	imageID, err := strconv.ParseUint(imageIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid image ID"})
		return
	}

	var req struct {
		AlbumID *uint `json:"album_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查图片是否属于当前用户
	_, err = dao.GetImageByIDAndUserID(uint(imageID), userID.(uint))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Image not found"})
		return
	}

	// 如果指定了相册ID，检查相册是否属于当前用户
	if req.AlbumID != nil {
		owned, err := dao.CheckAlbumOwnership(*req.AlbumID, userID.(uint))
		if err != nil || !owned {
			c.JSON(http.StatusForbidden, gin.H{"error": "Album not found or access denied"})
			return
		}
	}

	// 移动图片到相册
	if err := dao.MoveImageToAlbum(uint(imageID), req.AlbumID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to move image"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Image moved successfully",
	})
}

// removeFile 删除文件的辅助函数
func removeFile(filepath string) error {
	return os.Remove(filepath)
}
