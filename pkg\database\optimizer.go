package database

import (
	"fmt"
	"log"

	"gorm.io/gorm"
)

// QueryOptimizer 数据库查询优化器
type QueryOptimizer struct {
	db *gorm.DB
}

// NewQueryOptimizer 创建查询优化器
func NewQueryOptimizer(db *gorm.DB) *QueryOptimizer {
	return &QueryOptimizer{db: db}
}

// OptimizePreloads 优化预加载策略
func (o *QueryOptimizer) OptimizePreloads(query *gorm.DB, preloads []string) *gorm.DB {
	for _, preload := range preloads {
		query = query.Preload(preload)
	}
	return query
}

// BatchPreload 批量预加载，避免N+1查询
func (o *QueryOptimizer) BatchPreload(query *gorm.DB, associations map[string]interface{}) *gorm.DB {
	for association, condition := range associations {
		if condition == nil {
			query = query.Preload(association)
		} else {
			query = query.Preload(association, condition)
		}
	}
	return query
}

// SelectOptimized 优化字段选择，只查询需要的字段
func (o *QueryOptimizer) SelectOptimized(query *gorm.DB, fields []string) *gorm.DB {
	if len(fields) > 0 {
		return query.Select(fields)
	}
	return query
}

// AddIndexHints 添加索引提示（MySQL特定）
func (o *QueryOptimizer) AddIndexHints(query *gorm.DB, table string, indexes []string) *gorm.DB {
	if len(indexes) > 0 {
		for _, index := range indexes {
			query = query.Table(fmt.Sprintf("%s USE INDEX (%s)", table, index))
		}
	}
	return query
}

// ExplainQuery 分析查询执行计划
func (o *QueryOptimizer) ExplainQuery(query *gorm.DB) error {
	sql := query.ToSQL()
	log.Printf("Executing query: %s", sql)
	
	// 执行EXPLAIN分析
	var result []map[string]interface{}
	err := o.db.Raw("EXPLAIN " + sql).Scan(&result).Error
	if err != nil {
		return err
	}
	
	log.Printf("Query execution plan: %+v", result)
	return nil
}

// OptimizedImageQuery 优化的图片查询构建器
type OptimizedImageQuery struct {
	db    *gorm.DB
	query *gorm.DB
}

// NewOptimizedImageQuery 创建优化的图片查询
func NewOptimizedImageQuery(db *gorm.DB) *OptimizedImageQuery {
	return &OptimizedImageQuery{
		db:    db,
		query: db.Model(&struct{}{}), // 临时模型
	}
}

// WithUserID 添加用户ID过滤
func (q *OptimizedImageQuery) WithUserID(userID uint) *OptimizedImageQuery {
	q.query = q.query.Where("user_id = ?", userID)
	return q
}

// WithAlbumID 添加相册ID过滤
func (q *OptimizedImageQuery) WithAlbumID(albumID *uint) *OptimizedImageQuery {
	if albumID == nil {
		q.query = q.query.Where("album_id IS NULL")
	} else {
		q.query = q.query.Where("album_id = ?", *albumID)
	}
	return q
}

// WithSearch 添加搜索条件
func (q *OptimizedImageQuery) WithSearch(search string) *OptimizedImageQuery {
	if search != "" {
		q.query = q.query.Where("name LIKE ?", "%"+search+"%")
	}
	return q
}

// WithPreloads 添加预加载
func (q *OptimizedImageQuery) WithPreloads(minimal bool) *OptimizedImageQuery {
	if minimal {
		// 最小预加载，只加载必要的关联
		q.query = q.query.Preload("Album", "id, name, cover_image")
	} else {
		// 完整预加载
		q.query = q.query.Preload("User", "id, username, email").
			Preload("User.Role", "id, name, display_name").
			Preload("Album", "id, name, description, cover_image")
	}
	return q
}

// Build 构建最终查询
func (q *OptimizedImageQuery) Build() *gorm.DB {
	return q.query
}

// OptimizedAlbumQuery 优化的相册查询构建器
type OptimizedAlbumQuery struct {
	db    *gorm.DB
	query *gorm.DB
}

// NewOptimizedAlbumQuery 创建优化的相册查询
func NewOptimizedAlbumQuery(db *gorm.DB) *OptimizedAlbumQuery {
	return &OptimizedAlbumQuery{
		db:    db,
		query: db.Model(&struct{}{}), // 临时模型
	}
}

// WithUserID 添加用户ID过滤
func (q *OptimizedAlbumQuery) WithUserID(userID uint) *OptimizedAlbumQuery {
	q.query = q.query.Where("user_id = ?", userID)
	return q
}

// WithSearch 添加搜索条件
func (q *OptimizedAlbumQuery) WithSearch(search string) *OptimizedAlbumQuery {
	if search != "" {
		q.query = q.query.Where("name LIKE ? OR description LIKE ?", "%"+search+"%", "%"+search+"%")
	}
	return q
}

// WithImageCount 添加图片数量统计（使用子查询避免N+1）
func (q *OptimizedAlbumQuery) WithImageCount() *OptimizedAlbumQuery {
	q.query = q.query.Select("albums.*, (SELECT COUNT(*) FROM images WHERE images.album_id = albums.id) as image_count")
	return q
}

// WithPreloads 添加预加载
func (q *OptimizedAlbumQuery) WithPreloads(includeImages bool) *OptimizedAlbumQuery {
	if includeImages {
		// 预加载图片，但限制数量
		q.query = q.query.Preload("Images", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, name, url, album_id").Limit(5).Order("created_at DESC")
		})
	}
	return q
}

// Build 构建最终查询
func (q *OptimizedAlbumQuery) Build() *gorm.DB {
	return q.query
}

// BatchOperations 批量操作优化
type BatchOperations struct {
	db *gorm.DB
}

// NewBatchOperations 创建批量操作
func NewBatchOperations(db *gorm.DB) *BatchOperations {
	return &BatchOperations{db: db}
}

// BatchUpdateStorageUsed 批量更新用户存储使用量
func (b *BatchOperations) BatchUpdateStorageUsed() error {
	// 使用单个SQL语句更新所有用户的存储使用量
	sql := `
		UPDATE users 
		SET storage_used = (
			SELECT COALESCE(SUM(size), 0) 
			FROM images 
			WHERE images.user_id = users.id
		)
	`
	return b.db.Exec(sql).Error
}

// BatchUpdateAlbumImageCount 批量更新相册图片数量
func (b *BatchOperations) BatchUpdateAlbumImageCount() error {
	// 如果相册表有image_count字段的话
	sql := `
		UPDATE albums 
		SET image_count = (
			SELECT COUNT(*) 
			FROM images 
			WHERE images.album_id = albums.id
		)
	`
	return b.db.Exec(sql).Error
}

// GetUserStorageStats 获取用户存储统计（优化版本）
func (b *BatchOperations) GetUserStorageStats(userID uint) (map[string]interface{}, error) {
	var result map[string]interface{}
	
	sql := `
		SELECT 
			u.id,
			u.username,
			u.email,
			u.storage_quota,
			u.storage_used,
			(u.storage_quota - u.storage_used) as remaining_quota,
			COUNT(DISTINCT i.id) as image_count,
			COUNT(DISTINCT a.id) as album_count
		FROM users u
		LEFT JOIN images i ON u.id = i.user_id
		LEFT JOIN albums a ON u.id = a.user_id
		WHERE u.id = ?
		GROUP BY u.id, u.username, u.email, u.storage_quota, u.storage_used
	`
	
	err := b.db.Raw(sql, userID).Scan(&result).Error
	return result, err
}

// GetAlbumWithStats 获取相册及其统计信息（优化版本）
func (b *BatchOperations) GetAlbumWithStats(userID uint) ([]map[string]interface{}, error) {
	var results []map[string]interface{}
	
	sql := `
		SELECT 
			a.id,
			a.name,
			a.description,
			a.cover_image,
			a.created_at,
			a.updated_at,
			COUNT(i.id) as image_count,
			COALESCE(SUM(i.size), 0) as total_size
		FROM albums a
		LEFT JOIN images i ON a.id = i.album_id
		WHERE a.user_id = ?
		GROUP BY a.id, a.name, a.description, a.cover_image, a.created_at, a.updated_at
		ORDER BY a.created_at DESC
	`
	
	err := b.db.Raw(sql, userID).Scan(&results).Error
	return results, err
}
