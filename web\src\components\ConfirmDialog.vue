<template>
  <div v-if="show" class="confirm-overlay" @click="handleOverlayClick">
    <div class="confirm-dialog" @click.stop>
      <div class="confirm-header">
        <div class="confirm-icon" :class="iconClass">
          <i :class="iconName"></i>
        </div>
        <h3 class="confirm-title">{{ title }}</h3>
      </div>
      
      <div class="confirm-body">
        <p class="confirm-message">{{ message }}</p>
        <div v-if="details" class="confirm-details">
          {{ details }}
        </div>
      </div>
      
      <div class="confirm-actions">
        <button 
          class="btn btn-secondary" 
          @click="handleCancel"
          :disabled="loading"
        >
          {{ cancelText }}
        </button>
        <button 
          class="btn" 
          :class="confirmButtonClass"
          @click="handleConfirm"
          :disabled="loading"
        >
          <i v-if="loading" class="bi bi-arrow-clockwise spinning"></i>
          {{ loading ? loadingText : confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  show: boolean;
  title?: string;
  message: string;
  details?: string;
  type?: 'warning' | 'danger' | 'info' | 'success';
  confirmText?: string;
  cancelText?: string;
  loadingText?: string;
  loading?: boolean;
  closeOnOverlay?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '确认操作',
  type: 'warning',
  confirmText: '确认',
  cancelText: '取消',
  loadingText: '处理中...',
  loading: false,
  closeOnOverlay: true
});

const emit = defineEmits<{
  confirm: [];
  cancel: [];
}>();

const iconClass = computed(() => {
  const classes = {
    warning: 'icon-warning',
    danger: 'icon-danger',
    info: 'icon-info',
    success: 'icon-success'
  };
  return classes[props.type];
});

const iconName = computed(() => {
  const icons = {
    warning: 'bi bi-exclamation-triangle-fill',
    danger: 'bi bi-x-circle-fill',
    info: 'bi bi-info-circle-fill',
    success: 'bi bi-check-circle-fill'
  };
  return icons[props.type];
});

const confirmButtonClass = computed(() => {
  const classes = {
    warning: 'btn-warning',
    danger: 'btn-danger',
    info: 'btn-primary',
    success: 'btn-success'
  };
  return classes[props.type];
});

const handleConfirm = () => {
  emit('confirm');
};

const handleCancel = () => {
  emit('cancel');
};

const handleOverlayClick = () => {
  if (props.closeOnOverlay && !props.loading) {
    emit('cancel');
  }
};
</script>

<style scoped>
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.confirm-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 480px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: confirmSlideIn 0.3s ease-out;
}

@keyframes confirmSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.confirm-header {
  padding: 24px 24px 16px;
  text-align: center;
}

.confirm-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  font-size: 28px;
}

.icon-warning {
  background-color: #fff3cd;
  color: #856404;
}

.icon-danger {
  background-color: #f8d7da;
  color: #721c24;
}

.icon-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.icon-success {
  background-color: #d4edda;
  color: #155724;
}

.confirm-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.confirm-body {
  padding: 0 24px 24px;
  text-align: center;
}

.confirm-message {
  margin: 0 0 12px;
  font-size: 16px;
  color: #555;
  line-height: 1.5;
}

.confirm-details {
  font-size: 14px;
  color: #777;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #dee2e6;
}

.confirm-actions {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 80px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover:not(:disabled) {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #218838;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
