<template>
  <div class="dashboard-container">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <i class="bi bi-images" style="font-size: 1.5rem; color: white;"></i>
          <span class="logo-text" :class="{ hidden: sidebarCollapsed }">图床应用</span>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <router-link
          to="/dashboard"
          class="nav-item"
          :class="{ active: $route.name === 'Dashboard' }"
        >
          <i class="bi bi-speedometer2"></i>
          <span :class="{ hidden: sidebarCollapsed }">仪表盘</span>
        </router-link>

        <!-- 我的分组 -->
        <div class="nav-group">
          <div class="nav-group-title" :class="{ hidden: sidebarCollapsed }">
            <i class="bi bi-person-circle"></i>
            <span>我的</span>
          </div>

          <router-link
            to="/user/photos"
            class="nav-item nav-sub-item"
            :class="{ active: $route.name === 'UserPhotos' }"
          >
            <i class="bi bi-images"></i>
            <span :class="{ hidden: sidebarCollapsed }">我的图片</span>
          </router-link>

          <router-link
            to="/user/albums"
            class="nav-item nav-sub-item"
            :class="{ active: $route.name === 'UserAlbums' || $route.name === 'AlbumImages' }"
          >
            <i class="bi bi-folder"></i>
            <span :class="{ hidden: sidebarCollapsed }">我的相册</span>
          </router-link>

          <router-link
            to="/user/profile"
            class="nav-item nav-sub-item"
            :class="{ active: $route.name === 'UserProfile' }"
          >
            <i class="bi bi-person"></i>
            <span :class="{ hidden: sidebarCollapsed }">个人资料</span>
          </router-link>

          <router-link
            to="/user/settings"
            class="nav-item nav-sub-item"
            :class="{ active: $route.name === 'UserSettings' }"
          >
            <i class="bi bi-gear"></i>
            <span :class="{ hidden: sidebarCollapsed }">设置</span>
          </router-link>
        </div>

        <!-- 管理分组 -->
        <div class="nav-group" v-if="user?.role?.name === 'admin' || user?.role?.name === 'super_admin'">
          <div class="nav-group-title" :class="{ hidden: sidebarCollapsed }">
            <i class="bi bi-shield-check"></i>
            <span>管理</span>
          </div>

          <router-link
            to="/admin/users"
            class="nav-item nav-sub-item"
            :class="{ active: $route.path.startsWith('/admin/users') }"
          >
            <i class="bi bi-people"></i>
            <span :class="{ hidden: sidebarCollapsed }">用户管理</span>
          </router-link>

          <router-link
            to="/admin/groups"
            class="nav-item nav-sub-item"
            :class="{ active: $route.path.startsWith('/admin/groups') }"
          >
            <i class="bi bi-people-fill"></i>
            <span :class="{ hidden: sidebarCollapsed }">角色组管理</span>
          </router-link>

          <router-link
            to="/admin/images"
            class="nav-item nav-sub-item"
            :class="{ active: $route.path.startsWith('/admin/images') }"
          >
            <i class="bi bi-images"></i>
            <span :class="{ hidden: sidebarCollapsed }">图片管理</span>
          </router-link>

          <router-link
            to="/admin/roles"
            class="nav-item nav-sub-item"
            :class="{ active: $route.path.startsWith('/admin/roles') }"
          >
            <i class="bi bi-key"></i>
            <span :class="{ hidden: sidebarCollapsed }">角色权限</span>
          </router-link>

          <router-link
            to="/admin/storage"
            class="nav-item nav-sub-item"
            :class="{ active: $route.name === 'AdminStorage' }"
          >
            <i class="bi bi-hdd-stack"></i>
            <span :class="{ hidden: sidebarCollapsed }">存储管理</span>
          </router-link>

          <router-link
            to="/settings"
            class="nav-item nav-sub-item"
            :class="{ active: $route.name === 'Settings' }"
          >
            <i class="bi bi-gear"></i>
            <span :class="{ hidden: sidebarCollapsed }">系统设置</span>
          </router-link>
        </div>
      </nav>

      <div class="storage-info" :class="{ collapsed: sidebarCollapsed }">
        <h6>
          <i class="bi bi-hdd"></i>
          <span :class="{ hidden: sidebarCollapsed }">存储信息</span>
        </h6>
        <div class="progress">
          <div
            class="progress-bar"
            :style="{ width: storageUsage + '%' }"
          ></div>
        </div>
        <div class="storage-text" :class="{ hidden: sidebarCollapsed }">
          已使用 {{ usedStorage }} / {{ totalStorage }}
        </div>
      </div>
    </aside>
    
    <!-- 主内容区 -->
    <main class="main-content">
      <header class="main-header">
        <div class="header-left">
          <button class="menu-toggle" @click="toggleSidebar">
            <i class="bi bi-list"></i>
          </button>
        </div>
        <div class="user-info">
          <div class="user-avatar">
            <i class="bi bi-person-circle"></i>
          </div>
          <div class="user-details" :class="{ hidden: sidebarCollapsed }">
            <div class="user-name">{{ user?.username || user?.email }}</div>
            <div class="user-email">{{ user?.email }}</div>
          </div>
          <button @click="handleLogout" class="logout-btn" title="登出">
            <i class="bi bi-box-arrow-right"></i>
          </button>
        </div>
      </header>
      
      <div class="content">
        <router-view />
      </div>
    </main>

    <!-- 全局拖拽上传组件 -->
    <GlobalDragUpload />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { useStorageStore } from '../stores/storage';
import { formatBytes } from '../types/storage';
import GlobalDragUpload from '../components/GlobalDragUpload.vue';
import { useDialog } from '../utils/dialog';

const authStore = useAuthStore();
const storageStore = useStorageStore();
const router = useRouter();
const sidebarCollapsed = ref(false);
const { confirm } = useDialog();

// 计算属性
const user = computed(() => authStore.getUser);
const storageUsage = computed(() => {
  if (!storageStore.storageInfo) return 0;
  return Math.min(storageStore.storageInfo.usage_percent, 100);
});
const usedStorage = computed(() => {
  if (!storageStore.storageInfo) return '0 B';
  return formatBytes(storageStore.storageInfo.storage_used);
});
const totalStorage = computed(() => {
  if (!storageStore.storageInfo) return '0 B';
  return formatBytes(storageStore.storageInfo.storage_quota);
});

// 生命周期
onMounted(() => {
  storageStore.loadStorageInfo();
});

// 处理登出
const handleLogout = async () => {
  const confirmed = await confirm('确定要登出吗？', {
    title: '退出登录',
    type: 'warning',
    confirmText: '登出',
    cancelText: '取消'
  });

  if (confirmed) {
    await authStore.logout();
    router.push('/auth');
  }
};

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};
</script>

<style scoped>
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-color);
}

.sidebar {
  width: 250px;
  background: var(--sidebar-bg);
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  color: white;
  z-index: 100;
  border-right: 1px solid var(--border);
  flex-shrink: 0;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-text {
  font-size: 1.2rem;
  font-weight: 600;
  white-space: nowrap;
  transition: opacity 0.3s;
}

.sidebar-nav {
  flex: 1;
  padding: 10px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
  margin: 5px 10px;
  border-radius: 8px;
  white-space: nowrap;
}

.nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.nav-item i {
  margin-right: 12px;
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.nav-item span {
  transition: opacity 0.3s;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 导航分组样式 */
.nav-group {
  margin-bottom: 20px;
}

.nav-group-title {
  display: flex;
  align-items: center;
  padding: 8px 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 5px;
}

.nav-group-title i {
  width: 16px;
  margin-right: 8px;
  font-size: 12px;
}

.nav-group-title.hidden span {
  display: none;
}

.nav-sub-item {
  padding-left: 40px;
  font-size: 14px;
}

.nav-sub-item i {
  width: 16px;
  margin-right: 10px;
  font-size: 14px;
}

.storage-info {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.storage-info h6 {
  margin: 0 0 10px 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.storage-info h6 i {
  margin-right: 8px;
  flex-shrink: 0;
}

.storage-info h6 span {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: opacity 0.3s;
}

.progress {
  height: 10px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  transition: width 0.3s;
}

.storage-text {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: opacity 0.3s;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0; /* 允许内容区域收缩 */
}

.main-header {
  padding: 15px 20px;
  background-color: var(--header-bg);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
}

.menu-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-color);
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s;
}

.menu-toggle:hover {
  background-color: var(--card-bg);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
  min-width: 0; /* 允许用户信息区域收缩 */
}

.user-avatar {
  font-size: 1.8rem;
  color: var(--text-color);
  flex-shrink: 0;
}

.user-details {
  text-align: right;
  min-width: 0; /* 允许文本收缩 */
  transition: opacity 0.3s;
}

.user-email {
  font-weight: 500;
  font-size: 0.9rem;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-name {
  font-size: 0.8rem;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.logout-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s;
  flex-shrink: 0;
}

.logout-btn:hover {
  background-color: var(--card-bg);
  color: #dc3545;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 70px);
  background-color: var(--bg-color);
}

/* 隐藏类 */
.hidden {
  opacity: 0;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  border: 0;
  visibility: hidden;
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    height: 100vh;
    transform: translateX(0);
  }
  
  .sidebar.collapsed {
    transform: translateX(-100%);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .user-details {
    display: none;
  }
}

/* 针对浏览器缩放的优化 */
@media screen and (-webkit-min-device-pixel-ratio: 1.25), 
       screen and (min-resolution: 120dpi) {
  .sidebar {
    min-width: 70px;
  }
  
  .sidebar:not(.collapsed) {
    min-width: 250px;
  }
}
</style>