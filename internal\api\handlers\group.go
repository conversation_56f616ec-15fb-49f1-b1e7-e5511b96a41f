package controllers

import (
	"net/http"
	"strconv"

	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"

	"github.com/gin-gonic/gin"
)

// CreateGroupRequest 创建用户组请求
type CreateGroupRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	DisplayName string `json:"display_name" binding:"required,min=2,max=100"`
	Description string `json:"description" binding:"max=500"`
	IsActive    *bool  `json:"is_active"`
	IsDefault   *bool  `json:"is_default"`

	// 上传限制配置
	MaxFileSize       *int64 `json:"max_file_size" binding:"omitempty,min=1,max=1048576"`     // 最大文件大小(KB) 1KB-1GB
	ConcurrentUploads *int   `json:"concurrent_uploads" binding:"omitempty,min=1,max=10"`     // 并发上传限制 1-10
	UploadLimitMinute *int   `json:"upload_limit_minute" binding:"omitempty,min=0,max=1000"`  // 每分钟上传限制
	UploadLimitHour   *int   `json:"upload_limit_hour" binding:"omitempty,min=0,max=10000"`   // 每小时上传限制
	UploadLimitDay    *int   `json:"upload_limit_day" binding:"omitempty,min=0,max=50000"`    // 每天上传限制
	UploadLimitWeek   *int   `json:"upload_limit_week" binding:"omitempty,min=0,max=200000"`  // 每周上传限制
	UploadLimitMonth  *int   `json:"upload_limit_month" binding:"omitempty,min=0,max=500000"` // 每月上传限制

	// 文件命名和路径配置
	PathNamingRule *string `json:"path_naming_rule" binding:"omitempty,max=100"` // 路径命名规则
	FileNamingRule *string `json:"file_naming_rule" binding:"omitempty,max=100"` // 文件命名规则

	// 图片处理配置
	ImageQuality      *int    `json:"image_quality" binding:"omitempty,min=1,max=100"`                   // 图片保存质量
	ImageFormat       *string `json:"image_format" binding:"omitempty,oneof=original jpg jpeg png webp"` // 图片转换格式
	AllowedImageTypes *string `json:"allowed_image_types" binding:"omitempty,max=200"`                   // 允许的图片类型
}

// UpdateGroupRequest 更新用户组请求
type UpdateGroupRequest struct {
	DisplayName string `json:"display_name" binding:"required,min=2,max=100"`
	Description string `json:"description" binding:"max=500"`
	IsActive    *bool  `json:"is_active"`
	IsDefault   *bool  `json:"is_default"`

	// 上传限制配置
	MaxFileSize       *int64 `json:"max_file_size" binding:"omitempty,min=1,max=1048576"`     // 最大文件大小(KB) 1KB-1GB
	ConcurrentUploads *int   `json:"concurrent_uploads" binding:"omitempty,min=1,max=10"`     // 并发上传限制 1-10
	UploadLimitMinute *int   `json:"upload_limit_minute" binding:"omitempty,min=0,max=1000"`  // 每分钟上传限制
	UploadLimitHour   *int   `json:"upload_limit_hour" binding:"omitempty,min=0,max=10000"`   // 每小时上传限制
	UploadLimitDay    *int   `json:"upload_limit_day" binding:"omitempty,min=0,max=50000"`    // 每天上传限制
	UploadLimitWeek   *int   `json:"upload_limit_week" binding:"omitempty,min=0,max=200000"`  // 每周上传限制
	UploadLimitMonth  *int   `json:"upload_limit_month" binding:"omitempty,min=0,max=500000"` // 每月上传限制

	// 文件命名和路径配置
	PathNamingRule *string `json:"path_naming_rule" binding:"omitempty,max=100"` // 路径命名规则
	FileNamingRule *string `json:"file_naming_rule" binding:"omitempty,max=100"` // 文件命名规则

	// 图片处理配置
	ImageQuality      *int    `json:"image_quality" binding:"omitempty,min=1,max=100"`                   // 图片保存质量
	ImageFormat       *string `json:"image_format" binding:"omitempty,oneof=original jpg jpeg png webp"` // 图片转换格式
	AllowedImageTypes *string `json:"allowed_image_types" binding:"omitempty,max=200"`                   // 允许的图片类型
}

// AddUserToGroupRequest 添加用户到组请求
type AddUserToGroupRequest struct {
	UserIDs []uint `json:"user_ids" binding:"required,min=1"`
}

// AssignGroupPermissionsRequest 分配组权限请求
type AssignGroupPermissionsRequest struct {
	PermissionIDs []uint `json:"permission_ids" binding:"required"`
}

// GetAllGroups 获取所有用户组
func GetAllGroups(c *gin.Context) {
	var groups []models.Group

	// 预加载关联数据
	result := database.DB.Preload("Permissions").Preload("Creator").Find(&groups)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户组列表失败"})
		return
	}

	// 获取每个组的成员数量
	type GroupWithStats struct {
		models.Group
		MemberCount int64 `json:"member_count"`
	}

	var groupsWithStats []GroupWithStats
	for _, group := range groups {
		var memberCount int64
		database.DB.Model(&models.UserGroup{}).Where("group_id = ?", group.ID).Count(&memberCount)

		groupsWithStats = append(groupsWithStats, GroupWithStats{
			Group:       group,
			MemberCount: memberCount,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"groups": groupsWithStats,
		"total":  len(groupsWithStats),
	})
}

// GetGroup 获取单个用户组详情
func GetGroup(c *gin.Context) {
	groupID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的组ID"})
		return
	}

	var group models.Group
	result := database.DB.Preload("Permissions").Preload("Users").Preload("Creator").First(&group, groupID)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户组不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"group": group})
}

// CreateGroup 创建用户组
func CreateGroup(c *gin.Context) {
	var req CreateGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}

	// 检查组名是否已存在
	var existingGroup models.Group
	if database.DB.Where("name = ?", req.Name).First(&existingGroup).Error == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "组名已存在"})
		return
	}

	// 设置默认值
	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	isDefault := false
	if req.IsDefault != nil {
		isDefault = *req.IsDefault
	}

	// 创建用户组
	group := models.Group{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		IsActive:    isActive,
		IsDefault:   isDefault,
		CreatedBy:   userID.(uint),

		// 设置默认配置值
		MaxFileSize:       10240,
		ConcurrentUploads: 3,
		UploadLimitMinute: 10,
		UploadLimitHour:   100,
		UploadLimitDay:    500,
		UploadLimitWeek:   2000,
		UploadLimitMonth:  5000,
		PathNamingRule:    "{Y}/{m}/{d}",
		FileNamingRule:    "{uniqid}",
		ImageQuality:      85,
		ImageFormat:       "original",
		AllowedImageTypes: "jpg,jpeg,png,gif,webp",
	}

	// 应用用户提供的配置
	if req.MaxFileSize != nil {
		group.MaxFileSize = *req.MaxFileSize
	}
	if req.ConcurrentUploads != nil {
		group.ConcurrentUploads = *req.ConcurrentUploads
	}
	if req.UploadLimitMinute != nil {
		group.UploadLimitMinute = *req.UploadLimitMinute
	}
	if req.UploadLimitHour != nil {
		group.UploadLimitHour = *req.UploadLimitHour
	}
	if req.UploadLimitDay != nil {
		group.UploadLimitDay = *req.UploadLimitDay
	}
	if req.UploadLimitWeek != nil {
		group.UploadLimitWeek = *req.UploadLimitWeek
	}
	if req.UploadLimitMonth != nil {
		group.UploadLimitMonth = *req.UploadLimitMonth
	}
	if req.PathNamingRule != nil {
		group.PathNamingRule = *req.PathNamingRule
	}
	if req.FileNamingRule != nil {
		group.FileNamingRule = *req.FileNamingRule
	}
	if req.ImageQuality != nil {
		group.ImageQuality = *req.ImageQuality
	}
	if req.ImageFormat != nil {
		group.ImageFormat = *req.ImageFormat
	}
	if req.AllowedImageTypes != nil {
		group.AllowedImageTypes = *req.AllowedImageTypes
	}

	if err := database.DB.Create(&group).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建用户组失败"})
		return
	}

	// 预加载创建者信息
	database.DB.Preload("Creator").First(&group, group.ID)

	c.JSON(http.StatusCreated, gin.H{
		"message": "用户组创建成功",
		"group":   group,
	})
}

// UpdateGroup 更新用户组
func UpdateGroup(c *gin.Context) {
	groupID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的组ID"})
		return
	}

	var req UpdateGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var group models.Group
	if database.DB.First(&group, groupID).Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户组不存在"})
		return
	}

	// 系统组只能修改配置，不能修改基本属性
	if !group.IsSystem {
		// 非系统组可以修改所有字段
		group.DisplayName = req.DisplayName
		group.Description = req.Description
		if req.IsActive != nil {
			group.IsActive = *req.IsActive
		}
		if req.IsDefault != nil {
			group.IsDefault = *req.IsDefault
		}
	} else {
		// 系统组只能修改描述
		if req.Description != "" {
			group.Description = req.Description
		}
	}

	// 更新配置字段
	if req.MaxFileSize != nil {
		group.MaxFileSize = *req.MaxFileSize
	}
	if req.ConcurrentUploads != nil {
		group.ConcurrentUploads = *req.ConcurrentUploads
	}
	if req.UploadLimitMinute != nil {
		group.UploadLimitMinute = *req.UploadLimitMinute
	}
	if req.UploadLimitHour != nil {
		group.UploadLimitHour = *req.UploadLimitHour
	}
	if req.UploadLimitDay != nil {
		group.UploadLimitDay = *req.UploadLimitDay
	}
	if req.UploadLimitWeek != nil {
		group.UploadLimitWeek = *req.UploadLimitWeek
	}
	if req.UploadLimitMonth != nil {
		group.UploadLimitMonth = *req.UploadLimitMonth
	}
	if req.PathNamingRule != nil {
		group.PathNamingRule = *req.PathNamingRule
	}
	if req.FileNamingRule != nil {
		group.FileNamingRule = *req.FileNamingRule
	}
	if req.ImageQuality != nil {
		group.ImageQuality = *req.ImageQuality
	}
	if req.ImageFormat != nil {
		group.ImageFormat = *req.ImageFormat
	}
	if req.AllowedImageTypes != nil {
		group.AllowedImageTypes = *req.AllowedImageTypes
	}

	if err := database.DB.Save(&group).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新用户组失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "用户组更新成功",
		"group":   group,
	})
}

// DeleteGroup 删除用户组
func DeleteGroup(c *gin.Context) {
	groupID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的组ID"})
		return
	}

	var group models.Group
	if database.DB.First(&group, groupID).Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户组不存在"})
		return
	}

	// 检查是否为系统组
	if group.IsSystem {
		c.JSON(http.StatusForbidden, gin.H{"error": "不能删除系统组"})
		return
	}

	// 开始事务
	tx := database.DB.Begin()

	// 删除组权限关联
	if err := tx.Where("group_id = ?", groupID).Delete(&models.GroupPermission{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除组权限失败"})
		return
	}

	// 删除用户组关联
	if err := tx.Where("group_id = ?", groupID).Delete(&models.UserGroup{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除用户组关联失败"})
		return
	}

	// 删除用户组
	if err := tx.Delete(&group).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除用户组失败"})
		return
	}

	tx.Commit()

	c.JSON(http.StatusOK, gin.H{"message": "用户组删除成功"})
}

// AddUsersToGroup 添加用户到组
func AddUsersToGroup(c *gin.Context) {
	groupID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的组ID"})
		return
	}

	var req AddUserToGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查组是否存在
	var group models.Group
	if database.DB.First(&group, groupID).Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户组不存在"})
		return
	}

	// 获取当前用户ID
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}

	// 开始事务
	tx := database.DB.Begin()

	var addedUsers []uint
	var skippedUsers []uint

	for _, userID := range req.UserIDs {
		// 检查用户是否存在
		var user models.User
		if tx.First(&user, userID).Error != nil {
			skippedUsers = append(skippedUsers, userID)
			continue
		}

		// 检查用户是否已在组中
		var existingRelation models.UserGroup
		if tx.Where("user_id = ? AND group_id = ?", userID, groupID).First(&existingRelation).Error == nil {
			skippedUsers = append(skippedUsers, userID)
			continue
		}

		// 添加用户到组
		userGroup := models.UserGroup{
			UserID:  userID,
			GroupID: uint(groupID),
			AddedBy: currentUserID.(uint),
		}

		if err := tx.Create(&userGroup).Error; err != nil {
			skippedUsers = append(skippedUsers, userID)
			continue
		}

		addedUsers = append(addedUsers, userID)
	}

	tx.Commit()

	c.JSON(http.StatusOK, gin.H{
		"message":       "用户添加完成",
		"added_users":   addedUsers,
		"skipped_users": skippedUsers,
		"added_count":   len(addedUsers),
		"skipped_count": len(skippedUsers),
	})
}

// RemoveUserFromGroup 从组中移除用户
func RemoveUserFromGroup(c *gin.Context) {
	groupID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的组ID"})
		return
	}

	userID, err := strconv.ParseUint(c.Param("user_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户ID"})
		return
	}

	// 删除用户组关联
	result := database.DB.Where("user_id = ? AND group_id = ?", userID, groupID).Delete(&models.UserGroup{})
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "移除用户失败"})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户不在该组中"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "用户移除成功"})
}

// AssignGroupPermissions 分配组权限
func AssignGroupPermissions(c *gin.Context) {
	groupID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的组ID"})
		return
	}

	var req AssignGroupPermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查组是否存在
	var group models.Group
	if database.DB.First(&group, groupID).Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户组不存在"})
		return
	}

	// 检查是否为系统组
	if group.IsSystem {
		c.JSON(http.StatusForbidden, gin.H{"error": "不能修改系统组权限"})
		return
	}

	// 开始事务
	tx := database.DB.Begin()

	// 删除现有权限
	if err := tx.Where("group_id = ?", groupID).Delete(&models.GroupPermission{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "清除现有权限失败"})
		return
	}

	// 添加新权限
	for _, permissionID := range req.PermissionIDs {
		// 检查权限是否存在
		var permission models.Permission
		if tx.First(&permission, permissionID).Error != nil {
			continue // 跳过不存在的权限
		}

		groupPermission := models.GroupPermission{
			GroupID:      uint(groupID),
			PermissionID: permissionID,
		}

		if err := tx.Create(&groupPermission).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "分配权限失败"})
			return
		}
	}

	tx.Commit()

	c.JSON(http.StatusOK, gin.H{"message": "权限分配成功"})
}

// GetGroupMembers 获取组成员
func GetGroupMembers(c *gin.Context) {
	groupID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的组ID"})
		return
	}

	// 检查组是否存在
	var group models.Group
	if database.DB.First(&group, groupID).Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户组不存在"})
		return
	}

	// 获取组成员
	var userGroups []models.UserGroup
	result := database.DB.Preload("User").Preload("User.Role").Preload("Adder").
		Where("group_id = ?", groupID).Find(&userGroups)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取组成员失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"group":   group,
		"members": userGroups,
		"total":   len(userGroups),
	})
}

// GetGroupPermissions 获取组权限
func GetGroupPermissions(c *gin.Context) {
	groupID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的组ID"})
		return
	}

	// 检查组是否存在
	var group models.Group
	if database.DB.Preload("Permissions").First(&group, groupID).Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户组不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"group":       group,
		"permissions": group.Permissions,
		"total":       len(group.Permissions),
	})
}

// GetGroupStats 获取组统计信息
func GetGroupStats(c *gin.Context) {
	var stats []models.GroupStats

	query := `
		SELECT
			g.id as group_id,
			g.display_name as group_name,
			COUNT(ug.user_id) as member_count,
			g.created_at
		FROM groups g
		LEFT JOIN user_groups ug ON g.id = ug.group_id
		WHERE g.is_active = true
		GROUP BY g.id, g.display_name, g.created_at
		ORDER BY member_count DESC
	`

	if err := database.DB.Raw(query).Scan(&stats).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取组统计失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"stats": stats,
		"total": len(stats),
	})
}

// SetDefaultGroup 设置默认用户组
func SetDefaultGroup(c *gin.Context) {
	groupID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的组ID"})
		return
	}

	// 检查组是否存在
	var group models.Group
	if database.DB.First(&group, groupID).Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户组不存在"})
		return
	}

	// 检查组是否激活
	if !group.IsActive {
		c.JSON(http.StatusBadRequest, gin.H{"error": "不能将非激活组设为默认组"})
		return
	}

	// 开始事务
	tx := database.DB.Begin()

	// 取消所有组的默认状态
	if err := tx.Model(&models.Group{}).Where("is_default = ?", true).Update("is_default", false).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新默认组状态失败"})
		return
	}

	// 设置新的默认组
	if err := tx.Model(&models.Group{}).Where("id = ?", groupID).Update("is_default", true).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "设置默认组失败"})
		return
	}

	tx.Commit()

	c.JSON(http.StatusOK, gin.H{
		"message": "默认用户组设置成功",
		"group":   group,
	})
}

// GetDefaultGroup 获取默认用户组
func GetDefaultGroup(c *gin.Context) {
	var group models.Group
	result := database.DB.Where("is_default = ? AND is_active = ?", true, true).First(&group)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "未找到默认用户组"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"group": group})
}
