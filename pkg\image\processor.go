package image

import (
	"fmt"
	"image"
	"image/gif"
	"image/jpeg"
	"image/png"
	"io"
	"os"
	"path/filepath"
	"strings"

	"golang.org/x/image/draw"
)

// ThumbnailSize 缩略图尺寸配置
type ThumbnailSize struct {
	Name   string `json:"name"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Suffix string `json:"suffix"`
}

// ProcessorConfig 图片处理器配置
type ProcessorConfig struct {
	ThumbnailSizes []ThumbnailSize `json:"thumbnail_sizes"`
	Quality        int             `json:"quality"`        // JPEG质量 (1-100)
	OutputFormat   string          `json:"output_format"`  // 输出格式: "auto", "jpeg", "png", "webp"
	MaxFileSize    int64           `json:"max_file_size"`  // 最大文件大小（字节）
	MaxDimension   int             `json:"max_dimension"`  // 最大尺寸限制
}

// DefaultThumbnailSizes 默认缩略图尺寸
var DefaultThumbnailSizes = []ThumbnailSize{
	{Name: "small", Width: 150, Height: 150, Suffix: "_150"},
	{Name: "medium", Width: 300, Height: 300, Suffix: "_300"},
	{Name: "large", Width: 600, Height: 600, Suffix: "_600"},
	{Name: "xlarge", Width: 1200, Height: 1200, Suffix: "_1200"},
}

// DefaultConfig 默认配置
func DefaultConfig() ProcessorConfig {
	return ProcessorConfig{
		ThumbnailSizes: DefaultThumbnailSizes,
		Quality:        85,
		OutputFormat:   "auto",
		MaxFileSize:    50 * 1024 * 1024, // 50MB
		MaxDimension:   4096,
	}
}

// Processor 图片处理器
type Processor struct {
	config ProcessorConfig
}

// NewProcessor 创建图片处理器
func NewProcessor(config ProcessorConfig) *Processor {
	return &Processor{config: config}
}

// ThumbnailResult 缩略图生成结果
type ThumbnailResult struct {
	OriginalPath string            `json:"original_path"`
	Thumbnails   map[string]string `json:"thumbnails"` // size_name -> file_path
	Error        error             `json:"error,omitempty"`
}

// GenerateThumbnails 生成缩略图
func (p *Processor) GenerateThumbnails(inputPath string) (*ThumbnailResult, error) {
	result := &ThumbnailResult{
		OriginalPath: inputPath,
		Thumbnails:   make(map[string]string),
	}

	// 打开原始图片
	file, err := os.Open(inputPath)
	if err != nil {
		return result, fmt.Errorf("failed to open image: %v", err)
	}
	defer file.Close()

	// 解码图片
	img, format, err := image.Decode(file)
	if err != nil {
		return result, fmt.Errorf("failed to decode image: %v", err)
	}

	// 检查图片尺寸
	bounds := img.Bounds()
	if bounds.Dx() > p.config.MaxDimension || bounds.Dy() > p.config.MaxDimension {
		return result, fmt.Errorf("image dimensions exceed maximum allowed size")
	}

	// 为每个尺寸生成缩略图
	for _, size := range p.config.ThumbnailSizes {
		thumbnailPath, err := p.generateSingleThumbnail(img, inputPath, size, format)
		if err != nil {
			result.Error = err
			continue
		}
		result.Thumbnails[size.Name] = thumbnailPath
	}

	return result, nil
}

// generateSingleThumbnail 生成单个缩略图
func (p *Processor) generateSingleThumbnail(img image.Image, originalPath string, size ThumbnailSize, originalFormat string) (string, error) {
	// 计算缩略图尺寸（保持宽高比）
	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	// 计算缩放比例
	scaleX := float64(size.Width) / float64(originalWidth)
	scaleY := float64(size.Height) / float64(originalHeight)
	scale := scaleX
	if scaleY < scaleX {
		scale = scaleY
	}

	// 如果原图比目标尺寸小，不进行放大
	if scale > 1.0 {
		scale = 1.0
	}

	newWidth := int(float64(originalWidth) * scale)
	newHeight := int(float64(originalHeight) * scale)

	// 创建缩略图
	thumbnail := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))
	draw.CatmullRom.Scale(thumbnail, thumbnail.Bounds(), img, bounds, draw.Over, nil)

	// 生成输出文件路径
	outputPath := p.generateThumbnailPath(originalPath, size.Suffix)

	// 确保输出目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create output directory: %v", err)
	}

	// 保存缩略图
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	// 根据配置选择输出格式
	outputFormat := p.config.OutputFormat
	if outputFormat == "auto" {
		outputFormat = originalFormat
	}

	err = p.encodeImage(outputFile, thumbnail, outputFormat)
	if err != nil {
		return "", fmt.Errorf("failed to encode thumbnail: %v", err)
	}

	return outputPath, nil
}

// generateThumbnailPath 生成缩略图文件路径
func (p *Processor) generateThumbnailPath(originalPath, suffix string) string {
	dir := filepath.Dir(originalPath)
	filename := filepath.Base(originalPath)
	ext := filepath.Ext(filename)
	nameWithoutExt := strings.TrimSuffix(filename, ext)

	// 创建thumbnails子目录
	thumbnailDir := filepath.Join(dir, "thumbnails")
	
	return filepath.Join(thumbnailDir, nameWithoutExt+suffix+ext)
}

// encodeImage 编码图片
func (p *Processor) encodeImage(w io.Writer, img image.Image, format string) error {
	switch strings.ToLower(format) {
	case "jpeg", "jpg":
		return jpeg.Encode(w, img, &jpeg.Options{Quality: p.config.Quality})
	case "png":
		return png.Encode(w, img)
	case "gif":
		return gif.Encode(w, img, nil)
	default:
		// 默认使用JPEG
		return jpeg.Encode(w, img, &jpeg.Options{Quality: p.config.Quality})
	}
}

// ResizeImage 调整图片大小
func (p *Processor) ResizeImage(inputPath, outputPath string, width, height int) error {
	// 打开原始图片
	file, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open image: %v", err)
	}
	defer file.Close()

	// 解码图片
	img, format, err := image.Decode(file)
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 创建调整后的图片
	resized := image.NewRGBA(image.Rect(0, 0, width, height))
	draw.CatmullRom.Scale(resized, resized.Bounds(), img, img.Bounds(), draw.Over, nil)

	// 确保输出目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 保存调整后的图片
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	return p.encodeImage(outputFile, resized, format)
}

// GetImageInfo 获取图片信息
func (p *Processor) GetImageInfo(imagePath string) (*ImageInfo, error) {
	file, err := os.Open(imagePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open image: %v", err)
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %v", err)
	}

	// 解码图片获取尺寸和格式
	config, format, err := image.DecodeConfig(file)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image config: %v", err)
	}

	return &ImageInfo{
		Width:    config.Width,
		Height:   config.Height,
		Format:   format,
		Size:     fileInfo.Size(),
		Filename: filepath.Base(imagePath),
	}, nil
}

// ImageInfo 图片信息
type ImageInfo struct {
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	Format   string `json:"format"`
	Size     int64  `json:"size"`
	Filename string `json:"filename"`
}

// IsValidImageFormat 检查是否为有效的图片格式
func IsValidImageFormat(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
	
	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

// CleanupThumbnails 清理缩略图文件
func (p *Processor) CleanupThumbnails(originalPath string) error {
	thumbnailDir := filepath.Join(filepath.Dir(originalPath), "thumbnails")
	filename := filepath.Base(originalPath)
	nameWithoutExt := strings.TrimSuffix(filename, filepath.Ext(filename))

	// 删除所有相关的缩略图
	for _, size := range p.config.ThumbnailSizes {
		thumbnailPath := filepath.Join(thumbnailDir, nameWithoutExt+size.Suffix+filepath.Ext(filename))
		if err := os.Remove(thumbnailPath); err != nil && !os.IsNotExist(err) {
			return fmt.Errorf("failed to remove thumbnail %s: %v", thumbnailPath, err)
		}
	}

	return nil
}
