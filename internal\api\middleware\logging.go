package middleware

import (
	"time"

	"cloudbed/pkg/logger"

	"github.com/gin-gonic/gin"
)

// LoggingMiddleware 日志中间件
func LoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		start := time.Now()

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(start)

		// 获取用户ID（如果存在）
		var userID uint
		if uid, exists := c.Get("user_id"); exists {
			if id, ok := uid.(uint); ok {
				userID = id
			}
		}

		// 记录API请求日志
		logger.LogAPIRequest(
			c.Request.Method,
			c.Request.URL.Path,
			userID,
			c.Writer.Status(),
			duration,
		)

		// 如果有错误，记录错误日志
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				logger.LogError(err.Err, "Request processing", logger.Fields{
					"method":    c.Request.Method,
					"path":      c.Request.URL.Path,
					"user_id":   userID,
					"status":    c.Writer.Status(),
					"duration":  duration.Milliseconds(),
				})
			}
		}
	}
}

// LogUserAction 记录用户操作的辅助函数
func LogUserAction(c *gin.Context, action string, details logger.Fields) {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint); ok {
			// 添加请求信息到详细信息中
			if details == nil {
				details = make(logger.Fields)
			}
			details["method"] = c.Request.Method
			details["path"] = c.Request.URL.Path
			details["ip"] = c.ClientIP()
			details["user_agent"] = c.Request.UserAgent()

			logger.LogUserAction(id, action, details)
		}
	}
}
