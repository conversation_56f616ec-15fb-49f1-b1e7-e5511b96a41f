package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisCache Redis缓存实现
type RedisCache struct {
	client *redis.Client
	prefix string
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Host     string
	Port     int
	Password string
	DB       int
	Prefix   string
}

// NewRedisCache 创建Redis缓存实例
func NewRedisCache(config CacheConfig) *RedisCache {
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.Host, config.Port),
		Password: config.Password,
		DB:       config.DB,
	})

	// 测试连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Printf("Redis connection failed: %v", err)
		return nil
	}

	log.Println("Redis cache connected successfully")
	return &RedisCache{
		client: rdb,
		prefix: config.Prefix,
	}
}

// buildKey 构建缓存键
func (r *RedisCache) buildKey(key string) string {
	if r.prefix != "" {
		return fmt.Sprintf("%s:%s", r.prefix, key)
	}
	return key
}

// Set 设置缓存
func (r *RedisCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %v", err)
	}

	return r.client.Set(ctx, r.buildKey(key), data, expiration).Err()
}

// Get 获取缓存
func (r *RedisCache) Get(ctx context.Context, key string, dest interface{}) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	data, err := r.client.Get(ctx, r.buildKey(key)).Result()
	if err != nil {
		return err
	}

	return json.Unmarshal([]byte(data), dest)
}

// Delete 删除缓存
func (r *RedisCache) Delete(ctx context.Context, keys ...string) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	if len(keys) == 0 {
		return nil
	}

	// 构建完整的键名
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = r.buildKey(key)
	}

	return r.client.Del(ctx, fullKeys...).Err()
}

// Exists 检查键是否存在
func (r *RedisCache) Exists(ctx context.Context, key string) (bool, error) {
	if r.client == nil {
		return false, fmt.Errorf("redis client not initialized")
	}

	count, err := r.client.Exists(ctx, r.buildKey(key)).Result()
	return count > 0, err
}

// Expire 设置过期时间
func (r *RedisCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	return r.client.Expire(ctx, r.buildKey(key), expiration).Err()
}

// TTL 获取剩余过期时间
func (r *RedisCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	if r.client == nil {
		return 0, fmt.Errorf("redis client not initialized")
	}

	return r.client.TTL(ctx, r.buildKey(key)).Result()
}

// Increment 递增
func (r *RedisCache) Increment(ctx context.Context, key string) (int64, error) {
	if r.client == nil {
		return 0, fmt.Errorf("redis client not initialized")
	}

	return r.client.Incr(ctx, r.buildKey(key)).Result()
}

// Decrement 递减
func (r *RedisCache) Decrement(ctx context.Context, key string) (int64, error) {
	if r.client == nil {
		return 0, fmt.Errorf("redis client not initialized")
	}

	return r.client.Decr(ctx, r.buildKey(key)).Result()
}

// SetHash 设置哈希字段
func (r *RedisCache) SetHash(ctx context.Context, key string, field string, value interface{}) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %v", err)
	}

	return r.client.HSet(ctx, r.buildKey(key), field, data).Err()
}

// GetHash 获取哈希字段
func (r *RedisCache) GetHash(ctx context.Context, key string, field string, dest interface{}) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	data, err := r.client.HGet(ctx, r.buildKey(key), field).Result()
	if err != nil {
		return err
	}

	return json.Unmarshal([]byte(data), dest)
}

// DeleteHash 删除哈希字段
func (r *RedisCache) DeleteHash(ctx context.Context, key string, fields ...string) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	if len(fields) == 0 {
		return nil
	}

	return r.client.HDel(ctx, r.buildKey(key), fields...).Err()
}

// GetAllHash 获取所有哈希字段
func (r *RedisCache) GetAllHash(ctx context.Context, key string) (map[string]string, error) {
	if r.client == nil {
		return nil, fmt.Errorf("redis client not initialized")
	}

	return r.client.HGetAll(ctx, r.buildKey(key)).Result()
}

// SetList 设置列表
func (r *RedisCache) SetList(ctx context.Context, key string, values ...interface{}) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	// 先删除现有列表
	r.client.Del(ctx, r.buildKey(key))

	if len(values) == 0 {
		return nil
	}

	// 序列化所有值
	serializedValues := make([]interface{}, len(values))
	for i, value := range values {
		data, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("failed to marshal value at index %d: %v", i, err)
		}
		serializedValues[i] = data
	}

	return r.client.RPush(ctx, r.buildKey(key), serializedValues...).Err()
}

// GetList 获取列表
func (r *RedisCache) GetList(ctx context.Context, key string, start, stop int64) ([]string, error) {
	if r.client == nil {
		return nil, fmt.Errorf("redis client not initialized")
	}

	return r.client.LRange(ctx, r.buildKey(key), start, stop).Result()
}

// PushList 向列表添加元素
func (r *RedisCache) PushList(ctx context.Context, key string, values ...interface{}) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	if len(values) == 0 {
		return nil
	}

	// 序列化所有值
	serializedValues := make([]interface{}, len(values))
	for i, value := range values {
		data, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("failed to marshal value at index %d: %v", i, err)
		}
		serializedValues[i] = data
	}

	return r.client.RPush(ctx, r.buildKey(key), serializedValues...).Err()
}

// FlushAll 清空所有缓存
func (r *RedisCache) FlushAll(ctx context.Context) error {
	if r.client == nil {
		return fmt.Errorf("redis client not initialized")
	}

	return r.client.FlushAll(ctx).Err()
}

// Close 关闭连接
func (r *RedisCache) Close() error {
	if r.client == nil {
		return nil
	}

	return r.client.Close()
}
