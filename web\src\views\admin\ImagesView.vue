<template>
  <div class="admin-images-container">
    <div class="page-header">
      <h2><i class="bi bi-images"></i> 图片管理</h2>
      <p class="page-subtitle">管理系统中所有用户的图片</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-images"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalImages }}</div>
          <div class="stat-label">总图片数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-hdd"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ formatFileSize(totalSize) }}</div>
          <div class="stat-label">总存储大小</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-people"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ activeUsers }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-calendar-today"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ todayUploads }}</div>
          <div class="stat-label">今日上传</div>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="search-filters">
        <div class="search-box">
          <i class="bi bi-search"></i>
          <input 
            type="text" 
            placeholder="搜索图片名称..." 
            v-model="searchQuery"
            @input="handleSearch"
          />
        </div>
        <select v-model="filterUser" @change="handleSearch" class="filter-select">
          <option value="">所有用户</option>
          <option v-for="user in users" :key="user.id" :value="user.id">
            {{ user.email }}
          </option>
        </select>
        <select v-model="filterAlbum" @change="handleSearch" class="filter-select">
          <option value="">所有相册</option>
          <option value="uncategorized">无相册</option>
          <option v-for="album in albums" :key="album.id" :value="album.id">
            {{ album.name }}
          </option>
        </select>
        <select v-model="sortBy" @change="handleSearch" class="filter-select">
          <option value="created_at">按上传时间</option>
          <option value="name">按名称</option>
          <option value="size">按大小</option>
        </select>
      </div>
      <div class="action-buttons">
        <button class="btn btn-outline" @click="refreshData">
          <i class="bi bi-arrow-clockwise"></i> 刷新
        </button>
        <button class="btn btn-danger" @click="bulkDelete" v-if="selectedImages.length > 0">
          <i class="bi bi-trash"></i> 批量删除 ({{ selectedImages.length }})
        </button>
      </div>
    </div>

    <!-- 图片网格 -->
    <div class="images-grid-container">
      <div class="view-controls">
        <div class="view-mode">
          <button 
            class="btn btn-sm" 
            :class="{ 'btn-primary': viewMode === 'grid', 'btn-outline': viewMode !== 'grid' }"
            @click="viewMode = 'grid'"
          >
            <i class="bi bi-grid-3x3"></i> 网格
          </button>
          <button 
            class="btn btn-sm" 
            :class="{ 'btn-primary': viewMode === 'list', 'btn-outline': viewMode !== 'list' }"
            @click="viewMode = 'list'"
          >
            <i class="bi bi-list"></i> 列表
          </button>
        </div>
        <div class="bulk-actions">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              :checked="isAllSelected"
              @change="toggleSelectAll"
            />
            全选
          </label>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="images-grid">
        <div 
          v-for="image in images" 
          :key="image.id" 
          class="image-card"
          :class="{ selected: selectedImages.includes(image.id) }"
        >
          <div class="image-checkbox">
            <input 
              type="checkbox" 
              :value="image.id"
              v-model="selectedImages"
            />
          </div>
          <div class="image-thumbnail" @click="viewImage(image)">
            <img :src="image.url" :alt="image.name" />
          </div>
          <div class="image-info">
            <h4 class="image-name">{{ image.name }}</h4>
            <div class="image-meta">
              <span class="image-size">{{ formatFileSize(image.size) }}</span>
              <span class="image-date">{{ formatDate(image.created_at) }}</span>
            </div>
            <div class="image-owner">
              <i class="bi bi-person"></i>
              {{ image.user?.email }}
            </div>
            <div class="image-album" v-if="image.album">
              <i class="bi bi-collection"></i>
              {{ image.album.name }}
            </div>
          </div>
          <div class="image-actions">
            <button 
              class="btn-icon" 
              @click="downloadImage(image)"
              title="下载"
            >
              <i class="bi bi-download"></i>
            </button>
            <button 
              class="btn-icon btn-danger" 
              @click="deleteImage(image)"
              title="删除"
            >
              <i class="bi bi-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="images-table-container">
        <table class="images-table">
          <thead>
            <tr>
              <th width="40">
                <input 
                  type="checkbox" 
                  :checked="isAllSelected"
                  @change="toggleSelectAll"
                />
              </th>
              <th>图片</th>
              <th>用户</th>
              <th>相册</th>
              <th>大小</th>
              <th>上传时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="image in images" :key="image.id">
              <td>
                <input 
                  type="checkbox" 
                  :value="image.id"
                  v-model="selectedImages"
                />
              </td>
              <td>
                <div class="table-image-info">
                  <img :src="image.url" :alt="image.name" class="table-thumbnail" />
                  <div class="table-image-details">
                    <div class="table-image-name">{{ image.name }}</div>
                    <div class="table-image-id">ID: {{ image.id }}</div>
                  </div>
                </div>
              </td>
              <td>{{ image.user?.email }}</td>
              <td>
                <span v-if="image.album">{{ image.album.name }}</span>
                <span v-else class="text-muted">-</span>
              </td>
              <td>{{ formatFileSize(image.size) }}</td>
              <td>{{ formatDate(image.created_at) }}</td>
              <td>
                <div class="table-actions">
                  <button 
                    class="btn-icon" 
                    @click="viewImage(image)"
                    title="查看"
                  >
                    <i class="bi bi-eye"></i>
                  </button>
                  <button 
                    class="btn-icon" 
                    @click="downloadImage(image)"
                    title="下载"
                  >
                    <i class="bi bi-download"></i>
                  </button>
                  <button 
                    class="btn-icon btn-danger" 
                    @click="deleteImage(image)"
                    title="删除"
                  >
                    <i class="bi bi-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && images.length === 0" class="empty-state">
        <i class="bi bi-images"></i>
        <h3>暂无图片</h3>
        <p>系统中还没有任何图片</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <i class="bi bi-arrow-repeat spin"></i>
        <p>加载中...</p>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="totalPages > 1">
      <div class="pagination-info">
        显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalImages) }} 
        共 {{ totalImages }} 张图片
      </div>
      <div class="pagination">
        <button 
          class="btn btn-sm" 
          @click="changePage(currentPage - 1)"
          :disabled="currentPage <= 1"
        >
          上一页
        </button>
        <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
        <button 
          class="btn btn-sm" 
          @click="changePage(currentPage + 1)"
          :disabled="currentPage >= totalPages"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 图片预览模态框 -->
    <div v-if="showImageModal" class="modal-overlay" @click="closeImageModal">
      <div class="modal-content image-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ viewingImage?.name }}</h3>
          <button class="close-btn" @click="closeImageModal">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="image-preview">
            <img :src="viewingImage?.url" :alt="viewingImage?.name" />
          </div>
          <div class="image-details">
            <div class="detail-grid">
              <div class="detail-item">
                <label>文件名</label>
                <span>{{ viewingImage?.name }}</span>
              </div>
              <div class="detail-item">
                <label>大小</label>
                <span>{{ formatFileSize(viewingImage?.size || 0) }}</span>
              </div>
              <div class="detail-item">
                <label>上传者</label>
                <span>{{ viewingImage?.user?.email }}</span>
              </div>
              <div class="detail-item">
                <label>所属相册</label>
                <span>{{ viewingImage?.album?.name || '-' }}</span>
              </div>
              <div class="detail-item">
                <label>上传时间</label>
                <span>{{ formatDate(viewingImage?.created_at || '') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知 -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue';
import api from '../../services/api';
import { useDialog } from '../../utils/dialog';
import { useStorageStore } from '../../stores/storage';

// 响应式数据
const images = ref<any[]>([]);
const users = ref<any[]>([]);
const albums = ref<any[]>([]);
const { confirmDelete, confirm, notify } = useDialog();
const storageStore = useStorageStore();
const loading = ref(false);
const viewMode = ref<'grid' | 'list'>('grid');
const selectedImages = ref<number[]>([]);
const showImageModal = ref(false);
const viewingImage = ref<any>(null);

// 搜索和过滤
const searchQuery = ref('');
const filterUser = ref('');
const filterAlbum = ref('');
const selectedUser = ref('');
const selectedAlbum = ref('');
const sortBy = ref('created_at');
const sortDesc = ref(true);

// 分页
const currentPage = ref(1);
const pageSize = ref(20);
const totalImages = ref(0);
const totalPages = ref(0);

// 统计数据
const totalSize = ref(0);
const activeUsers = ref(0);
const todayUploads = ref(0);

// 通知
const notification = reactive({
  show: false,
  message: '',
  type: 'success'
});

// 计算属性
const isAllSelected = computed(() => {
  return images.value.length > 0 && selectedImages.value.length === images.value.length;
});

// 方法
const fetchImages = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value,
      user_id: selectedUser.value || undefined,
      album_id: selectedAlbum.value || undefined,
      sort_by: sortBy.value,
      sort_desc: sortDesc.value
    };

    const response = await api.getAllImagesAdmin(params);
    images.value = response.images || [];
    totalImages.value = response.total || 0;
    totalPages.value = Math.ceil(totalImages.value / pageSize.value);
  } catch (error: any) {
    console.error('Failed to fetch images:', error);
    showNotification('获取图片列表失败', 'error');
  } finally {
    loading.value = false;
  }
};

const fetchUsers = async () => {
  try {
    const response = await api.getUsersForFilter();
    users.value = response || [];
  } catch (error) {
    console.error('Failed to fetch users:', error);
  }
};

const fetchAlbums = async () => {
  try {
    const response = await api.getAlbumsForFilter();
    albums.value = response || [];
  } catch (error) {
    console.error('Failed to fetch albums:', error);
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchImages();
};

const changePage = (page: number) => {
  currentPage.value = page;
  fetchImages();
};

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedImages.value = [];
  } else {
    selectedImages.value = images.value.map(img => img.id);
  }
};

const viewImage = (image: any) => {
  viewingImage.value = image;
  showImageModal.value = true;
};

const closeImageModal = () => {
  showImageModal.value = false;
  viewingImage.value = null;
};

const downloadImage = (image: any) => {
  // TODO: 实现图片下载功能
  console.log('Downloading image:', image.id);
};

const deleteImage = async (image: any) => {
  const confirmed = await confirmDelete(image.name, {
    details: '删除后无法恢复，请确认操作。'
  });

  if (confirmed) {
    try {
      await api.deleteImageAdmin(image.id);
      showNotification('图片删除成功');
      fetchImages(); // 重新加载图片列表

      // 刷新存储信息（防抖）
      storageStore.debouncedRefresh();
    } catch (error) {
      console.error('删除图片失败:', error);
      showNotification('删除图片失败', 'error');
    }
  }
};

const bulkDelete = async () => {
  const confirmed = await confirm(`确定要删除选中的 ${selectedImages.value.length} 张图片吗？`, {
    title: '批量删除图片',
    details: '删除后无法恢复，请确认操作。',
    type: 'danger',
    confirmText: '删除'
  });

  if (confirmed) {
    try {
      // 批量删除图片
      await api.bulkDeleteImagesAdmin(selectedImages.value);

      showNotification(`成功删除 ${selectedImages.value.length} 张图片`);
      selectedImages.value = []; // 清空选择
      fetchImages(); // 重新加载图片列表

      // 刷新存储信息（防抖）
      storageStore.debouncedRefresh();
    } catch (error) {
      console.error('批量删除图片失败:', error);
      showNotification('批量删除图片失败', 'error');
    }
  }
};

const refreshData = () => {
  fetchImages();
  fetchUsers();
  fetchAlbums();
};

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  if (type === 'success') {
    notify.success(message);
  } else {
    notify.error(message);
  }
};

// 生命周期
onMounted(() => {
  fetchImages();
  fetchUsers();
  fetchAlbums();
});
</script>

<style scoped>
.admin-images-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-subtitle {
  color: #666;
  margin: 0;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-card:nth-child(1) .stat-icon { background: #007bff; }
.stat-card:nth-child(2) .stat-icon { background: #28a745; }
.stat-card:nth-child(3) .stat-icon { background: #ffc107; }
.stat-card:nth-child(4) .stat-icon { background: #dc3545; }

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
  flex-wrap: wrap;
}

.search-filters {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: var(--text-secondary);
}

.search-box input {
  padding: 8px 12px 8px 35px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  width: 250px;
  background: var(--input-bg);
  color: var(--text-color);
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--input-bg);
  color: var(--text-color);
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.images-grid-container {
  background: var(--card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f1f3f4;
}

.view-mode {
  display: flex;
  gap: 5px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  padding: 20px;
}

.image-card {
  position: relative;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s;
}

.image-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-card.selected {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.image-checkbox {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
}

.image-thumbnail {
  height: 200px;
  overflow: hidden;
  cursor: pointer;
}

.image-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-info {
  padding: 15px;
}

.image-name {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.image-owner, .image-album {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.image-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.2s;
}

.image-card:hover .image-actions {
  opacity: 1;
}

.images-table-container {
  overflow-x: auto;
}

.images-table {
  width: 100%;
  border-collapse: collapse;
}

.images-table th,
.images-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #f1f3f4;
}

.images-table th {
  background: var(--header-bg);
  font-weight: 600;
  color: var(--text-color);
}

.table-image-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-thumbnail {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
}

.table-image-name {
  font-weight: 500;
  color: #333;
}

.table-image-id {
  font-size: 12px;
  color: #666;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.btn-icon {
  padding: 6px 8px;
  border: none;
  background: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon:hover {
  background: #e9ecef;
}

.btn-icon.btn-danger:hover {
  background: #dc3545;
  color: white;
}

.btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:hover:not(:disabled) {
  background: #f8f9fa;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-outline {
  background: transparent;
  color: #007bff;
  border-color: #007bff;
}

.btn-outline:hover:not(:disabled) {
  background: #007bff;
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}

.empty-state, .loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-state i, .loading-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 10px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content.image-modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
}

.modal-body {
  padding: 20px;
}

.image-preview {
  text-align: center;
  margin-bottom: 20px;
}

.image-preview img {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.detail-item span {
  color: #666;
  font-size: 14px;
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 6px;
  color: white;
  z-index: 1001;
}

.notification.success {
  background: #198754;
}

.notification.error {
  background: #dc3545;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.text-muted {
  color: #6c757d;
}
</style>
