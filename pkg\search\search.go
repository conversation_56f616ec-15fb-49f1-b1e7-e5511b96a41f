package search

import (
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// SearchParams 搜索参数
type SearchParams struct {
	Query       string    `json:"query" form:"query"`             // 搜索关键词
	Tags        []string  `json:"tags" form:"tags"`               // 标签过滤
	AlbumID     *uint     `json:"album_id" form:"album_id"`       // 相册ID过滤
	Format      string    `json:"format" form:"format"`           // 文件格式过滤
	MinSize     *int64    `json:"min_size" form:"min_size"`       // 最小文件大小
	MaxSize     *int64    `json:"max_size" form:"max_size"`       // 最大文件大小
	MinWidth    *int      `json:"min_width" form:"min_width"`     // 最小宽度
	MaxWidth    *int      `json:"max_width" form:"max_width"`     // 最大宽度
	MinHeight   *int      `json:"min_height" form:"min_height"`   // 最小高度
	MaxHeight   *int      `json:"max_height" form:"max_height"`   // 最大高度
	StartDate   *string   `json:"start_date" form:"start_date"`   // 开始日期 (YYYY-MM-DD)
	EndDate     *string   `json:"end_date" form:"end_date"`       // 结束日期 (YYYY-MM-DD)
	SortBy      string    `json:"sort_by" form:"sort_by"`         // 排序字段
	SortOrder   string    `json:"sort_order" form:"sort_order"`   // 排序方向
	Page        int       `json:"page" form:"page"`               // 页码
	PageSize    int       `json:"page_size" form:"page_size"`     // 每页大小
	UserID      uint      `json:"-"`                              // 用户ID（内部使用）
}

// SearchResult 搜索结果
type SearchResult struct {
	Items       interface{} `json:"items"`        // 搜索结果项
	Total       int64       `json:"total"`        // 总数量
	Page        int         `json:"page"`         // 当前页
	PageSize    int         `json:"page_size"`    // 每页大小
	TotalPages  int         `json:"total_pages"`  // 总页数
	HasNext     bool        `json:"has_next"`     // 是否有下一页
	HasPrev     bool        `json:"has_prev"`     // 是否有上一页
	Query       string      `json:"query"`        // 搜索关键词
	Filters     interface{} `json:"filters"`      // 应用的过滤器
	Suggestions []string    `json:"suggestions"`  // 搜索建议
}

// SearchBuilder 搜索构建器
type SearchBuilder struct {
	db     *gorm.DB
	params *SearchParams
	query  *gorm.DB
}

// NewSearchBuilder 创建搜索构建器
func NewSearchBuilder(db *gorm.DB, params *SearchParams) *SearchBuilder {
	return &SearchBuilder{
		db:     db,
		params: params,
		query:  db.Model(&struct{}{}),
	}
}

// BuildImageSearch 构建图片搜索查询
func (sb *SearchBuilder) BuildImageSearch() *gorm.DB {
	query := sb.db.Table("images")

	// 用户过滤
	if sb.params.UserID > 0 {
		query = query.Where("user_id = ?", sb.params.UserID)
	}

	// 关键词搜索（文件名）
	if sb.params.Query != "" {
		keywords := strings.Fields(sb.params.Query)
		for _, keyword := range keywords {
			query = query.Where("name LIKE ?", "%"+keyword+"%")
		}
	}

	// 相册过滤
	if sb.params.AlbumID != nil {
		if *sb.params.AlbumID == 0 {
			// 查找未分类图片
			query = query.Where("album_id IS NULL")
		} else {
			query = query.Where("album_id = ?", *sb.params.AlbumID)
		}
	}

	// 格式过滤
	if sb.params.Format != "" {
		query = query.Where("format = ?", strings.ToLower(sb.params.Format))
	}

	// 文件大小过滤
	if sb.params.MinSize != nil {
		query = query.Where("size >= ?", *sb.params.MinSize)
	}
	if sb.params.MaxSize != nil {
		query = query.Where("size <= ?", *sb.params.MaxSize)
	}

	// 图片尺寸过滤
	if sb.params.MinWidth != nil {
		query = query.Where("width >= ?", *sb.params.MinWidth)
	}
	if sb.params.MaxWidth != nil {
		query = query.Where("width <= ?", *sb.params.MaxWidth)
	}
	if sb.params.MinHeight != nil {
		query = query.Where("height >= ?", *sb.params.MinHeight)
	}
	if sb.params.MaxHeight != nil {
		query = query.Where("height <= ?", *sb.params.MaxHeight)
	}

	// 日期范围过滤
	if sb.params.StartDate != nil {
		if startTime, err := time.Parse("2006-01-02", *sb.params.StartDate); err == nil {
			query = query.Where("created_at >= ?", startTime)
		}
	}
	if sb.params.EndDate != nil {
		if endTime, err := time.Parse("2006-01-02", *sb.params.EndDate); err == nil {
			// 结束日期包含整天
			endTime = endTime.Add(24*time.Hour - time.Second)
			query = query.Where("created_at <= ?", endTime)
		}
	}

	// 排序
	orderClause := sb.buildOrderClause()
	if orderClause != "" {
		query = query.Order(orderClause)
	}

	return query
}

// BuildAlbumSearch 构建相册搜索查询
func (sb *SearchBuilder) BuildAlbumSearch() *gorm.DB {
	query := sb.db.Table("albums")

	// 用户过滤
	if sb.params.UserID > 0 {
		query = query.Where("user_id = ?", sb.params.UserID)
	}

	// 关键词搜索（相册名称和描述）
	if sb.params.Query != "" {
		keywords := strings.Fields(sb.params.Query)
		for _, keyword := range keywords {
			query = query.Where("(name LIKE ? OR description LIKE ?)", 
				"%"+keyword+"%", "%"+keyword+"%")
		}
	}

	// 日期范围过滤
	if sb.params.StartDate != nil {
		if startTime, err := time.Parse("2006-01-02", *sb.params.StartDate); err == nil {
			query = query.Where("created_at >= ?", startTime)
		}
	}
	if sb.params.EndDate != nil {
		if endTime, err := time.Parse("2006-01-02", *sb.params.EndDate); err == nil {
			endTime = endTime.Add(24*time.Hour - time.Second)
			query = query.Where("created_at <= ?", endTime)
		}
	}

	// 排序
	orderClause := sb.buildOrderClause()
	if orderClause != "" {
		query = query.Order(orderClause)
	}

	return query
}

// buildOrderClause 构建排序子句
func (sb *SearchBuilder) buildOrderClause() string {
	sortBy := sb.params.SortBy
	sortOrder := strings.ToUpper(sb.params.SortOrder)

	// 验证排序方向
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "DESC"
	}

	// 验证排序字段
	validSortFields := map[string]bool{
		"created_at": true,
		"updated_at": true,
		"name":       true,
		"size":       true,
		"width":      true,
		"height":     true,
	}

	if sortBy == "" || !validSortFields[sortBy] {
		sortBy = "created_at"
	}

	return fmt.Sprintf("%s %s", sortBy, sortOrder)
}

// GetSearchSuggestions 获取搜索建议
func GetSearchSuggestions(db *gorm.DB, query string, userID uint, limit int) []string {
	if query == "" || limit <= 0 {
		return []string{}
	}

	var suggestions []string

	// 从图片名称中获取建议
	var imageNames []string
	db.Table("images").
		Where("user_id = ? AND name LIKE ?", userID, "%"+query+"%").
		Limit(limit).
		Pluck("name", &imageNames)

	// 提取文件名（不包含扩展名）作为建议
	for _, name := range imageNames {
		// 移除扩展名
		if dotIndex := strings.LastIndex(name, "."); dotIndex > 0 {
			baseName := name[:dotIndex]
			if strings.Contains(strings.ToLower(baseName), strings.ToLower(query)) {
				suggestions = append(suggestions, baseName)
			}
		}
	}

	// 去重
	suggestions = removeDuplicates(suggestions)

	// 限制数量
	if len(suggestions) > limit {
		suggestions = suggestions[:limit]
	}

	return suggestions
}

// removeDuplicates 去除重复项
func removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}

// ValidateSearchParams 验证搜索参数
func ValidateSearchParams(params *SearchParams) error {
	// 验证页码
	if params.Page < 1 {
		params.Page = 1
	}

	// 验证页大小
	if params.PageSize < 1 {
		params.PageSize = 20
	}
	if params.PageSize > 100 {
		params.PageSize = 100
	}

	// 验证文件大小范围
	if params.MinSize != nil && params.MaxSize != nil {
		if *params.MinSize > *params.MaxSize {
			return fmt.Errorf("最小文件大小不能大于最大文件大小")
		}
	}

	// 验证图片尺寸范围
	if params.MinWidth != nil && params.MaxWidth != nil {
		if *params.MinWidth > *params.MaxWidth {
			return fmt.Errorf("最小宽度不能大于最大宽度")
		}
	}
	if params.MinHeight != nil && params.MaxHeight != nil {
		if *params.MinHeight > *params.MaxHeight {
			return fmt.Errorf("最小高度不能大于最大高度")
		}
	}

	// 验证日期格式
	if params.StartDate != nil {
		if _, err := time.Parse("2006-01-02", *params.StartDate); err != nil {
			return fmt.Errorf("开始日期格式错误，应为 YYYY-MM-DD")
		}
	}
	if params.EndDate != nil {
		if _, err := time.Parse("2006-01-02", *params.EndDate); err != nil {
			return fmt.Errorf("结束日期格式错误，应为 YYYY-MM-DD")
		}
	}

	// 验证日期范围
	if params.StartDate != nil && params.EndDate != nil {
		startTime, _ := time.Parse("2006-01-02", *params.StartDate)
		endTime, _ := time.Parse("2006-01-02", *params.EndDate)
		if startTime.After(endTime) {
			return fmt.Errorf("开始日期不能晚于结束日期")
		}
	}

	return nil
}
