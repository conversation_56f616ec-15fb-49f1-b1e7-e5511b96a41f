package controllers

import (
	"net/http"

	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"

	"github.com/gin-gonic/gin"
)

// GetUserSettings 获取用户设置
func GetUserSettings(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	user, err := dao.GetUserWithDefaultAlbum(userID.(uint))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	response := models.UserSettingsResponse{
		ID:             user.ID,
		Email:          user.Email,
		DefaultAlbumID: user.DefaultAlbumID,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"user":    response,
	})
}

// UpdateUserSettings 更新用户设置
func UpdateUserSettings(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.UserSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 如果设置了默认相册，验证相册是否属于当前用户
	if req.DefaultAlbumID != nil {
		owned, err := dao.CheckAlbumOwnership(*req.DefaultAlbumID, userID.(uint))
		if err != nil || !owned {
			c.JSON(http.StatusForbidden, gin.H{"error": "Album not found or access denied"})
			return
		}
	}

	// 更新用户的默认相册
	if err := dao.UpdateUserDefaultAlbum(userID.(uint), req.DefaultAlbumID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user settings"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User settings updated successfully",
	})
}

// GetUserProfile 获取用户资料
func GetUserProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	user, err := dao.GetUserWithDefaultAlbum(userID.(uint))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 检查默认相册是否仍然存在，如果不存在则清除引用
	if user.DefaultAlbumID != nil && user.DefaultAlbum == nil {
		// 默认相册已被删除，清除用户的默认相册设置
		dao.UpdateUserDefaultAlbum(user.ID, nil)
		user.DefaultAlbumID = nil
	}

	// 不返回敏感信息
	response := gin.H{
		"id":               user.ID,
		"username":         user.Username,
		"email":            user.Email,
		"default_album_id": user.DefaultAlbumID,
		"created_at":       user.CreatedAt,
		"updated_at":       user.UpdatedAt,
	}

	if user.DefaultAlbum != nil {
		response["default_album"] = gin.H{
			"id":   user.DefaultAlbum.ID,
			"name": user.DefaultAlbum.Name,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"user":    response,
	})
}
