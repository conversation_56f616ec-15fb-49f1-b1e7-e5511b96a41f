package models

import (
	"encoding/json"
	"time"
)

// Image 表示图片模型
type Image struct {
	ID         uint      `json:"id" gorm:"primary_key"`
	Name       string    `json:"name" gorm:"type:varchar(191);not null"` // 文件名
	URL        string    `json:"url" gorm:"type:varchar(500);not null"`  // 访问URL
	Size       int64     `json:"size"`                                   // 文件大小
	Width      int       `json:"width" gorm:"default:0"`                 // 图片宽度
	Height     int       `json:"height" gorm:"default:0"`                // 图片高度
	Format     string    `json:"format" gorm:"size:10"`                  // 图片格式
	Thumbnails string    `json:"thumbnails" gorm:"type:text"`            // 缩略图路径(JSON)
	UserID     uint      `json:"user_id" gorm:"not null"`                // 所属用户ID
	User       User      `json:"user" gorm:"foreignKey:UserID"`          // 关联的用户
	AlbumID    *uint     `json:"album_id"`                               // 所属相册ID（可选）
	Album      *Album    `json:"album" gorm:"foreignKey:AlbumID"`        // 关联的相册
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// ImageResponse 图片响应结构
type ImageResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	URL       string    `json:"url"`
	Size      int64     `json:"size"`
	AlbumID   *uint     `json:"album_id"`
	Album     *Album    `json:"album,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ThumbnailInfo 缩略图信息
type ThumbnailInfo struct {
	Small  string `json:"small,omitempty"`  // 150x150
	Medium string `json:"medium,omitempty"` // 300x300
	Large  string `json:"large,omitempty"`  // 600x600
	XLarge string `json:"xlarge,omitempty"` // 1200x1200
}

// GetThumbnails 获取缩略图信息
func (img *Image) GetThumbnails() *ThumbnailInfo {
	if img.Thumbnails == "" {
		return &ThumbnailInfo{}
	}

	var thumbnails ThumbnailInfo
	if err := json.Unmarshal([]byte(img.Thumbnails), &thumbnails); err != nil {
		return &ThumbnailInfo{}
	}

	return &thumbnails
}

// SetThumbnails 设置缩略图信息
func (img *Image) SetThumbnails(thumbnails *ThumbnailInfo) error {
	data, err := json.Marshal(thumbnails)
	if err != nil {
		return err
	}

	img.Thumbnails = string(data)
	return nil
}

// HasThumbnails 检查是否有缩略图
func (img *Image) HasThumbnails() bool {
	thumbnails := img.GetThumbnails()
	return thumbnails.Small != "" || thumbnails.Medium != "" || thumbnails.Large != "" || thumbnails.XLarge != ""
}

// AdminImageResponse 管理员图片响应结构
type AdminImageResponse struct {
	ID        uint           `json:"id"`
	Name      string         `json:"name"`
	URL       string         `json:"url"`
	Size      int64          `json:"size"`
	UserID    uint           `json:"user_id"`
	User      *UserResponse  `json:"user,omitempty"`
	AlbumID   *uint          `json:"album_id"`
	Album     *AlbumResponse `json:"album,omitempty"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
}

// ImageListRequest 图片列表请求
type ImageListRequest struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Search   string `json:"search"`
	UserID   uint   `json:"user_id"`
	AlbumID  uint   `json:"album_id"`
	SortBy   string `json:"sort_by"`
	SortDesc bool   `json:"sort_desc"`
}

// ImageListResponse 图片列表响应
type ImageListResponse struct {
	Images     []AdminImageResponse `json:"images"`
	Total      int64                `json:"total"`
	Page       int                  `json:"page"`
	PageSize   int                  `json:"page_size"`
	TotalPages int                  `json:"total_pages"`
}

// ImageStats 图片统计信息
type ImageStats struct {
	TotalImages  int64 `json:"total_images"`
	TotalSize    int64 `json:"total_size"`
	ActiveUsers  int64 `json:"active_users"`
	TodayUploads int64 `json:"today_uploads"`
	WeekUploads  int64 `json:"week_uploads"`
	MonthUploads int64 `json:"month_uploads"`
}

// UserForFilter 用于过滤的用户信息
type UserForFilter struct {
	ID    uint   `json:"id"`
	Email string `json:"email"`
}

// AlbumForFilter 用于过滤的相册信息
type AlbumForFilter struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}
