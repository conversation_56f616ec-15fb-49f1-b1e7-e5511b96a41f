package config

import (
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"os"
)

// Config 应用配置结构
type Config struct {
	Database DatabaseConfig `json:"database"`
	Server   ServerConfig   `json:"server"`
	JWT      JWTConfig      `json:"jwt"`
	Upload   UploadConfig   `json:"upload"`
	Cache    CacheConfig    `json:"cache"`
}

// CacheConfig 缓存配置结构
type CacheConfig struct {
	Type   string            `json:"type"`   // redis 或 memory
	Redis  RedisCacheConfig  `json:"redis"`  // Redis配置
	Memory MemoryCacheConfig `json:"memory"` // 内存缓存配置
}

// RedisCacheConfig Redis缓存配置
type RedisCacheConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Password string `json:"password"`
	DB       int    `json:"db"`
	Prefix   string `json:"prefix"`
}

// MemoryCacheConfig 内存缓存配置
type MemoryCacheConfig struct {
	Prefix string `json:"prefix"`
}

// DatabaseConfig 数据库配置结构
type DatabaseConfig struct {
	Type   string       `json:"type"`
	MySQL  MySQLConfig  `json:"mysql"`
	SQLite SQLiteConfig `json:"sqlite"`
}

// MySQLConfig MySQL配置结构
type MySQLConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	Name     string `json:"name"`
	Charset  string `json:"charset"`
}

// SQLiteConfig SQLite配置结构
type SQLiteConfig struct {
	Path string `json:"path"`
}

// ServerConfig 服务器配置结构
type ServerConfig struct {
	Port           string `json:"port"`
	BaseURL        string `json:"base_url,omitempty"`        // 可选的基础URL，如果不设置则自动检测
	AllowedOrigins string `json:"allowed_origins,omitempty"` // 允许的CORS域名，逗号分隔
}

// JWTConfig JWT配置结构
type JWTConfig struct {
	Secret string `json:"secret"`
}

// UploadConfig 上传配置结构
type UploadConfig struct {
	Path    string `json:"path"`
	MaxSize int64  `json:"max_size"`
}

// App 配置实例
var App *Config

// LoadConfig 加载配置文件
func LoadConfig() *Config {
	// 如果已经加载过配置，直接返回
	if App != nil {
		return App
	}

	// 初始化默认配置
	App = &Config{
		Database: DatabaseConfig{
			Type: "mysql",
			MySQL: MySQLConfig{
				Host:     "localhost",
				Port:     "3306",
				User:     "root",
				Password: "",
				Name:     "cloudbed",
				Charset:  "utf8mb4",
			},
			SQLite: SQLiteConfig{
				Path: "cloudbed.db",
			},
		},
		Server: ServerConfig{
			Port: "18080",
		},
		JWT: JWTConfig{
			Secret: "", // 将在下面生成或加载
		},
		Upload: UploadConfig{
			Path:    "./uploads",
			MaxSize: 10485760, // 10MB
		},
	}

	// 检查配置文件是否存在
	if _, err := os.Stat("config/config.json"); os.IsNotExist(err) {
		// 配置文件不存在，生成JWT密钥并保存配置
		App.JWT.Secret = generateJWTSecret()
		if err := saveConfig(); err != nil {
			log.Fatal("Failed to save initial config:", err)
		}
		log.Println("Generated new JWT secret and saved initial config")
	} else {
		// 配置文件存在，加载它
		file, err := os.Open("config/config.json")
		if err != nil {
			log.Fatal("Failed to open config file:", err)
		}
		defer file.Close()

		// 解析JSON配置
		decoder := json.NewDecoder(file)
		if err := decoder.Decode(App); err != nil {
			log.Fatalf("Failed to parse config file: %v", err)
		}

		// 如果JWT密钥为空，生成一个新的
		if App.JWT.Secret == "" {
			App.JWT.Secret = generateJWTSecret()
			if err := saveConfig(); err != nil {
				log.Fatalf("Failed to save config with new JWT secret: %v", err)
			}
			log.Println("Generated new JWT secret and updated config")
		}
	}

	// 使用环境变量覆盖配置（如果存在）
	overrideWithEnvVars()

	return App
}

// overrideWithEnvVars 使用环境变量覆盖配置
func overrideWithEnvVars() {
	// JWT密钥
	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		App.JWT.Secret = jwtSecret
		log.Println("JWT secret loaded from environment variable")
	}

	// 服务器端口
	if port := os.Getenv("SERVER_PORT"); port != "" {
		App.Server.Port = port
		log.Printf("Server port set to %s from environment variable", port)
	}

	// 允许的CORS域名
	if allowedOrigins := os.Getenv("ALLOWED_ORIGINS"); allowedOrigins != "" {
		App.Server.AllowedOrigins = allowedOrigins
		log.Printf("Allowed origins set from environment variable")
	}

	// 数据库配置
	if dbType := os.Getenv("DB_TYPE"); dbType != "" {
		App.Database.Type = dbType
	}

	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		App.Database.MySQL.Host = dbHost
	}

	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		App.Database.MySQL.Port = dbPort
	}

	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		App.Database.MySQL.User = dbUser
	}

	if dbPassword := os.Getenv("DB_PASSWORD"); dbPassword != "" {
		App.Database.MySQL.Password = dbPassword
	}

	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		App.Database.MySQL.Name = dbName
	}
}

// generateJWTSecret 生成随机JWT密钥
func generateJWTSecret() string {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		log.Fatalf("Failed to generate JWT secret: %v", err)
	}
	return base64.URLEncoding.EncodeToString(bytes)
}

// saveConfig 保存配置到文件
func saveConfig() error {
	// 确保config目录存在
	if err := os.MkdirAll("config", os.ModePerm); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// 创建或截断配置文件
	file, err := os.Create("config/config.json")
	if err != nil {
		return fmt.Errorf("failed to create config file: %w", err)
	}
	defer file.Close()

	// 格式化并写入配置
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(App); err != nil {
		return fmt.Errorf("failed to encode config: %w", err)
	}

	return nil
}

// GetMySQLDSN 获取MySQL连接字符串
func (c *DatabaseConfig) GetMySQLDSN() string {
	mysql := c.MySQL
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		mysql.User, mysql.Password, mysql.Host, mysql.Port, mysql.Name, mysql.Charset)
}

// GetBaseURL 获取基础URL，如果配置中没有设置则根据请求自动生成
func GetBaseURL(host string, isHTTPS bool, cfg *Config) string {
	// 如果配置中设置了BaseURL，直接使用
	if cfg.Server.BaseURL != "" {
		return cfg.Server.BaseURL
	}

	// 否则根据请求信息自动生成
	scheme := "http"
	if isHTTPS {
		scheme = "https"
	}

	return fmt.Sprintf("%s://%s", scheme, host)
}
