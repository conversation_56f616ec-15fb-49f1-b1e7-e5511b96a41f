{"name": "cloudbed", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.11.0", "bootstrap-icons": "^1.13.1", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^4.5.14", "vue-tsc": "^2.2.12"}}