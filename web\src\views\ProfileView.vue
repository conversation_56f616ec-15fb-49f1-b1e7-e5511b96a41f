<template>
  <div class="profile-container">
    <div class="page-header">
      <h2><i class="bi bi-person"></i> 个人资料</h2>
      <p class="page-subtitle">管理和更新您的个人资料信息</p>
    </div>
    
    <div class="profile-grid">
      <!-- 个人信息卡片 -->
      <div class="card profile-card">
        <div class="card-header">
          <h4><i class="bi bi-person-badge"></i> 个人信息</h4>
        </div>
        <div class="card-body">
          <div class="profile-avatar">
            <div class="avatar-placeholder">
              <i class="bi bi-person-circle"></i>
            </div>
            <button class="change-avatar-btn">
              <i class="bi bi-camera"></i> 更换头像
            </button>
          </div>
          
          <div class="profile-info">
            <div class="info-item">
              <label>用户名</label>
              <div class="info-value">{{ profile.username }}</div>
            </div>

            <div class="info-item">
              <label>邮箱</label>
              <div class="info-value">{{ profile.email }}</div>
            </div>
            
            <div class="info-item">
              <label>注册时间</label>
              <div class="info-value">{{ profile.joinDate }}</div>
            </div>
            
            <div class="info-item">
              <label>最后登录</label>
              <div class="info-value">{{ profile.lastLogin }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 修改密码卡片 -->
      <div class="card profile-card">
        <div class="card-header">
          <h4><i class="bi bi-key"></i> 修改密码</h4>
        </div>
        <div class="card-body">
          <form @submit.prevent="updatePassword">
            <div class="form-group">
              <label for="currentPassword">当前密码</label>
              <div class="input-group">
                <span class="input-icon"><i class="bi bi-lock"></i></span>
                <input 
                  type="password" 
                  id="currentPassword" 
                  v-model="passwordForm.currentPassword" 
                  placeholder="请输入当前密码"
                  required
                />
              </div>
            </div>
            
            <div class="form-group">
              <label for="newPassword">新密码</label>
              <div class="input-group">
                <span class="input-icon"><i class="bi bi-lock-fill"></i></span>
                <input 
                  type="password" 
                  id="newPassword" 
                  v-model="passwordForm.newPassword" 
                  placeholder="请输入新密码"
                  required
                />
              </div>
            </div>
            
            <div class="form-group">
              <label for="confirmPassword">确认新密码</label>
              <div class="input-group">
                <span class="input-icon"><i class="bi bi-shield-lock"></i></span>
                <input 
                  type="password" 
                  id="confirmPassword" 
                  v-model="passwordForm.confirmPassword" 
                  placeholder="请再次输入新密码"
                  required
                />
              </div>
            </div>
            
            <div class="password-requirements">
              <h5>密码要求：</h5>
              <ul>
                <li :class="{ met: passwordForm.newPassword.length >= 8 }">
                  <i class="bi" :class="passwordForm.newPassword.length >= 8 ? 'bi-check-circle-fill' : 'bi-circle'"></i>
                  至少8个字符
                </li>
                <li :class="{ met: /[A-Z]/.test(passwordForm.newPassword) }">
                  <i class="bi" :class="/[A-Z]/.test(passwordForm.newPassword) ? 'bi-check-circle-fill' : 'bi-circle'"></i>
                  包含大写字母
                </li>
                <li :class="{ met: /[a-z]/.test(passwordForm.newPassword) }">
                  <i class="bi" :class="/[a-z]/.test(passwordForm.newPassword) ? 'bi-check-circle-fill' : 'bi-circle'"></i>
                  包含小写字母
                </li>
                <li :class="{ met: /[0-9]/.test(passwordForm.newPassword) }">
                  <i class="bi" :class="/[0-9]/.test(passwordForm.newPassword) ? 'bi-check-circle-fill' : 'bi-circle'"></i>
                  包含数字
                </li>
              </ul>
            </div>
            
            <button type="submit" class="btn btn-primary" :disabled="passwordUpdating">
              <span v-if="passwordUpdating" class="spinner"><i class="bi bi-arrow-repeat spin"></i></span>
              {{ passwordUpdating ? '更新中...' : '更新密码' }}
            </button>
          </form>
        </div>
      </div>
      
    </div>
    
    <!-- 通知消息 -->
    <div v-if="notification.message" class="notification" :class="notification.type">
      <i :class="getNotificationIcon(notification.type)"></i> {{ notification.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useAuthStore } from '../stores/auth';

const authStore = useAuthStore();

const profile = reactive({
  username: computed(() => authStore.getUser?.username || ''),
  email: computed(() => authStore.getUser?.email || ''),
  joinDate: '2025-01-01',
  lastLogin: '2025-07-27 10:30'
});



const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const passwordUpdating = ref(false);
const notification = ref({ message: '', type: 'info' });

// 更新密码
const updatePassword = async () => {
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    showNotification('新密码与确认密码不一致', 'error');
    return;
  }
  
  if (passwordForm.newPassword.length < 8) {
    showNotification('密码长度至少8位', 'error');
    return;
  }
  
  passwordUpdating.value = true;
  
  try {
    // 在实际应用中，这里会发送请求到后端更新密码
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
    showNotification('密码已更新', 'success');
    passwordForm.currentPassword = '';
    passwordForm.newPassword = '';
    passwordForm.confirmPassword = '';
  } catch (error) {
    showNotification('密码更新失败', 'error');
  } finally {
    passwordUpdating.value = false;
  }
};

// 显示通知消息
const showNotification = (message: string, type: string) => {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value.message = '';
  }, 3000);
};

// 获取通知图标
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'success': return 'bi bi-check-circle';
    case 'error': return 'bi bi-exclamation-circle';
    case 'info': return 'bi bi-info-circle';
    default: return 'bi bi-info-circle';
  }
};

// 格式化文件大小（暂时保留，后续可能会用到）
// const formatFileSize = (bytes: number) => {
//   if (bytes === 0) return '0 Bytes';
//   const k = 1024;
//   const sizes = ['Bytes', 'KB', 'MB', 'GB'];
//   const i = Math.floor(Math.log(bytes) / Math.log(k));
//   return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
// };
</script>

<style scoped>
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px; /* 添加内边距以适应缩放 */
}

.page-header {
  margin-bottom: 25px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: var(--text-color);
  font-weight: 600;
}

.page-header h2 i {
  margin-right: 10px;
  color: #007bff;
}

body.dark-theme .page-header h2 i {
  color: #66b3ff;
}

.page-subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.card {
  background: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid var(--border);
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid var(--border);
  background-color: var(--header-bg);
}

.card-header h4 {
  margin: 0;
  color: var(--text-color);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-body {
  padding: 25px;
  background-color: var(--card-bg);
}

.profile-avatar {
  text-align: center;
  margin-bottom: 30px;
}

.avatar-placeholder {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: var(--header-bg);
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: var(--text-secondary);
  border: 1px solid var(--border);
}

.change-avatar-btn {
  background: none;
  border: 1px solid var(--border);
  border-radius: 6px;
  padding: 8px 15px;
  cursor: pointer;
  color: var(--text-color);
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  background-color: var(--card-bg);
}

.change-avatar-btn:hover {
  background-color: var(--header-bg);
}

.profile-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-item label {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.info-value {
  padding: 12px 15px;
  background-color: var(--header-bg);
  border-radius: 8px;
  color: var(--text-color);
  border: 1px solid var(--border);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.9rem;
}

.input-group {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  z-index: 1;
}

.form-group input {
  width: 100%;
  padding: 12px 12px 12px 45px;
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 16px;
  box-sizing: border-box;
  transition: border-color 0.3s, box-shadow 0.3s;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.form-group input:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

body.dark-theme .form-group input:focus {
  border-color: #66b3ff;
  box-shadow: 0 0 0 3px rgba(102, 179, 255, 0.1);
}

.password-requirements {
  background-color: var(--header-bg);
  border-radius: 8px;
  padding: 20px;
  margin: 25px 0;
  border: 1px solid var(--border);
}

.password-requirements h5 {
  margin: 0 0 15px 0;
  color: var(--text-color);
}

.password-requirements ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.password-requirements li {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  color: var(--text-secondary);
}

.password-requirements li:last-child {
  margin-bottom: 0;
}

.password-requirements li.met {
  color: #28a745;
}

body.dark-theme .password-requirements li.met {
  color: #20c997;
}

.password-requirements li.met .bi {
  color: #28a745;
}

body.dark-theme .password-requirements li.met .bi {
  color: #20c997;
}

.btn {
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s;
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  background-color: var(--card-bg);
  color: var(--text-color);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff, #00bcd4);
  color: white;
  width: 100%;
}

body.dark-theme .btn-primary {
  background: linear-gradient(135deg, #3a57b0, #1a8fc9);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

body.dark-theme .btn-primary:hover:not(:disabled) {
  box-shadow: 0 5px 15px rgba(102, 179, 255, 0.3);
}

.btn-primary:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spinner {
  margin-right: 8px;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  color: white;
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
  z-index: 1000;
  animation: slideIn 0.3s, fadeOut 0.5s 2.5s forwards;
  display: flex;
  align-items: center;
  gap: 10px;
  max-width: 350px;
}

.notification.success {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.notification.error {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.notification.info {
  background: linear-gradient(135deg, #17a2b8, #00bcd4);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@media (max-width: 768px) {
  .profile-grid {
    grid-template-columns: 1fr;
  }
  
  .profile-card .card-body {
    padding: 20px;
  }
  
  .profile-container {
    padding: 0 10px;
  }
  
  .avatar-placeholder {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
  }
}

/* 针对浏览器缩放的优化 */
@media screen and (-webkit-min-device-pixel-ratio: 1.25), 
       screen and (min-resolution: 120dpi) {
  .form-group input {
    padding: 10px 10px 10px 40px;
    font-size: 15px;
  }
  
  .info-value {
    padding: 10px 12px;
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 1.5), 
       screen and (min-resolution: 144dpi) {
  .profile-grid {
    gap: 20px;
  }
  
  .card-header {
    padding: 18px;
  }
  
  .card-body {
    padding: 20px;
  }
}
</style>