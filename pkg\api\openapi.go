package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// OpenAPISpec OpenAPI规范
type OpenAPISpec struct {
	OpenAPI    string                 `json:"openapi"`
	Info       Info                   `json:"info"`
	Servers    []Server               `json:"servers"`
	Paths      map[string]PathItem    `json:"paths"`
	Components Components             `json:"components"`
	Security   []SecurityRequirement  `json:"security,omitempty"`
	Tags       []Tag                  `json:"tags,omitempty"`
}

// Info API信息
type Info struct {
	Title          string  `json:"title"`
	Description    string  `json:"description"`
	Version        string  `json:"version"`
	TermsOfService string  `json:"termsOfService,omitempty"`
	Contact        Contact `json:"contact,omitempty"`
	License        License `json:"license,omitempty"`
}

// Contact 联系信息
type Contact struct {
	Name  string `json:"name,omitempty"`
	URL   string `json:"url,omitempty"`
	Email string `json:"email,omitempty"`
}

// License 许可证信息
type License struct {
	Name string `json:"name"`
	URL  string `json:"url,omitempty"`
}

// Server 服务器信息
type Server struct {
	URL         string                    `json:"url"`
	Description string                    `json:"description,omitempty"`
	Variables   map[string]ServerVariable `json:"variables,omitempty"`
}

// ServerVariable 服务器变量
type ServerVariable struct {
	Enum        []string `json:"enum,omitempty"`
	Default     string   `json:"default"`
	Description string   `json:"description,omitempty"`
}

// PathItem 路径项
type PathItem struct {
	Summary     string     `json:"summary,omitempty"`
	Description string     `json:"description,omitempty"`
	Get         *Operation `json:"get,omitempty"`
	Put         *Operation `json:"put,omitempty"`
	Post        *Operation `json:"post,omitempty"`
	Delete      *Operation `json:"delete,omitempty"`
	Options     *Operation `json:"options,omitempty"`
	Head        *Operation `json:"head,omitempty"`
	Patch       *Operation `json:"patch,omitempty"`
	Trace       *Operation `json:"trace,omitempty"`
	Parameters  []Parameter `json:"parameters,omitempty"`
}

// Operation 操作
type Operation struct {
	Tags         []string              `json:"tags,omitempty"`
	Summary      string                `json:"summary,omitempty"`
	Description  string                `json:"description,omitempty"`
	OperationID  string                `json:"operationId,omitempty"`
	Parameters   []Parameter           `json:"parameters,omitempty"`
	RequestBody  *RequestBody          `json:"requestBody,omitempty"`
	Responses    map[string]Response   `json:"responses"`
	Callbacks    map[string]Callback   `json:"callbacks,omitempty"`
	Deprecated   bool                  `json:"deprecated,omitempty"`
	Security     []SecurityRequirement `json:"security,omitempty"`
	Servers      []Server              `json:"servers,omitempty"`
}

// Parameter 参数
type Parameter struct {
	Name            string      `json:"name"`
	In              string      `json:"in"`
	Description     string      `json:"description,omitempty"`
	Required        bool        `json:"required,omitempty"`
	Deprecated      bool        `json:"deprecated,omitempty"`
	AllowEmptyValue bool        `json:"allowEmptyValue,omitempty"`
	Style           string      `json:"style,omitempty"`
	Explode         bool        `json:"explode,omitempty"`
	AllowReserved   bool        `json:"allowReserved,omitempty"`
	Schema          *Schema     `json:"schema,omitempty"`
	Example         interface{} `json:"example,omitempty"`
	Examples        map[string]Example `json:"examples,omitempty"`
}

// RequestBody 请求体
type RequestBody struct {
	Description string                `json:"description,omitempty"`
	Content     map[string]MediaType  `json:"content"`
	Required    bool                  `json:"required,omitempty"`
}

// Response 响应
type Response struct {
	Description string               `json:"description"`
	Headers     map[string]Header    `json:"headers,omitempty"`
	Content     map[string]MediaType `json:"content,omitempty"`
	Links       map[string]Link      `json:"links,omitempty"`
}

// MediaType 媒体类型
type MediaType struct {
	Schema   *Schema            `json:"schema,omitempty"`
	Example  interface{}        `json:"example,omitempty"`
	Examples map[string]Example `json:"examples,omitempty"`
	Encoding map[string]Encoding `json:"encoding,omitempty"`
}

// Schema 模式
type Schema struct {
	Type                 string             `json:"type,omitempty"`
	AllOf                []*Schema          `json:"allOf,omitempty"`
	OneOf                []*Schema          `json:"oneOf,omitempty"`
	AnyOf                []*Schema          `json:"anyOf,omitempty"`
	Not                  *Schema            `json:"not,omitempty"`
	Items                *Schema            `json:"items,omitempty"`
	Properties           map[string]*Schema `json:"properties,omitempty"`
	AdditionalProperties interface{}        `json:"additionalProperties,omitempty"`
	Description          string             `json:"description,omitempty"`
	Format               string             `json:"format,omitempty"`
	Default              interface{}        `json:"default,omitempty"`
	Title                string             `json:"title,omitempty"`
	MultipleOf           float64            `json:"multipleOf,omitempty"`
	Maximum              float64            `json:"maximum,omitempty"`
	ExclusiveMaximum     bool               `json:"exclusiveMaximum,omitempty"`
	Minimum              float64            `json:"minimum,omitempty"`
	ExclusiveMinimum     bool               `json:"exclusiveMinimum,omitempty"`
	MaxLength            int                `json:"maxLength,omitempty"`
	MinLength            int                `json:"minLength,omitempty"`
	Pattern              string             `json:"pattern,omitempty"`
	MaxItems             int                `json:"maxItems,omitempty"`
	MinItems             int                `json:"minItems,omitempty"`
	UniqueItems          bool               `json:"uniqueItems,omitempty"`
	MaxProperties        int                `json:"maxProperties,omitempty"`
	MinProperties        int                `json:"minProperties,omitempty"`
	Required             []string           `json:"required,omitempty"`
	Enum                 []interface{}      `json:"enum,omitempty"`
	Example              interface{}        `json:"example,omitempty"`
	Nullable             bool               `json:"nullable,omitempty"`
	ReadOnly             bool               `json:"readOnly,omitempty"`
	WriteOnly            bool               `json:"writeOnly,omitempty"`
	Deprecated           bool               `json:"deprecated,omitempty"`
	Ref                  string             `json:"$ref,omitempty"`
}

// Components 组件
type Components struct {
	Schemas         map[string]*Schema        `json:"schemas,omitempty"`
	Responses       map[string]Response       `json:"responses,omitempty"`
	Parameters      map[string]Parameter      `json:"parameters,omitempty"`
	Examples        map[string]Example        `json:"examples,omitempty"`
	RequestBodies   map[string]RequestBody    `json:"requestBodies,omitempty"`
	Headers         map[string]Header         `json:"headers,omitempty"`
	SecuritySchemes map[string]SecurityScheme `json:"securitySchemes,omitempty"`
	Links           map[string]Link           `json:"links,omitempty"`
	Callbacks       map[string]Callback       `json:"callbacks,omitempty"`
}

// Example 示例
type Example struct {
	Summary       string      `json:"summary,omitempty"`
	Description   string      `json:"description,omitempty"`
	Value         interface{} `json:"value,omitempty"`
	ExternalValue string      `json:"externalValue,omitempty"`
}

// Header 头部
type Header struct {
	Description     string      `json:"description,omitempty"`
	Required        bool        `json:"required,omitempty"`
	Deprecated      bool        `json:"deprecated,omitempty"`
	AllowEmptyValue bool        `json:"allowEmptyValue,omitempty"`
	Style           string      `json:"style,omitempty"`
	Explode         bool        `json:"explode,omitempty"`
	AllowReserved   bool        `json:"allowReserved,omitempty"`
	Schema          *Schema     `json:"schema,omitempty"`
	Example         interface{} `json:"example,omitempty"`
	Examples        map[string]Example `json:"examples,omitempty"`
}

// SecurityScheme 安全方案
type SecurityScheme struct {
	Type             string      `json:"type"`
	Description      string      `json:"description,omitempty"`
	Name             string      `json:"name,omitempty"`
	In               string      `json:"in,omitempty"`
	Scheme           string      `json:"scheme,omitempty"`
	BearerFormat     string      `json:"bearerFormat,omitempty"`
	Flows            OAuthFlows  `json:"flows,omitempty"`
	OpenIDConnectURL string      `json:"openIdConnectUrl,omitempty"`
}

// OAuthFlows OAuth流程
type OAuthFlows struct {
	Implicit          *OAuthFlow `json:"implicit,omitempty"`
	Password          *OAuthFlow `json:"password,omitempty"`
	ClientCredentials *OAuthFlow `json:"clientCredentials,omitempty"`
	AuthorizationCode *OAuthFlow `json:"authorizationCode,omitempty"`
}

// OAuthFlow OAuth流程
type OAuthFlow struct {
	AuthorizationURL string            `json:"authorizationUrl,omitempty"`
	TokenURL         string            `json:"tokenUrl,omitempty"`
	RefreshURL       string            `json:"refreshUrl,omitempty"`
	Scopes           map[string]string `json:"scopes"`
}

// SecurityRequirement 安全要求
type SecurityRequirement map[string][]string

// Tag 标签
type Tag struct {
	Name         string        `json:"name"`
	Description  string        `json:"description,omitempty"`
	ExternalDocs ExternalDocs  `json:"externalDocs,omitempty"`
}

// ExternalDocs 外部文档
type ExternalDocs struct {
	Description string `json:"description,omitempty"`
	URL         string `json:"url"`
}

// Link 链接
type Link struct {
	OperationRef string                 `json:"operationRef,omitempty"`
	OperationID  string                 `json:"operationId,omitempty"`
	Parameters   map[string]interface{} `json:"parameters,omitempty"`
	RequestBody  interface{}            `json:"requestBody,omitempty"`
	Description  string                 `json:"description,omitempty"`
	Server       *Server                `json:"server,omitempty"`
}

// Callback 回调
type Callback map[string]PathItem

// Encoding 编码
type Encoding struct {
	ContentType   string             `json:"contentType,omitempty"`
	Headers       map[string]Header  `json:"headers,omitempty"`
	Style         string             `json:"style,omitempty"`
	Explode       bool               `json:"explode,omitempty"`
	AllowReserved bool               `json:"allowReserved,omitempty"`
}

// APIDocGenerator API文档生成器
type APIDocGenerator struct {
	spec *OpenAPISpec
}

// NewAPIDocGenerator 创建API文档生成器
func NewAPIDocGenerator() *APIDocGenerator {
	return &APIDocGenerator{
		spec: &OpenAPISpec{
			OpenAPI: "3.0.3",
			Info: Info{
				Title:       "Image Backup API",
				Description: "图片备份系统API文档",
				Version:     "1.0.0",
				Contact: Contact{
					Name:  "Image Backup Team",
					Email: "<EMAIL>",
				},
				License: License{
					Name: "MIT",
					URL:  "https://opensource.org/licenses/MIT",
				},
			},
			Servers: []Server{
				{
					URL:         "https://api.imagebackup.com/v1",
					Description: "生产环境",
				},
				{
					URL:         "https://staging-api.imagebackup.com/v1",
					Description: "测试环境",
				},
			},
			Paths:      make(map[string]PathItem),
			Components: Components{
				Schemas:         make(map[string]*Schema),
				SecuritySchemes: make(map[string]SecurityScheme),
			},
			Security: []SecurityRequirement{
				{"bearerAuth": []string{}},
			},
			Tags: []Tag{
				{Name: "auth", Description: "用户认证"},
				{Name: "users", Description: "用户管理"},
				{Name: "images", Description: "图片管理"},
				{Name: "albums", Description: "相册管理"},
				{Name: "tags", Description: "标签管理"},
				{Name: "sharing", Description: "分享功能"},
				{Name: "search", Description: "搜索功能"},
				{Name: "processing", Description: "图片处理"},
			},
		},
	}
}

// AddPath 添加路径
func (adg *APIDocGenerator) AddPath(path, method string, operation Operation) {
	if adg.spec.Paths[path].Get == nil && 
	   adg.spec.Paths[path].Post == nil && 
	   adg.spec.Paths[path].Put == nil && 
	   adg.spec.Paths[path].Delete == nil {
		adg.spec.Paths[path] = PathItem{}
	}

	pathItem := adg.spec.Paths[path]
	switch strings.ToUpper(method) {
	case "GET":
		pathItem.Get = &operation
	case "POST":
		pathItem.Post = &operation
	case "PUT":
		pathItem.Put = &operation
	case "DELETE":
		pathItem.Delete = &operation
	case "PATCH":
		pathItem.Patch = &operation
	}
	adg.spec.Paths[path] = pathItem
}

// AddSchema 添加模式
func (adg *APIDocGenerator) AddSchema(name string, schema *Schema) {
	adg.spec.Components.Schemas[name] = schema
}

// GenerateFromStruct 从结构体生成模式
func (adg *APIDocGenerator) GenerateFromStruct(v interface{}) *Schema {
	return adg.generateSchemaFromType(reflect.TypeOf(v))
}

// generateSchemaFromType 从类型生成模式
func (adg *APIDocGenerator) generateSchemaFromType(t reflect.Type) *Schema {
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	schema := &Schema{}

	switch t.Kind() {
	case reflect.String:
		schema.Type = "string"
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		schema.Type = "integer"
	case reflect.Float32, reflect.Float64:
		schema.Type = "number"
	case reflect.Bool:
		schema.Type = "boolean"
	case reflect.Array, reflect.Slice:
		schema.Type = "array"
		schema.Items = adg.generateSchemaFromType(t.Elem())
	case reflect.Struct:
		schema.Type = "object"
		schema.Properties = make(map[string]*Schema)
		
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			if field.PkgPath != "" { // 跳过私有字段
				continue
			}
			
			jsonTag := field.Tag.Get("json")
			if jsonTag == "-" {
				continue
			}
			
			fieldName := field.Name
			if jsonTag != "" {
				parts := strings.Split(jsonTag, ",")
				if parts[0] != "" {
					fieldName = parts[0]
				}
			}
			
			fieldSchema := adg.generateSchemaFromType(field.Type)
			
			// 添加描述
			if desc := field.Tag.Get("description"); desc != "" {
				fieldSchema.Description = desc
			}
			
			// 添加验证规则
			if binding := field.Tag.Get("binding"); binding != "" {
				if strings.Contains(binding, "required") {
					schema.Required = append(schema.Required, fieldName)
				}
			}
			
			schema.Properties[fieldName] = fieldSchema
		}
	case reflect.Map:
		schema.Type = "object"
		schema.AdditionalProperties = adg.generateSchemaFromType(t.Elem())
	default:
		schema.Type = "string"
	}

	return schema
}

// GenerateSpec 生成完整规范
func (adg *APIDocGenerator) GenerateSpec() *OpenAPISpec {
	// 添加安全方案
	adg.spec.Components.SecuritySchemes["bearerAuth"] = SecurityScheme{
		Type:         "http",
		Scheme:       "bearer",
		BearerFormat: "JWT",
		Description:  "JWT Bearer Token",
	}

	return adg.spec
}

// ServeSwaggerUI 提供Swagger UI
func (adg *APIDocGenerator) ServeSwaggerUI(router *gin.Engine) {
	// 提供OpenAPI规范JSON
	router.GET("/api/docs/openapi.json", func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.JSON(http.StatusOK, adg.GenerateSpec())
	})

	// 提供Swagger UI
	router.GET("/api/docs", func(c *gin.Context) {
		html := `
<!DOCTYPE html>
<html>
<head>
    <title>Image Backup API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui.css" />
    <style>
        html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
        *, *:before, *:after { box-sizing: inherit; }
        body { margin:0; background: #fafafa; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '/api/docs/openapi.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout"
            });
        };
    </script>
</body>
</html>`
		c.Header("Content-Type", "text/html")
		c.String(http.StatusOK, html)
	})
}

// 预定义的常用响应
func StandardResponses() map[string]Response {
	return map[string]Response{
		"200": {
			Description: "成功",
			Content: map[string]MediaType{
				"application/json": {
					Schema: &Schema{
						Type: "object",
						Properties: map[string]*Schema{
							"code": {Type: "integer", Example: 200},
							"message": {Type: "string", Example: "success"},
							"data": {Type: "object"},
						},
					},
				},
			},
		},
		"400": {
			Description: "请求错误",
			Content: map[string]MediaType{
				"application/json": {
					Schema: &Schema{
						Type: "object",
						Properties: map[string]*Schema{
							"code": {Type: "integer", Example: 400},
							"message": {Type: "string", Example: "Bad Request"},
							"error": {Type: "string"},
						},
					},
				},
			},
		},
		"401": {
			Description: "未授权",
			Content: map[string]MediaType{
				"application/json": {
					Schema: &Schema{
						Type: "object",
						Properties: map[string]*Schema{
							"code": {Type: "integer", Example: 401},
							"message": {Type: "string", Example: "Unauthorized"},
						},
					},
				},
			},
		},
		"404": {
			Description: "资源不存在",
			Content: map[string]MediaType{
				"application/json": {
					Schema: &Schema{
						Type: "object",
						Properties: map[string]*Schema{
							"code": {Type: "integer", Example: 404},
							"message": {Type: "string", Example: "Not Found"},
						},
					},
				},
			},
		},
		"500": {
			Description: "服务器错误",
			Content: map[string]MediaType{
				"application/json": {
					Schema: &Schema{
						Type: "object",
						Properties: map[string]*Schema{
							"code": {Type: "integer", Example: 500},
							"message": {Type: "string", Example: "Internal Server Error"},
						},
					},
				},
			},
		},
	}
}
