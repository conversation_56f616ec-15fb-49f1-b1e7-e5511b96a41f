// 存储管理相关类型定义

export type StorageProvider = 
  | 'local' 
  | 'ali_oss' 
  | 'tencent_cos' 
  | 'qiniu_kodo' 
  | 'aws_s3' 
  | 'ftp' 
  | 'sftp';

export interface StorageConfig {
  id: number;
  name: string;
  provider: StorageProvider;
  is_default: boolean;
  is_enabled: boolean;
  access_url: string;
  storage_path: string;
  config: any;
  created_at: string;
  updated_at: string;
}

export interface StorageConfigRequest {
  name: string;
  provider: StorageProvider;
  is_default: boolean;
  is_enabled: boolean;
  access_url: string;
  storage_path: string;
  config: any;
}

export interface LocalStorageConfig {
  upload_path: string;
  max_size: number;
}

export interface AliOSSConfig {
  access_key_id: string;
  access_key_secret: string;
  endpoint: string;
  bucket_name: string;
  region: string;
  path_prefix: string;
}

export interface TencentCOSConfig {
  secret_id: string;
  secret_key: string;
  region: string;
  bucket_name: string;
  path_prefix: string;
}

export interface QiniuKodoConfig {
  access_key: string;
  secret_key: string;
  bucket_name: string;
  domain: string;
  zone: string;
  path_prefix: string;
}

export interface AWSS3Config {
  access_key_id: string;
  secret_access_key: string;
  region: string;
  bucket_name: string;
  path_prefix: string;
}

export interface FTPConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  remote_path: string;
  passive_mode: boolean;
}

export interface SFTPConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  private_key: string;
  remote_path: string;
}

export interface StorageTestRequest {
  provider: StorageProvider;
  config: any;
}

export interface StorageTestResponse {
  success: boolean;
  message: string;
  details?: string;
}

export interface StorageStats {
  total_files: number;
  total_size: number;
  used_quota: number;
  total_quota: number;
}

// 存储提供商信息
export const STORAGE_PROVIDERS = [
  {
    value: 'local' as StorageProvider,
    label: '本地存储',
    icon: 'bi-hdd',
    description: '将文件存储在服务器本地磁盘'
  },
  {
    value: 'ali_oss' as StorageProvider,
    label: '阿里云OSS',
    icon: 'bi-cloud',
    description: '阿里云对象存储服务'
  },
  {
    value: 'tencent_cos' as StorageProvider,
    label: '腾讯云COS',
    icon: 'bi-cloud',
    description: '腾讯云对象存储服务'
  },
  {
    value: 'qiniu_kodo' as StorageProvider,
    label: '七牛云Kodo',
    icon: 'bi-cloud',
    description: '七牛云对象存储服务'
  },
  {
    value: 'aws_s3' as StorageProvider,
    label: 'AWS S3',
    icon: 'bi-cloud',
    description: 'Amazon S3对象存储服务'
  },
  {
    value: 'ftp' as StorageProvider,
    label: 'FTP服务器',
    icon: 'bi-server',
    description: 'FTP文件传输协议'
  },
  {
    value: 'sftp' as StorageProvider,
    label: 'SFTP服务器',
    icon: 'bi-shield-lock',
    description: 'SSH文件传输协议'
  }
];

// 获取存储提供商信息
export const getStorageProviderInfo = (provider: StorageProvider) => {
  return STORAGE_PROVIDERS.find(p => p.value === provider) || STORAGE_PROVIDERS[0];
};

// 默认配置模板
export const getDefaultConfig = (provider: StorageProvider): any => {
  switch (provider) {
    case 'local':
      return {
        upload_path: './uploads',
        max_size: 104857600 // 100MB
      } as LocalStorageConfig;
    
    case 'ali_oss':
      return {
        access_key_id: '',
        access_key_secret: '',
        endpoint: '',
        bucket_name: '',
        region: '',
        path_prefix: 'images/'
      } as AliOSSConfig;
    
    case 'tencent_cos':
      return {
        secret_id: '',
        secret_key: '',
        region: '',
        bucket_name: '',
        path_prefix: 'images/'
      } as TencentCOSConfig;
    
    case 'qiniu_kodo':
      return {
        access_key: '',
        secret_key: '',
        bucket_name: '',
        domain: '',
        zone: 'z0',
        path_prefix: 'images/'
      } as QiniuKodoConfig;
    
    case 'aws_s3':
      return {
        access_key_id: '',
        secret_access_key: '',
        region: 'us-east-1',
        bucket_name: '',
        path_prefix: 'images/'
      } as AWSS3Config;
    
    case 'ftp':
      return {
        host: '',
        port: 21,
        username: '',
        password: '',
        remote_path: '/uploads',
        passive_mode: true
      } as FTPConfig;
    
    case 'sftp':
      return {
        host: '',
        port: 22,
        username: '',
        password: '',
        private_key: '',
        remote_path: '/uploads'
      } as SFTPConfig;
    
    default:
      return {};
  }
};
