package middleware

import (
	"net/http"
	"strconv"

	"cloudbed/internal/core/repository"

	"github.com/gin-gonic/gin"
)

// RequirePermission 权限检查中间件
func RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// 检查用户权限
		hasPermission, err := dao.CheckUserPermission(userID.(uint), permission)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check permission"})
			c.Abort()
			return
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRole 角色检查中间件
func RequireRole(roleName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// 获取用户信息
		user, err := dao.GetUserWithRole(userID.(uint))
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
			c.Abort()
			return
		}

		if user.Role.Name != roleName {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient role"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdmin 管理员权限中间件
func RequireAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// 获取用户信息
		user, err := dao.GetUserWithRole(userID.(uint))
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
			c.Abort()
			return
		}

		if !user.IsAdmin() {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireSuperAdmin 超级管理员权限中间件
func RequireSuperAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// 获取用户信息
		user, err := dao.GetUserWithRole(userID.(uint))
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
			c.Abort()
			return
		}

		if !user.IsSuperAdmin() {
			c.JSON(http.StatusForbidden, gin.H{"error": "Super admin access required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CheckResourceOwnership 检查资源所有权中间件
func CheckResourceOwnership(resourceType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// 获取用户信息
		user, err := dao.GetUserWithRole(userID.(uint))
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
			c.Abort()
			return
		}

		// 管理员可以访问所有资源
		if user.IsAdmin() {
			c.Next()
			return
		}

		// 获取资源ID
		resourceIDStr := c.Param("id")
		if resourceIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Resource ID required"})
			c.Abort()
			return
		}

		resourceID, err := strconv.ParseUint(resourceIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID"})
			c.Abort()
			return
		}

		// 检查资源所有权
		var isOwner bool
		switch resourceType {
		case "image":
			image, err := dao.GetImageByID(uint(resourceID))
			if err != nil {
				c.JSON(http.StatusNotFound, gin.H{"error": "Image not found"})
				c.Abort()
				return
			}
			isOwner = image.UserID == userID.(uint)
		case "album":
			album, err := dao.GetAlbumByID(uint(resourceID))
			if err != nil {
				c.JSON(http.StatusNotFound, gin.H{"error": "Album not found"})
				c.Abort()
				return
			}
			isOwner = album.UserID == userID.(uint)
		case "user":
			// 用户只能管理自己的信息
			isOwner = uint(resourceID) == userID.(uint)
		default:
			c.JSON(http.StatusBadRequest, gin.H{"error": "Unknown resource type"})
			c.Abort()
			return
		}

		if !isOwner {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied: not resource owner"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireActiveUser 要求用户为活跃状态
func RequireActiveUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// 获取用户信息
		user, err := dao.GetUserWithRole(userID.(uint))
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
			c.Abort()
			return
		}

		if !user.IsActive() {
			c.JSON(http.StatusForbidden, gin.H{"error": "Account is not active"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// SetUserContext 设置用户上下文信息
func SetUserContext() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		userID, exists := c.Get("user_id")
		if exists {
			// 获取完整的用户信息
			user, err := dao.GetUserWithRole(userID.(uint))
			if err == nil {
				c.Set("user", user)
				c.Set("user_role", user.Role.Name)
				c.Set("is_admin", user.IsAdmin())
				c.Set("is_super_admin", user.IsSuperAdmin())
			}
		}

		c.Next()
	}
}
