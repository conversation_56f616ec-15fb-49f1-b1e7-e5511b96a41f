<template>
  <div class="tag-manager">
    <!-- 标签输入区域 -->
    <div class="tag-input-section">
      <div class="tag-input-wrapper">
        <input
          ref="tagInput"
          v-model="newTagName"
          type="text"
          class="tag-input"
          placeholder="输入标签名称..."
          @keydown.enter="addTag"
          @keydown.comma.prevent="addTag"
          @input="onTagInput"
          @focus="showSuggestions = true"
          @blur="onInputBlur"
        />
        <button
          class="add-tag-btn"
          @click="addTag"
          :disabled="!newTagName.trim()"
        >
          <i class="bi bi-plus"></i>
          添加
        </button>
      </div>

      <!-- 标签建议 -->
      <div
        v-if="showSuggestions && suggestions.length > 0"
        class="suggestions-dropdown"
      >
        <div class="suggestions-header">
          <span>建议标签</span>
        </div>
        <div
          v-for="(suggestion, index) in suggestions"
          :key="index"
          class="suggestion-item"
          @click="selectSuggestion(suggestion)"
        >
          <div class="suggestion-tag">
            <span
              class="tag-color"
              :style="{ backgroundColor: suggestion.color }"
            ></span>
            <span class="tag-name">{{ suggestion.name }}</span>
          </div>
          <span class="suggestion-source">{{ getSuggestionSourceText(suggestion.source) }}</span>
        </div>
      </div>
    </div>

    <!-- 当前标签列表 -->
    <div class="current-tags">
      <div class="tags-header">
        <span>当前标签 ({{ currentTags.length }})</span>
        <button
          v-if="currentTags.length > 0"
          class="clear-all-btn"
          @click="clearAllTags"
        >
          清空
        </button>
      </div>
      
      <div class="tags-list">
        <div
          v-for="tag in currentTags"
          :key="tag.id"
          class="tag-item"
          :style="{ borderColor: tag.color }"
        >
          <span
            class="tag-color"
            :style="{ backgroundColor: tag.color }"
          ></span>
          <span class="tag-name">{{ tag.name }}</span>
          <button
            class="remove-tag-btn"
            @click="removeTag(tag)"
            title="移除标签"
          >
            <i class="bi bi-x"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 热门标签 -->
    <div v-if="popularTags.length > 0" class="popular-tags">
      <div class="popular-header">
        <span>热门标签</span>
      </div>
      <div class="popular-tags-list">
        <button
          v-for="tag in popularTags"
          :key="tag.id"
          class="popular-tag"
          :style="{ 
            borderColor: tag.color,
            color: tag.color
          }"
          @click="addExistingTag(tag)"
          :disabled="isTagSelected(tag)"
        >
          <span
            class="tag-color"
            :style="{ backgroundColor: tag.color }"
          ></span>
          {{ tag.name }}
          <span class="usage-count">({{ tag.usage_count }})</span>
        </button>
      </div>
    </div>

    <!-- 标签颜色选择器 -->
    <div v-if="showColorPicker" class="color-picker-modal" @click="showColorPicker = false">
      <div class="color-picker-content" @click.stop>
        <div class="color-picker-header">
          <h4>选择标签颜色</h4>
          <button class="close-btn" @click="showColorPicker = false">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="color-options">
          <button
            v-for="color in defaultColors"
            :key="color"
            class="color-option"
            :style="{ backgroundColor: color }"
            :class="{ selected: selectedColor === color }"
            @click="selectColor(color)"
          ></button>
        </div>
        <div class="custom-color">
          <label>自定义颜色:</label>
          <input
            v-model="selectedColor"
            type="color"
            class="color-input"
          />
        </div>
        <div class="color-picker-actions">
          <button class="cancel-btn" @click="showColorPicker = false">取消</button>
          <button class="confirm-btn" @click="confirmColor">确认</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

interface Tag {
  id: number
  name: string
  color: string
  usage_count?: number
}

interface TagSuggestion {
  name: string
  color: string
  confidence: number
  source: string
}

interface Props {
  modelValue: Tag[]
  imageId?: number
  placeholder?: string
  maxTags?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入标签名称...',
  maxTags: 20
})

const emit = defineEmits<{
  'update:modelValue': [tags: Tag[]]
  'tag-added': [tag: Tag]
  'tag-removed': [tag: Tag]
}>()

// 响应式数据
const tagInput = ref<HTMLInputElement>()
const newTagName = ref('')
const showSuggestions = ref(false)
const showColorPicker = ref(false)
const selectedColor = ref('#007bff')
const pendingTagName = ref('')

// 标签数据
const currentTags = ref<Tag[]>([...props.modelValue])
const suggestions = ref<TagSuggestion[]>([])
const popularTags = ref<Tag[]>([])

// 默认颜色选项
const defaultColors = [
  '#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8',
  '#6f42c1', '#e83e8c', '#fd7e14', '#20c997', '#6c757d'
]

// 计算属性
const isTagSelected = (tag: Tag) => {
  return currentTags.value.some(t => t.name.toLowerCase() === tag.name.toLowerCase())
}

// 方法
const onTagInput = async () => {
  if (newTagName.value.length >= 2) {
    await loadSuggestions()
    showSuggestions.value = true
  } else {
    suggestions.value = []
    showSuggestions.value = false
  }
}

const loadSuggestions = async () => {
  try {
    // TODO: 调用API获取标签建议
    // const response = await api.getTagSuggestions(newTagName.value)
    // suggestions.value = response.data
    
    // 模拟数据
    suggestions.value = [
      { name: '风景', color: '#28a745', confidence: 0.8, source: 'popular' },
      { name: '旅行', color: '#17a2b8', confidence: 0.7, source: 'filename' },
      { name: '自然', color: '#20c997', confidence: 0.6, source: 'similar' }
    ].filter(s => s.name.toLowerCase().includes(newTagName.value.toLowerCase()))
  } catch (error) {
    console.error('Failed to load suggestions:', error)
  }
}

const loadPopularTags = async () => {
  try {
    // TODO: 调用API获取热门标签
    // const response = await api.getPopularTags()
    // popularTags.value = response.data
    
    // 模拟数据
    popularTags.value = [
      { id: 1, name: '风景', color: '#28a745', usage_count: 25 },
      { id: 2, name: '人物', color: '#ffc107', usage_count: 18 },
      { id: 3, name: '旅行', color: '#17a2b8', usage_count: 15 },
      { id: 4, name: '食物', color: '#fd7e14', usage_count: 12 },
      { id: 5, name: '动物', color: '#6f42c1', usage_count: 10 }
    ]
  } catch (error) {
    console.error('Failed to load popular tags:', error)
  }
}

const addTag = () => {
  const tagName = newTagName.value.trim()
  if (!tagName) return
  
  // 检查是否已存在
  if (isTagSelected({ name: tagName } as Tag)) {
    newTagName.value = ''
    return
  }
  
  // 检查数量限制
  if (currentTags.value.length >= props.maxTags) {
    // TODO: 显示错误提示
    return
  }
  
  // 显示颜色选择器
  pendingTagName.value = tagName
  selectedColor.value = getRandomColor()
  showColorPicker.value = true
}

const confirmColor = () => {
  const newTag: Tag = {
    id: Date.now(), // 临时ID
    name: pendingTagName.value,
    color: selectedColor.value
  }
  
  currentTags.value.push(newTag)
  emit('update:modelValue', currentTags.value)
  emit('tag-added', newTag)
  
  newTagName.value = ''
  pendingTagName.value = ''
  showColorPicker.value = false
  hideSuggestions()
}

const selectSuggestion = (suggestion: TagSuggestion) => {
  if (isTagSelected({ name: suggestion.name } as Tag)) {
    hideSuggestions()
    return
  }
  
  const newTag: Tag = {
    id: Date.now(),
    name: suggestion.name,
    color: suggestion.color
  }
  
  currentTags.value.push(newTag)
  emit('update:modelValue', currentTags.value)
  emit('tag-added', newTag)
  
  newTagName.value = ''
  hideSuggestions()
}

const addExistingTag = (tag: Tag) => {
  if (isTagSelected(tag)) return
  
  currentTags.value.push({ ...tag })
  emit('update:modelValue', currentTags.value)
  emit('tag-added', tag)
}

const removeTag = (tag: Tag) => {
  const index = currentTags.value.findIndex(t => t.id === tag.id)
  if (index > -1) {
    currentTags.value.splice(index, 1)
    emit('update:modelValue', currentTags.value)
    emit('tag-removed', tag)
  }
}

const clearAllTags = () => {
  const removedTags = [...currentTags.value]
  currentTags.value = []
  emit('update:modelValue', currentTags.value)
  
  removedTags.forEach(tag => {
    emit('tag-removed', tag)
  })
}

const selectColor = (color: string) => {
  selectedColor.value = color
}

const getRandomColor = () => {
  return defaultColors[Math.floor(Math.random() * defaultColors.length)]
}

const getSuggestionSourceText = (source: string) => {
  const sourceMap: Record<string, string> = {
    'filename': '文件名',
    'popular': '热门',
    'similar': '相似'
  }
  return sourceMap[source] || '推荐'
}

const hideSuggestions = () => {
  showSuggestions.value = false
}

const onInputBlur = () => {
  // 延迟隐藏建议，以便点击建议项
  setTimeout(() => {
    hideSuggestions()
  }, 200)
}

// 监听外部标签变化
watch(() => props.modelValue, (newTags) => {
  currentTags.value = [...newTags]
}, { deep: true })

// 生命周期
onMounted(() => {
  loadPopularTags()
})
</script>

<style scoped>
.tag-manager {
  width: 100%;
}

.tag-input-section {
  position: relative;
  margin-bottom: 20px;
}

.tag-input-wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tag-input {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.tag-input:focus {
  outline: none;
  border-color: #007bff;
}

.add-tag-btn {
  padding: 10px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-tag-btn:hover:not(:disabled) {
  background: #0056b3;
}

.add-tag-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.suggestions-header {
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background: #f8f9fa;
}

.suggestion-tag {
  display: flex;
  align-items: center;
  gap: 8px;
}

.suggestion-source {
  font-size: 0.75rem;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 10px;
}

.current-tags {
  margin-bottom: 20px;
}

.tags-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #495057;
}

.clear-all-btn {
  padding: 4px 8px;
  background: none;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  color: #6c757d;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.clear-all-btn:hover {
  background: #f8f9fa;
  border-color: #dc3545;
  color: #dc3545;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: white;
  border: 2px solid;
  border-radius: 16px;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.tag-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tag-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.tag-name {
  font-weight: 500;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.remove-tag-btn:hover {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.popular-tags {
  margin-bottom: 20px;
}

.popular-header {
  margin-bottom: 12px;
  font-weight: 500;
  color: #495057;
}

.popular-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.popular-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: white;
  border: 1px solid;
  border-radius: 16px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.popular-tag:hover:not(:disabled) {
  background: rgba(0, 123, 255, 0.1);
  transform: translateY(-1px);
}

.popular-tag:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.usage-count {
  font-size: 0.75rem;
  opacity: 0.7;
}

.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.color-picker-content {
  background: white;
  border-radius: 12px;
  padding: 20px;
  max-width: 400px;
  width: 90%;
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.color-picker-header h4 {
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
}

.color-options {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  margin-bottom: 20px;
}

.color-option {
  width: 40px;
  height: 40px;
  border: 3px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.selected {
  border-color: #333;
  transform: scale(1.1);
}

.custom-color {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.color-input {
  width: 50px;
  height: 30px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
}

.color-picker-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-btn,
.confirm-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.confirm-btn {
  background: #007bff;
  color: white;
}

.confirm-btn:hover {
  background: #0056b3;
}
</style>
