package controllers

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"
	"cloudbed/pkg/response"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

// CreateShare 创建分享
func CreateShare(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req models.ShareRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 验证资源权限
	var resourceExists bool
	switch req.ResourceType {
	case "image":
		if _, err := dao.GetImageByIDAndUserID(req.ResourceID, userID.(uint)); err == nil {
			resourceExists = true
		}
	case "album":
		if _, err := dao.GetAlbumByIDAndUserID(req.ResourceID, userID.(uint)); err == nil {
			resourceExists = true
		}
	}

	if !resourceExists {
		response.NotFound(c, "资源不存在或无权限")
		return
	}

	// 创建分享记录
	share := &models.Share{
		ShareType:   models.ShareType(req.ResourceType),
		ResourceID:  req.ResourceID,
		OwnerID:     userID.(uint),
		Title:       req.Title,
		Description: req.Description,
		Permissions: req.Permissions,
		AllowGuest:  req.AllowGuest,
		MaxViews:    req.MaxViews,
		IsPublic:    req.IsPublic,
		Status:      models.ShareStatusActive,
	}

	// 设置密码
	if req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			response.InternalServerError(c, "密码加密失败")
			return
		}
		share.Password = string(hashedPassword)
		share.HasPassword = true
	}

	// 设置过期时间
	if req.ExpiresIn > 0 {
		expiresAt := time.Now().Add(time.Duration(req.ExpiresIn) * time.Hour)
		share.ExpiresAt = &expiresAt
	}

	// 设置默认权限
	if len(share.Permissions) == 0 {
		share.Permissions = []models.SharePermission{models.PermissionView}
	}

	// 保存到数据库
	if err := dao.CreateShare(share); err != nil {
		response.InternalServerErrorWithDetail(c, "创建分享失败", err.Error())
		return
	}

	// 构建分享URL
	baseURL := getBaseURL(c)
	shareURL := fmt.Sprintf("%s/share/%s", baseURL, share.ShareToken)
	qrCodeURL := fmt.Sprintf("%s/api/share/qr/%s", baseURL, share.ShareToken)

	response.SuccessWithMessage(c, "分享创建成功", models.ShareResponse{
		Share:     *share,
		ShareURL:  shareURL,
		QRCodeURL: qrCodeURL,
	})
}

// GetShare 获取分享信息
func GetShare(c *gin.Context) {
	token := c.Param("token")
	if token == "" {
		response.BadRequest(c, "分享令牌不能为空")
		return
	}

	// 获取分享信息
	share, err := dao.GetShareByToken(token)
	if err != nil {
		response.NotFound(c, "分享不存在或已失效")
		return
	}

	// 检查分享状态
	if !share.IsActive() {
		response.BadRequest(c, "分享已过期或被禁用")
		return
	}

	// 验证密码（如果需要）
	password := c.Query("password")
	if share.HasPassword {
		if password == "" {
			response.Unauthorized(c, "需要访问密码")
			return
		}
		if err := bcrypt.CompareHashAndPassword([]byte(share.Password), []byte(password)); err != nil {
			response.Unauthorized(c, "访问密码错误")
			return
		}
	}

	// 记录访问
	go recordAccess(c, share.ID, "view")

	// 获取资源信息
	var resource interface{}
	switch share.ShareType {
	case models.ShareTypeImage:
		if image, err := dao.GetImageByID(share.ResourceID); err == nil {
			resource = image
		}
	case models.ShareTypeAlbum:
		if album, err := dao.GetAlbumByID(share.ResourceID); err == nil {
			// 获取相册中的图片
			if images, err := dao.GetImagesByAlbumID(album.ID); err == nil {
				albumWithImages := map[string]interface{}{
					"album":  album,
					"images": images,
				}
				resource = albumWithImages
			} else {
				resource = album
			}
		}
	}

	response.Success(c, gin.H{
		"share":    share,
		"resource": resource,
	})
}

// GetMyShares 获取我的分享列表
func GetMyShares(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	shares, total, err := dao.GetSharesByOwner(userID.(uint), page, pageSize)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取分享列表失败", err.Error())
		return
	}

	// 构建分享URL
	baseURL := getBaseURL(c)
	var shareList []map[string]interface{}
	for _, share := range shares {
		shareURL := fmt.Sprintf("%s/share/%s", baseURL, share.ShareToken)
		shareList = append(shareList, map[string]interface{}{
			"share":     share,
			"share_url": shareURL,
		})
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	response.Success(c, gin.H{
		"shares":      shareList,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
	})
}

// UpdateShare 更新分享
func UpdateShare(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	shareID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "分享ID格式错误")
		return
	}

	// 获取分享信息
	share, err := dao.GetShareByID(uint(shareID))
	if err != nil {
		response.NotFound(c, "分享不存在")
		return
	}

	// 验证权限
	if share.OwnerID != userID.(uint) {
		response.Forbidden(c, "无权限修改此分享")
		return
	}

	var req models.ShareRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 更新分享信息
	share.Title = req.Title
	share.Description = req.Description
	share.Permissions = req.Permissions
	share.AllowGuest = req.AllowGuest
	share.MaxViews = req.MaxViews
	share.IsPublic = req.IsPublic

	// 更新密码
	if req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			response.InternalServerError(c, "密码加密失败")
			return
		}
		share.Password = string(hashedPassword)
		share.HasPassword = true
	} else {
		share.Password = ""
		share.HasPassword = false
	}

	// 更新过期时间
	if req.ExpiresIn > 0 {
		expiresAt := time.Now().Add(time.Duration(req.ExpiresIn) * time.Hour)
		share.ExpiresAt = &expiresAt
	} else {
		share.ExpiresAt = nil
	}

	// 保存更新
	if err := dao.UpdateShare(share); err != nil {
		response.InternalServerErrorWithDetail(c, "更新分享失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "分享更新成功", share)
}

// DeleteShare 删除分享
func DeleteShare(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	shareID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "分享ID格式错误")
		return
	}

	// 获取分享信息
	share, err := dao.GetShareByID(uint(shareID))
	if err != nil {
		response.NotFound(c, "分享不存在")
		return
	}

	// 验证权限
	if share.OwnerID != userID.(uint) {
		response.Forbidden(c, "无权限删除此分享")
		return
	}

	// 删除分享
	if err := dao.DeleteShare(uint(shareID)); err != nil {
		response.InternalServerErrorWithDetail(c, "删除分享失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "分享删除成功", nil)
}

// DownloadSharedResource 下载分享的资源
func DownloadSharedResource(c *gin.Context) {
	token := c.Param("token")
	if token == "" {
		response.BadRequest(c, "分享令牌不能为空")
		return
	}

	// 获取分享信息
	share, err := dao.GetShareByToken(token)
	if err != nil {
		response.NotFound(c, "分享不存在或已失效")
		return
	}

	// 检查分享状态
	if !share.IsActive() {
		response.BadRequest(c, "分享已过期或被禁用")
		return
	}

	// 检查下载权限
	if !share.HasPermission(models.PermissionDownload) {
		response.Forbidden(c, "无下载权限")
		return
	}

	// 验证密码（如果需要）
	password := c.Query("password")
	if share.HasPassword {
		if password == "" {
			response.Unauthorized(c, "需要访问密码")
			return
		}
		if err := bcrypt.CompareHashAndPassword([]byte(share.Password), []byte(password)); err != nil {
			response.Unauthorized(c, "访问密码错误")
			return
		}
	}

	// 记录下载访问
	go recordAccess(c, share.ID, "download")

	// 处理下载
	switch share.ShareType {
	case models.ShareTypeImage:
		downloadImage(c, share.ResourceID)
	case models.ShareTypeAlbum:
		downloadAlbum(c, share.ResourceID)
	default:
		response.BadRequest(c, "不支持的资源类型")
	}
}

// GetShareStatistics 获取分享统计
func GetShareStatistics(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	shareID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "分享ID格式错误")
		return
	}

	// 获取分享信息
	share, err := dao.GetShareByID(uint(shareID))
	if err != nil {
		response.NotFound(c, "分享不存在")
		return
	}

	// 验证权限
	if share.OwnerID != userID.(uint) {
		response.Forbidden(c, "无权限查看此分享统计")
		return
	}

	// 获取统计数据
	stats, err := dao.GetShareStatistics(uint(shareID))
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取统计数据失败", err.Error())
		return
	}

	response.Success(c, stats)
}

// 辅助函数
func getBaseURL(c *gin.Context) string {
	scheme := "http"
	if c.Request.TLS != nil {
		scheme = "https"
	}
	return fmt.Sprintf("%s://%s", scheme, c.Request.Host)
}

func recordAccess(c *gin.Context, shareID uint, action string) {
	access := &models.ShareAccess{
		ShareID:   shareID,
		IPAddress: getClientIP(c),
		UserAgent: c.GetHeader("User-Agent"),
		Referer:   c.GetHeader("Referer"),
		Action:    action,
	}

	// 获取用户ID（如果已登录）
	if userID, exists := c.Get("user_id"); exists {
		uid := userID.(uint)
		access.UserID = &uid
	}

	// 简化的地理位置信息（实际应用中可以使用IP地理位置服务）
	access.Country = "Unknown"
	access.City = "Unknown"

	// 记录访问
	dao.RecordShareAccess(access)

	// 更新分享访问次数
	if action == "view" {
		dao.UpdateShareViewCount(shareID)
	} else if action == "download" {
		dao.UpdateShareDownloadCount(shareID)
	}
}

func getClientIP(c *gin.Context) string {
	// 尝试从各种头部获取真实IP
	ip := c.GetHeader("X-Forwarded-For")
	if ip != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(ip, ",")
		return strings.TrimSpace(ips[0])
	}

	ip = c.GetHeader("X-Real-IP")
	if ip != "" {
		return ip
	}

	return c.ClientIP()
}

func downloadImage(c *gin.Context, imageID uint) {
	// 获取图片信息
	image, err := dao.GetImageByID(imageID)
	if err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 设置下载头部
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", image.Name))
	c.Header("Content-Type", "application/octet-stream")

	// 提供文件下载
	c.File("./uploads/" + image.Name)
}

func downloadAlbum(c *gin.Context, albumID uint) {
	// 获取相册信息
	album, err := dao.GetAlbumByID(albumID)
	if err != nil {
		response.NotFound(c, "相册不存在")
		return
	}

	// 这里应该创建ZIP文件包含相册中的所有图片
	// 简化实现，返回相册信息
	response.Success(c, gin.H{
		"message": "相册下载功能开发中",
		"album":   album,
	})
}
