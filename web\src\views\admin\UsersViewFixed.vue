<template>
  <div class="users-view">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2><i class="bi bi-people"></i> 用户管理</h2>
          <p class="page-subtitle">管理系统用户和权限</p>
        </div>
        <div class="header-right">
          <button class="btn btn-secondary" @click="fetchUsers">
            <i class="bi bi-arrow-clockwise"></i> 刷新
          </button>
          <button class="btn btn-primary" @click="showCreateModal = true">
            <i class="bi bi-plus"></i> 添加用户
          </button>
        </div>
      </div>
    </div>

    <!-- 调试信息 (可选) -->
    <div v-if="showDebug" class="debug-info">
      <h4>调试信息</h4>
      <p><strong>Loading:</strong> {{ loading }}</p>
      <p><strong>Error:</strong> {{ error }}</p>
      <p><strong>Users count:</strong> {{ users.length }}</p>
      <button @click="fetchUsers" class="btn btn-sm btn-primary">手动刷新</button>
      <details>
        <summary>原始数据</summary>
        <pre>{{ JSON.stringify(users, null, 2) }}</pre>
      </details>
    </div>

    <!-- 用户列表 -->
    <div class="users-table-container">
      <table class="users-table">
        <thead>
          <tr>
            <th>用户</th>
            <th>角色</th>
            <th>状态</th>
            <th>存储使用</th>
            <th>图片数量</th>
            <th>相册数量</th>
            <th>最后登录</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <!-- 加载状态 -->
          <tr v-if="loading">
            <td colspan="8" class="text-center">
              <div class="loading-spinner">
                <i class="bi bi-arrow-clockwise spinning"></i>
                <span>加载中...</span>
              </div>
            </td>
          </tr>
          
          <!-- 错误状态 -->
          <tr v-else-if="error">
            <td colspan="8" class="text-center">
              <div class="error-state">
                <i class="bi bi-exclamation-triangle"></i>
                <span>{{ error }}</span>
                <button class="btn btn-sm btn-primary" @click="fetchUsers">重试</button>
              </div>
            </td>
          </tr>
          
          <!-- 无数据状态 -->
          <tr v-else-if="users.length === 0">
            <td colspan="8" class="text-center">
              <div class="no-data">
                <i class="bi bi-inbox"></i>
                <span>暂无用户数据</span>
              </div>
            </td>
          </tr>
          
          <!-- 用户数据 -->
          <tr v-else v-for="(user, index) in users" :key="index">
            <td>
              <div class="user-info">
                <div class="user-avatar">
                  <i class="bi bi-person-circle"></i>
                </div>
                <div class="user-details">
                  <div class="user-email">{{ getUserEmail(user) }}</div>
                  <div class="user-id">ID: {{ getUserId(user) }}</div>
                </div>
              </div>
            </td>
            <td>
              <span class="role-badge">
                {{ getUserRole(user) }}
              </span>
            </td>
            <td>
              <span class="status-badge" :class="getStatusClass(getUserStatus(user))">
                {{ getStatusText(getUserStatus(user)) }}
              </span>
            </td>
            <td>
              <div class="storage-info">
                <div class="storage-text">
                  <span class="storage-used">{{ formatBytes(getUserStorageUsed(user)) }}</span>
                  <span class="storage-separator">/</span>
                  <span class="storage-quota">{{ formatBytes(getUserStorageQuota(user)) }}</span>
                </div>
                <div class="storage-progress">
                  <div class="progress-bar">
                    <div 
                      class="progress-fill" 
                      :style="{ width: getStoragePercentage(user) + '%' }"
                      :class="getStorageProgressClass(user)"
                    ></div>
                  </div>
                  <span class="progress-text">{{ getStoragePercentage(user).toFixed(1) }}%</span>
                </div>
              </div>
            </td>
            <td>
              <span class="count-badge">{{ user.image_count || 0 }}</span>
            </td>
            <td>
              <span class="count-badge">{{ user.album_count || 0 }}</span>
            </td>
            <td>
              <span class="last-login">{{ getUserLastLogin(user) }}</span>
            </td>
            <td>
              <div class="action-buttons">
                <button class="btn btn-sm btn-outline-primary" @click="editUser(user)">
                  <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" @click="deleteUser(user)">
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 通知 -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import api from '../../services/api';
import { formatBytes } from '../../types/storage';

// 响应式数据
const loading = ref(false);
const error = ref('');
const users = ref<any[]>([]);
const showDebug = ref(false); // 设为false隐藏调试信息
const showCreateModal = ref(false);

const notification = reactive({
  show: false,
  message: '',
  type: 'success' as 'success' | 'error'
});

// 获取用户数据
const fetchUsers = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    console.log('获取用户数据...');
    const response = await api.getAllUsersWithStats();
    console.log('API响应:', response);
    
    users.value = response.users || [];
    console.log('用户数据:', users.value);
  } catch (err: any) {
    console.error('获取用户数据失败:', err);
    error.value = err.response?.data?.error || err.message || '获取数据失败';
    showNotification('加载用户列表失败', 'error');
  } finally {
    loading.value = false;
  }
};

// 工具函数
const getUserId = (user: any) => {
  return user.user?.id || user.id || 'N/A';
};

const getUserEmail = (user: any) => {
  return user.user?.email || user.email || 'N/A';
};

const getUserRole = (user: any) => {
  return user.user?.role?.display_name || user.role?.display_name || 'N/A';
};

const getUserStatus = (user: any) => {
  return user.user?.status || user.status || 'unknown';
};

const getUserStorageUsed = (user: any) => {
  return user.user?.storage_used || user.storage_used || 0;
};

const getUserStorageQuota = (user: any) => {
  return user.user?.storage_quota || user.storage_quota || 0;
};

const getUserLastLogin = (user: any) => {
  const lastLogin = user.user?.last_login_at || user.last_login_at;
  return lastLogin ? new Date(lastLogin).toLocaleString('zh-CN') : '从未登录';
};

const getStoragePercentage = (user: any) => {
  const used = getUserStorageUsed(user);
  const quota = getUserStorageQuota(user);
  if (quota === 0) return 0;
  return Math.min((used / quota) * 100, 100);
};

const getStorageProgressClass = (user: any) => {
  const percentage = getStoragePercentage(user);
  if (percentage >= 90) return 'progress-critical';
  if (percentage >= 80) return 'progress-warning';
  return 'progress-normal';
};

const getStatusClass = (status: string) => {
  const classes: { [key: string]: string } = {
    'active': 'status-active',
    'inactive': 'status-inactive',
    'suspended': 'status-suspended'
  };
  return classes[status] || 'status-default';
};

const getStatusText = (status: string) => {
  const texts: { [key: string]: string } = {
    'active': '活跃',
    'inactive': '非活跃',
    'suspended': '暂停'
  };
  return texts[status] || status;
};

// 操作函数
const editUser = (user: any) => {
  console.log('编辑用户:', user);
  // TODO: 实现编辑功能
};

const deleteUser = (user: any) => {
  console.log('删除用户:', user);
  // TODO: 实现删除功能
};

const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  notification.message = message;
  notification.type = type;
  notification.show = true;
  setTimeout(() => {
    notification.show = false;
  }, 3000);
};

// 生命周期
onMounted(() => {
  console.log('组件已挂载，开始获取数据');
  fetchUsers();
});
</script>

<style scoped>
.users-view {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.header-left h2 {
  color: #333;
  margin-bottom: 8px;
}

.page-subtitle {
  color: #666;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 10px;
}

.debug-info {
  background: #f8f9fa;
  padding: 15px;
  margin: 15px 0;
  border-radius: 5px;
  border: 1px solid #dee2e6;
}

.debug-info details {
  margin-top: 10px;
}

.debug-info pre {
  background: white;
  padding: 10px;
  border-radius: 3px;
  max-height: 200px;
  overflow-y: auto;
  font-size: 12px;
}

.users-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  background: #f8f9fa;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #dee2e6;
}

.users-table td {
  padding: 15px 12px;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: middle;
}

.users-table tr:hover {
  background: #f8f9fa;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #6c757d;
}

.user-email {
  font-weight: 500;
  color: #333;
}

.user-id {
  font-size: 12px;
  color: #666;
}

.role-badge, .status-badge, .count-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.role-badge {
  background: #e3f2fd;
  color: #1976d2;
}

.status-badge.status-active {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.status-inactive {
  background: #fff3e0;
  color: #f57c00;
}

.status-badge.status-suspended {
  background: #ffebee;
  color: #d32f2f;
}

.count-badge {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.storage-info {
  min-width: 140px;
}

.storage-text {
  margin-bottom: 4px;
  font-size: 12px;
}

.storage-used {
  font-weight: 600;
  color: #333;
}

.storage-separator {
  color: #666;
  margin: 0 2px;
}

.storage-quota {
  color: #666;
}

.storage-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-fill.progress-normal {
  background: #28a745;
}

.progress-fill.progress-warning {
  background: #ffc107;
}

.progress-fill.progress-critical {
  background: #dc3545;
}

.progress-text {
  font-size: 10px;
  color: #666;
  min-width: 35px;
}

.last-login {
  font-size: 12px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-outline-primary {
  border: 1px solid #007bff;
  color: #007bff;
  background: transparent;
}

.btn-outline-primary:hover {
  background: #007bff;
  color: white;
}

.btn-outline-danger {
  border: 1px solid #dc3545;
  color: #dc3545;
  background: transparent;
}

.btn-outline-danger:hover {
  background: #dc3545;
  color: white;
}

.text-center {
  text-align: center;
}

.loading-spinner, .error-state, .no-data {
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.loading-spinner i, .error-state i, .no-data i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-weight: 500;
  z-index: 1001;
}

.notification.success {
  background: #28a745;
}

.notification.error {
  background: #dc3545;
}


</style>
