-- 性能优化索引
-- 为提升查询性能添加必要的数据库索引

-- 用户表索引
CREATE INDEX IF NOT EXISTS `idx_users_email` ON `users` (`email`);
CREATE INDEX IF NOT EXISTS `idx_users_username` ON `users` (`username`);
CREATE INDEX IF NOT EXISTS `idx_users_role_id` ON `users` (`role_id`);
CREATE INDEX IF NOT EXISTS `idx_users_status` ON `users` (`status`);
CREATE INDEX IF NOT EXISTS `idx_users_last_login` ON `users` (`last_login_at`);
CREATE INDEX IF NOT EXISTS `idx_users_storage_used` ON `users` (`storage_used`);
CREATE INDEX IF NOT EXISTS `idx_users_created_at` ON `users` (`created_at`);

-- 图片表索引
CREATE INDEX IF NOT EXISTS `idx_images_user_id` ON `images` (`user_id`);
CREATE INDEX IF NOT EXISTS `idx_images_album_id` ON `images` (`album_id`);
CREATE INDEX IF NOT EXISTS `idx_images_user_album` ON `images` (`user_id`, `album_id`);
CREATE INDEX IF NOT EXISTS `idx_images_name` ON `images` (`name`);
CREATE INDEX IF NOT EXISTS `idx_images_size` ON `images` (`size`);
CREATE INDEX IF NOT EXISTS `idx_images_created_at` ON `images` (`created_at`);
CREATE INDEX IF NOT EXISTS `idx_images_user_created` ON `images` (`user_id`, `created_at`);

-- 相册表索引
CREATE INDEX IF NOT EXISTS `idx_albums_user_id` ON `albums` (`user_id`);
CREATE INDEX IF NOT EXISTS `idx_albums_name` ON `albums` (`name`);
CREATE INDEX IF NOT EXISTS `idx_albums_user_name` ON `albums` (`user_id`, `name`);
CREATE INDEX IF NOT EXISTS `idx_albums_created_at` ON `albums` (`created_at`);
CREATE INDEX IF NOT EXISTS `idx_albums_user_created` ON `albums` (`user_id`, `created_at`);

-- 角色权限表索引
CREATE INDEX IF NOT EXISTS `idx_role_permissions_role_id` ON `role_permissions` (`role_id`);
CREATE INDEX IF NOT EXISTS `idx_role_permissions_permission_id` ON `role_permissions` (`permission_id`);

-- 用户权限表索引
CREATE INDEX IF NOT EXISTS `idx_user_permissions_user_id` ON `user_permissions` (`user_id`);
CREATE INDEX IF NOT EXISTS `idx_user_permissions_permission_id` ON `user_permissions` (`permission_id`);

-- 用户组表索引
CREATE INDEX IF NOT EXISTS `idx_user_groups_user_id` ON `user_groups` (`user_id`);
CREATE INDEX IF NOT EXISTS `idx_user_groups_group_id` ON `user_groups` (`group_id`);

-- 组权限表索引
CREATE INDEX IF NOT EXISTS `idx_group_permissions_group_id` ON `group_permissions` (`group_id`);
CREATE INDEX IF NOT EXISTS `idx_group_permissions_permission_id` ON `group_permissions` (`permission_id`);

-- 存储配置表索引
CREATE INDEX IF NOT EXISTS `idx_storage_configs_is_default` ON `storage_configs` (`is_default`);
CREATE INDEX IF NOT EXISTS `idx_storage_configs_is_active` ON `storage_configs` (`is_active`);

-- 复合索引用于常见查询模式
CREATE INDEX IF NOT EXISTS `idx_images_user_album_created` ON `images` (`user_id`, `album_id`, `created_at`);
CREATE INDEX IF NOT EXISTS `idx_albums_user_name_created` ON `albums` (`user_id`, `name`, `created_at`);
CREATE INDEX IF NOT EXISTS `idx_users_role_status` ON `users` (`role_id`, `status`);

-- 全文搜索索引（如果支持）
-- CREATE FULLTEXT INDEX `idx_images_name_fulltext` ON `images` (`name`);
-- CREATE FULLTEXT INDEX `idx_albums_name_desc_fulltext` ON `albums` (`name`, `description`);

-- 分析表以更新统计信息
ANALYZE TABLE `users`;
ANALYZE TABLE `images`;
ANALYZE TABLE `albums`;
ANALYZE TABLE `roles`;
ANALYZE TABLE `permissions`;
ANALYZE TABLE `role_permissions`;
ANALYZE TABLE `user_permissions`;
ANALYZE TABLE `groups`;
ANALYZE TABLE `user_groups`;
ANALYZE TABLE `group_permissions`;
ANALYZE TABLE `storage_configs`;
