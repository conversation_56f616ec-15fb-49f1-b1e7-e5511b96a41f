package models

import (
	"time"
)

// UserStatus 用户状态枚举
type UserStatus string

const (
	UserStatusActive    UserStatus = "active"
	UserStatusInactive  UserStatus = "inactive"
	UserStatusSuspended UserStatus = "suspended"
)

// User 表示用户模型
type User struct {
	ID              uint             `json:"id" gorm:"primary_key"`
	Username        string           `json:"username" gorm:"type:varchar(50);unique"`                                     // 用户名
	Email           string           `json:"email" gorm:"type:varchar(191);unique"`                                       // 邮箱作为登录凭证
	Password        string           `json:"-" gorm:"type:varchar(191);not null"`                                         // 密码不包含在JSON响应中
	RoleID          uint             `json:"role_id" gorm:"not null;default:3"`                                           // 角色ID，默认为普通用户
	Role            Role             `json:"role" gorm:"foreignKey:RoleID"`                                               // 关联的角色
	Status          UserStatus       `json:"status" gorm:"type:enum('active','inactive','suspended');default:'active'"`   // 用户状态
	LastLoginAt     *time.Time       `json:"last_login_at"`                                                               // 最后登录时间
	StorageQuota    int64            `json:"storage_quota" gorm:"default:1073741824"`                                     // 存储配额（字节），默认1GB
	StorageUsed     int64            `json:"storage_used" gorm:"default:0"`                                               // 已使用存储（字节）
	DefaultAlbumID  *uint            `json:"default_album_id"`                                                            // 默认相册ID（可选）
	DefaultAlbum    *Album           `json:"default_album" gorm:"foreignKey:DefaultAlbumID;constraint:OnDelete:SET NULL"` // 关联的默认相册
	Images          []Image          `json:"images" gorm:"foreignKey:UserID"`                                             // 用户的图片
	Albums          []Album          `json:"albums" gorm:"foreignKey:UserID"`                                             // 用户的相册
	UserPermissions []UserPermission `json:"user_permissions" gorm:"foreignKey:UserID"`                                   // 用户特殊权限
	Groups          []Group          `json:"groups" gorm:"many2many:user_groups"`                                         // 用户所属的组
	CreatedAt       time.Time        `json:"created_at"`
	UpdatedAt       time.Time        `json:"updated_at"`
}

// Token 表示JWT token结构
type Token struct {
	Token string `json:"token"`
}

// UserSettingsRequest 用户设置请求
type UserSettingsRequest struct {
	DefaultAlbumID *uint `json:"default_album_id"`
}

// HasPermission 检查用户是否拥有指定权限
func (u *User) HasPermission(permissionName string) bool {
	// 检查角色权限
	if u.Role.HasPermission(permissionName) {
		return true
	}

	// 检查用户特殊权限
	for _, userPerm := range u.UserPermissions {
		if userPerm.Permission.Name == permissionName {
			return userPerm.Granted
		}
	}

	return false
}

// IsActive 检查用户是否为活跃状态
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// IsSuperAdmin 检查用户是否为超级管理员
func (u *User) IsSuperAdmin() bool {
	return u.Role.Name == RoleSuperAdmin
}

// IsAdmin 检查用户是否为管理员（包括超级管理员）
func (u *User) IsAdmin() bool {
	return u.Role.Name == RoleSuperAdmin || u.Role.Name == RoleAdmin
}

// CanManageUser 检查是否可以管理指定用户
func (u *User) CanManageUser(targetUser *User) bool {
	// 超级管理员可以管理所有用户
	if u.IsSuperAdmin() {
		return true
	}

	// 管理员可以管理普通用户和访客，但不能管理其他管理员
	if u.IsAdmin() && !targetUser.IsAdmin() {
		return true
	}

	// 用户只能管理自己
	return u.ID == targetUser.ID
}

// GetEffectivePermissions 获取用户的有效权限列表
func (u *User) GetEffectivePermissions() []string {
	permissionMap := make(map[string]bool)

	// 添加角色权限
	for _, perm := range u.Role.Permissions {
		permissionMap[perm.Name] = true
	}

	// 处理用户特殊权限
	for _, userPerm := range u.UserPermissions {
		permissionMap[userPerm.Permission.Name] = userPerm.Granted
	}

	// 转换为权限列表
	var permissions []string
	for permName, granted := range permissionMap {
		if granted {
			permissions = append(permissions, permName)
		}
	}

	return permissions
}

// UserSettingsResponse 用户设置响应
type UserSettingsResponse struct {
	ID             uint   `json:"id"`
	Username       string `json:"username"`
	Email          string `json:"email"`
	DefaultAlbumID *uint  `json:"default_album_id"`
}

// UserResponse 用户响应结构（用于管理界面）
type UserResponse struct {
	ID          uint         `json:"id"`
	Username    string       `json:"username"`
	Email       string       `json:"email"`
	Role        RoleResponse `json:"role"`
	Status      UserStatus   `json:"status"`
	LastLoginAt *time.Time   `json:"last_login_at"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
}

// UserCreateRequest 创建用户请求
type UserCreateRequest struct {
	Username string     `json:"username" binding:"required,min=3,max=50"`
	Email    string     `json:"email" binding:"required,email"`
	Password string     `json:"password" binding:"required,min=6"`
	RoleID   uint       `json:"role_id" binding:"required"`
	Status   UserStatus `json:"status" binding:"required,oneof=active inactive suspended"`
}

// UserUpdateRequest 更新用户请求
type UserUpdateRequest struct {
	Username string     `json:"username" binding:"required,min=3,max=50"`
	Email    string     `json:"email" binding:"required,email"`
	RoleID   uint       `json:"role_id" binding:"required"`
	Status   UserStatus `json:"status" binding:"required,oneof=active inactive suspended"`
}

// UserPasswordUpdateRequest 更新用户密码请求
type UserPasswordUpdateRequest struct {
	Password string `json:"password" binding:"required,min=6"`
}

// UserListRequest 用户列表查询请求
type UserListRequest struct {
	Page     int        `json:"page" form:"page"`
	PageSize int        `json:"page_size" form:"page_size"`
	Username string     `json:"username" form:"username"`
	Email    string     `json:"email" form:"email"`
	RoleID   uint       `json:"role_id" form:"role_id"`
	Status   UserStatus `json:"status" form:"status"`
	SortBy   string     `json:"sort_by" form:"sort_by"`
	SortDesc bool       `json:"sort_desc" form:"sort_desc"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Users      []UserResponse `json:"users"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

// StorageQuotaResponse 存储配额响应
type StorageQuotaResponse struct {
	UserID       uint    `json:"user_id"`
	Username     string  `json:"username"`
	Email        string  `json:"email"`
	StorageQuota int64   `json:"storage_quota"` // 存储配额（字节）
	StorageUsed  int64   `json:"storage_used"`  // 已使用存储（字节）
	UsagePercent float64 `json:"usage_percent"` // 使用百分比
	QuotaGB      float64 `json:"quota_gb"`      // 配额（GB）
	UsedGB       float64 `json:"used_gb"`       // 已使用（GB）
	AvailableGB  float64 `json:"available_gb"`  // 可用（GB）
}

// StorageQuotaUpdateRequest 更新存储配额请求
type StorageQuotaUpdateRequest struct {
	StorageQuota int64 `json:"storage_quota" binding:"required,min=0"` // 存储配额（字节）
}

// UserWithStorageStats 用户及其存储统计信息
type UserWithStorageStats struct {
	User           User  `json:"user"`
	ImageCount     int64 `json:"image_count"`     // 图片数量
	AlbumCount     int64 `json:"album_count"`     // 相册数量
	RemainingQuota int64 `json:"remaining_quota"` // 剩余配额（字节）
}
