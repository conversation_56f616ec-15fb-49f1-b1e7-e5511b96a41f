package upload

import (
	"fmt"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"cloudbed/pkg/errors"
)

// FileValidator 文件验证器
type FileValidator struct {
	MaxSize           int64    // 最大文件大小（字节）
	AllowedTypes      []string // 允许的MIME类型
	AllowedExtensions []string // 允许的文件扩展名
}

// DefaultImageValidator 默认图片验证器
func DefaultImageValidator() *FileValidator {
	return &FileValidator{
		MaxSize: 10 * 1024 * 1024, // 10MB
		AllowedTypes: []string{
			"image/jpeg",
			"image/jpg",
			"image/png",
			"image/gif",
			"image/webp",
			"image/svg+xml",
		},
		AllowedExtensions: []string{
			".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg",
		},
	}
}

// ValidateFile 验证上传的文件
func (v *FileValidator) ValidateFile(fileHeader *multipart.FileHeader) error {
	if fileHeader == nil {
		return errors.New(errors.ErrCodeMissingParameter, "未选择文件")
	}

	// 检查文件大小
	if v.MaxSize > 0 && fileHeader.Size > v.MaxSize {
		return errors.New(errors.ErrCodeFileTooLarge,
			fmt.Sprintf("文件大小超过限制，最大允许 %s", formatFileSize(v.MaxSize)))
	}

	// 检查文件扩展名
	if len(v.AllowedExtensions) > 0 {
		ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
		if !contains(v.AllowedExtensions, ext) {
			return errors.New(errors.ErrCodeFileTypeNotAllowed,
				fmt.Sprintf("不支持的文件类型: %s", ext))
		}
	}

	// 检查MIME类型
	if len(v.AllowedTypes) > 0 {
		file, err := fileHeader.Open()
		if err != nil {
			return errors.NewWithError(errors.ErrCodeUploadFailed, "无法读取文件", err)
		}
		defer file.Close()

		// 读取文件头部来检测MIME类型
		buffer := make([]byte, 512)
		n, err := file.Read(buffer)
		if err != nil {
			return errors.NewWithError(errors.ErrCodeUploadFailed, "无法读取文件内容", err)
		}

		mimeType := http.DetectContentType(buffer[:n])
		if !contains(v.AllowedTypes, mimeType) {
			return errors.New(errors.ErrCodeFileTypeNotAllowed,
				fmt.Sprintf("不支持的文件类型: %s", mimeType))
		}
	}

	return nil
}

// ValidateImageFile 验证图片文件（使用默认验证器）
func ValidateImageFile(fileHeader *multipart.FileHeader) error {
	validator := DefaultImageValidator()
	return validator.ValidateFile(fileHeader)
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// formatFileSize 格式化文件大小
func formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// FileInfo 文件信息
type FileInfo struct {
	Name     string `json:"name"`
	Size     int64  `json:"size"`
	MimeType string `json:"mime_type"`
	Ext      string `json:"ext"`
}

// GetFileInfo 获取文件信息
func GetFileInfo(fileHeader *multipart.FileHeader) (*FileInfo, error) {
	if fileHeader == nil {
		return nil, errors.New(errors.ErrCodeMissingParameter, "未选择文件")
	}

	file, err := fileHeader.Open()
	if err != nil {
		return nil, errors.NewWithError(errors.ErrCodeUploadFailed, "无法读取文件", err)
	}
	defer file.Close()

	// 读取文件头部来检测MIME类型
	buffer := make([]byte, 512)
	n, err := file.Read(buffer)
	if err != nil {
		return nil, errors.NewWithError(errors.ErrCodeUploadFailed, "无法读取文件内容", err)
	}

	mimeType := http.DetectContentType(buffer[:n])
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))

	return &FileInfo{
		Name:     fileHeader.Filename,
		Size:     fileHeader.Size,
		MimeType: mimeType,
		Ext:      ext,
	}, nil
}

// SanitizeFilename 清理文件名，移除危险字符
func SanitizeFilename(filename string) string {
	// 移除路径分隔符和其他危险字符
	dangerous := []string{"/", "\\", "..", ":", "*", "?", "\"", "<", ">", "|"}
	sanitized := filename

	for _, char := range dangerous {
		sanitized = strings.ReplaceAll(sanitized, char, "_")
	}

	// 限制文件名长度
	if len(sanitized) > 255 {
		ext := filepath.Ext(sanitized)
		name := sanitized[:255-len(ext)]
		sanitized = name + ext
	}

	return sanitized
}

// GenerateUniqueFilename 生成唯一的文件名
func GenerateUniqueFilename(originalName string) string {
	ext := filepath.Ext(originalName)
	name := strings.TrimSuffix(originalName, ext)
	name = SanitizeFilename(name)

	// 使用时间戳和随机数生成唯一文件名
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	return fmt.Sprintf("%s_%s%s", name, timestamp, ext)
}
