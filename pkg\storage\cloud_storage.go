package storage

import (
	"context"
	"fmt"
	"io"
	"time"
)

// StorageProvider 存储提供商类型
type StorageProvider string

const (
	ProviderLocal StorageProvider = "local"
	ProviderS3    StorageProvider = "s3"
	ProviderOSS   StorageProvider = "oss"
	ProviderCOS   StorageProvider = "cos"
	ProviderGCS   StorageProvider = "gcs"
)

// StorageConfig 存储配置
type StorageConfig struct {
	Provider    StorageProvider `json:"provider"`
	Region      string          `json:"region"`
	Bucket      string          `json:"bucket"`
	AccessKey   string          `json:"access_key"`
	SecretKey   string          `json:"secret_key"`
	Endpoint    string          `json:"endpoint"`
	UseSSL      bool            `json:"use_ssl"`
	CDNDomain   string          `json:"cdn_domain"`
	LocalPath   string          `json:"local_path"`
}

// FileInfo 文件信息
type FileInfo struct {
	Key          string            `json:"key"`
	Size         int64             `json:"size"`
	ContentType  string            `json:"content_type"`
	ETag         string            `json:"etag"`
	LastModified time.Time         `json:"last_modified"`
	Metadata     map[string]string `json:"metadata"`
	URL          string            `json:"url"`
	CDNUrl       string            `json:"cdn_url"`
}

// UploadOptions 上传选项
type UploadOptions struct {
	ContentType string            `json:"content_type"`
	Metadata    map[string]string `json:"metadata"`
	ACL         string            `json:"acl"`
	StorageClass string           `json:"storage_class"`
	Expires     *time.Time        `json:"expires"`
}

// DownloadOptions 下载选项
type DownloadOptions struct {
	Range      string        `json:"range"`
	IfMatch    string        `json:"if_match"`
	IfNoneMatch string       `json:"if_none_match"`
	Expires    time.Duration `json:"expires"`
}

// CloudStorage 云存储接口
type CloudStorage interface {
	// 文件操作
	Upload(ctx context.Context, key string, reader io.Reader, options *UploadOptions) (*FileInfo, error)
	Download(ctx context.Context, key string, writer io.Writer, options *DownloadOptions) error
	Delete(ctx context.Context, key string) error
	Copy(ctx context.Context, srcKey, dstKey string) error
	Move(ctx context.Context, srcKey, dstKey string) error
	
	// 文件信息
	GetFileInfo(ctx context.Context, key string) (*FileInfo, error)
	Exists(ctx context.Context, key string) (bool, error)
	
	// 目录操作
	ListFiles(ctx context.Context, prefix string, limit int) ([]*FileInfo, error)
	DeleteDirectory(ctx context.Context, prefix string) error
	
	// URL生成
	GetPublicURL(key string) string
	GetSignedURL(ctx context.Context, key string, expires time.Duration) (string, error)
	GetUploadURL(ctx context.Context, key string, expires time.Duration) (string, error)
	
	// 批量操作
	BatchUpload(ctx context.Context, files map[string]io.Reader, options *UploadOptions) (map[string]*FileInfo, error)
	BatchDelete(ctx context.Context, keys []string) error
	
	// 存储统计
	GetStorageUsage(ctx context.Context) (*StorageUsage, error)
}

// StorageUsage 存储使用情况
type StorageUsage struct {
	TotalSize     int64 `json:"total_size"`
	FileCount     int64 `json:"file_count"`
	LastUpdated   time.Time `json:"last_updated"`
	StorageClass  map[string]int64 `json:"storage_class"`
}

// StorageManager 存储管理器
type StorageManager struct {
	primary   CloudStorage
	backup    CloudStorage
	config    StorageConfig
	metrics   *StorageMetrics
}

// StorageMetrics 存储指标
type StorageMetrics struct {
	UploadCount    int64         `json:"upload_count"`
	DownloadCount  int64         `json:"download_count"`
	ErrorCount     int64         `json:"error_count"`
	TotalSize      int64         `json:"total_size"`
	AvgUploadTime  time.Duration `json:"avg_upload_time"`
	AvgDownloadTime time.Duration `json:"avg_download_time"`
}

// NewStorageManager 创建存储管理器
func NewStorageManager(config StorageConfig) (*StorageManager, error) {
	primary, err := createStorage(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create primary storage: %v", err)
	}

	sm := &StorageManager{
		primary: primary,
		config:  config,
		metrics: &StorageMetrics{},
	}

	return sm, nil
}

// SetBackupStorage 设置备份存储
func (sm *StorageManager) SetBackupStorage(config StorageConfig) error {
	backup, err := createStorage(config)
	if err != nil {
		return fmt.Errorf("failed to create backup storage: %v", err)
	}
	sm.backup = backup
	return nil
}

// Upload 上传文件
func (sm *StorageManager) Upload(ctx context.Context, key string, reader io.Reader, options *UploadOptions) (*FileInfo, error) {
	start := time.Now()
	defer func() {
		sm.metrics.AvgUploadTime = time.Since(start)
		sm.metrics.UploadCount++
	}()

	// 上传到主存储
	fileInfo, err := sm.primary.Upload(ctx, key, reader, options)
	if err != nil {
		sm.metrics.ErrorCount++
		return nil, fmt.Errorf("primary storage upload failed: %v", err)
	}

	// 异步备份到备份存储
	if sm.backup != nil {
		go sm.backupFile(key, fileInfo)
	}

	sm.metrics.TotalSize += fileInfo.Size
	return fileInfo, nil
}

// Download 下载文件
func (sm *StorageManager) Download(ctx context.Context, key string, writer io.Writer, options *DownloadOptions) error {
	start := time.Now()
	defer func() {
		sm.metrics.AvgDownloadTime = time.Since(start)
		sm.metrics.DownloadCount++
	}()

	// 尝试从主存储下载
	err := sm.primary.Download(ctx, key, writer, options)
	if err == nil {
		return nil
	}

	// 如果主存储失败，尝试从备份存储下载
	if sm.backup != nil {
		err = sm.backup.Download(ctx, key, writer, options)
		if err == nil {
			// 异步恢复到主存储
			go sm.restoreFile(key)
			return nil
		}
	}

	sm.metrics.ErrorCount++
	return fmt.Errorf("download failed from all storages: %v", err)
}

// Delete 删除文件
func (sm *StorageManager) Delete(ctx context.Context, key string) error {
	// 从主存储删除
	err := sm.primary.Delete(ctx, key)
	if err != nil {
		sm.metrics.ErrorCount++
	}

	// 从备份存储删除
	if sm.backup != nil {
		sm.backup.Delete(ctx, key)
	}

	return err
}

// GetFileInfo 获取文件信息
func (sm *StorageManager) GetFileInfo(ctx context.Context, key string) (*FileInfo, error) {
	return sm.primary.GetFileInfo(ctx, key)
}

// Exists 检查文件是否存在
func (sm *StorageManager) Exists(ctx context.Context, key string) (bool, error) {
	return sm.primary.Exists(ctx, key)
}

// ListFiles 列出文件
func (sm *StorageManager) ListFiles(ctx context.Context, prefix string, limit int) ([]*FileInfo, error) {
	return sm.primary.ListFiles(ctx, prefix, limit)
}

// GetPublicURL 获取公共URL
func (sm *StorageManager) GetPublicURL(key string) string {
	return sm.primary.GetPublicURL(key)
}

// GetSignedURL 获取签名URL
func (sm *StorageManager) GetSignedURL(ctx context.Context, key string, expires time.Duration) (string, error) {
	return sm.primary.GetSignedURL(ctx, key, expires)
}

// BatchUpload 批量上传
func (sm *StorageManager) BatchUpload(ctx context.Context, files map[string]io.Reader, options *UploadOptions) (map[string]*FileInfo, error) {
	results := make(map[string]*FileInfo)
	errors := make(map[string]error)

	// 并发上传
	type uploadResult struct {
		key      string
		fileInfo *FileInfo
		err      error
	}

	resultChan := make(chan uploadResult, len(files))

	for key, reader := range files {
		go func(k string, r io.Reader) {
			fileInfo, err := sm.Upload(ctx, k, r, options)
			resultChan <- uploadResult{k, fileInfo, err}
		}(key, reader)
	}

	// 收集结果
	for i := 0; i < len(files); i++ {
		result := <-resultChan
		if result.err != nil {
			errors[result.key] = result.err
		} else {
			results[result.key] = result.fileInfo
		}
	}

	if len(errors) > 0 {
		return results, fmt.Errorf("batch upload completed with %d errors", len(errors))
	}

	return results, nil
}

// GetMetrics 获取存储指标
func (sm *StorageManager) GetMetrics() *StorageMetrics {
	return sm.metrics
}

// GetStorageUsage 获取存储使用情况
func (sm *StorageManager) GetStorageUsage(ctx context.Context) (*StorageUsage, error) {
	return sm.primary.GetStorageUsage(ctx)
}

// 私有方法
func (sm *StorageManager) backupFile(key string, fileInfo *FileInfo) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 从主存储下载
	var buffer []byte
	err := sm.primary.Download(ctx, key, &writeBuffer{&buffer}, nil)
	if err != nil {
		return
	}

	// 上传到备份存储
	sm.backup.Upload(ctx, key, &readBuffer{buffer}, &UploadOptions{
		ContentType: fileInfo.ContentType,
		Metadata:    fileInfo.Metadata,
	})
}

func (sm *StorageManager) restoreFile(key string) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 从备份存储下载
	var buffer []byte
	err := sm.backup.Download(ctx, key, &writeBuffer{&buffer}, nil)
	if err != nil {
		return
	}

	// 上传到主存储
	fileInfo, _ := sm.backup.GetFileInfo(ctx, key)
	options := &UploadOptions{}
	if fileInfo != nil {
		options.ContentType = fileInfo.ContentType
		options.Metadata = fileInfo.Metadata
	}

	sm.primary.Upload(ctx, key, &readBuffer{buffer}, options)
}

// 辅助类型
type writeBuffer struct {
	buffer *[]byte
}

func (wb *writeBuffer) Write(p []byte) (n int, err error) {
	*wb.buffer = append(*wb.buffer, p...)
	return len(p), nil
}

type readBuffer struct {
	buffer []byte
	pos    int
}

func (rb *readBuffer) Read(p []byte) (n int, err error) {
	if rb.pos >= len(rb.buffer) {
		return 0, io.EOF
	}
	n = copy(p, rb.buffer[rb.pos:])
	rb.pos += n
	return n, nil
}

// createStorage 创建存储实例
func createStorage(config StorageConfig) (CloudStorage, error) {
	switch config.Provider {
	case ProviderLocal:
		return NewLocalStorage(config)
	case ProviderS3:
		return NewS3Storage(config)
	case ProviderOSS:
		return NewOSSStorage(config)
	case ProviderCOS:
		return NewCOSStorage(config)
	case ProviderGCS:
		return NewGCSStorage(config)
	default:
		return nil, fmt.Errorf("unsupported storage provider: %s", config.Provider)
	}
}

// 存储提供商实现（占位符）
func NewLocalStorage(config StorageConfig) (CloudStorage, error) {
	// TODO: 实现本地存储
	return nil, fmt.Errorf("local storage not implemented")
}

func NewS3Storage(config StorageConfig) (CloudStorage, error) {
	// TODO: 实现AWS S3存储
	return nil, fmt.Errorf("S3 storage not implemented")
}

func NewOSSStorage(config StorageConfig) (CloudStorage, error) {
	// TODO: 实现阿里云OSS存储
	return nil, fmt.Errorf("OSS storage not implemented")
}

func NewCOSStorage(config StorageConfig) (CloudStorage, error) {
	// TODO: 实现腾讯云COS存储
	return nil, fmt.Errorf("COS storage not implemented")
}

func NewGCSStorage(config StorageConfig) (CloudStorage, error) {
	// TODO: 实现Google Cloud Storage
	return nil, fmt.Errorf("GCS storage not implemented")
}
