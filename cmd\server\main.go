package main

import (
	"log"
	"net/http"
	"os"
	"strings"

	"cloudbed/internal/api/handlers"
	"cloudbed/internal/api/middleware"
	"cloudbed/internal/config"
	"cloudbed/internal/database"
	"cloudbed/pkg/logger"
	"cloudbed/pkg/response"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Initialize logger
	logger.InitLogger(cfg.Log.Level, cfg.Log.File)

	// Initialize database
	if err := database.InitDatabase(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer database.CloseDB()

	// Setup Gin mode
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// Create router
	router := gin.Default()

	// Setup CORS
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "http://localhost:8080", "http://localhost:5173"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		AllowCredentials: true,
	}))

	// Setup middleware
	router.Use(middleware.RequestLogger())
	router.Use(middleware.ErrorHandler())

	// Setup routes
	setupRoutes(router, cfg)

	// Start server
	port := cfg.Server.Port
	if port == "" {
		port = "8080"
	}

	log.Printf("Server starting on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

func setupRoutes(router *gin.Engine, cfg *config.Config) {
	// Static file middleware
	staticFileMiddleware := func() gin.HandlerFunc {
		return func(c *gin.Context) {
			path := c.Request.URL.Path

			// Set appropriate headers for different file types
			if strings.HasSuffix(path, ".js") {
				c.Header("Content-Type", "application/javascript; charset=utf-8")
				c.Header("Cache-Control", "public, max-age=31536000")
			} else if strings.HasSuffix(path, ".css") {
				c.Header("Content-Type", "text/css; charset=utf-8")
				c.Header("Cache-Control", "public, max-age=31536000")
			} else if strings.HasSuffix(path, ".woff") || strings.HasSuffix(path, ".woff2") {
				c.Header("Content-Type", "font/woff")
				c.Header("Cache-Control", "public, max-age=31536000")
			} else if strings.HasSuffix(path, ".svg") {
				c.Header("Content-Type", "image/svg+xml")
				c.Header("Cache-Control", "public, max-age=86400")
			} else if strings.HasSuffix(path, ".png") || strings.HasSuffix(path, ".jpg") || strings.HasSuffix(path, ".jpeg") {
				c.Header("Cache-Control", "public, max-age=86400")
			}

			c.Next()
		}
	}

	// Static file serving
	router.Static("/storage/uploads", "./storage/uploads")

	// Frontend assets with middleware
	assetsGroup := router.Group("/assets")
	assetsGroup.Use(staticFileMiddleware())
	assetsGroup.StaticFS("/", http.Dir("./web/dist/assets"))

	// Frontend static files
	router.StaticFS("/static", http.Dir("./web/dist"))

	// Frontend routes
	router.GET("/", serveIndexHTML)
	router.GET("/favicon.ico", func(c *gin.Context) {
		c.File("./web/dist/vite.svg")
	})
	router.GET("/vite.svg", func(c *gin.Context) {
		c.File("./web/dist/vite.svg")
	})
	router.GET("/manifest.json", func(c *gin.Context) {
		c.Header("Content-Type", "application/json")
		c.File("./web/dist/manifest.json")
	})
	router.GET("/sw.js", func(c *gin.Context) {
		c.Header("Content-Type", "application/javascript")
		c.File("./web/dist/sw.js")
	})

	// API routes
	api := router.Group("/api")
	{
		// Auth routes
		api.POST("/register", handlers.Register)
		api.POST("/login", handlers.Login)
		api.POST("/logout", middleware.AuthRequired(), handlers.Logout)
		api.GET("/profile", middleware.AuthRequired(), handlers.GetProfile)
		api.PUT("/profile", middleware.AuthRequired(), handlers.UpdateProfile)

		// Image routes
		api.POST("/upload", middleware.AuthRequired(), handlers.UploadImage)
		api.GET("/images", middleware.AuthRequired(), handlers.GetImages)
		api.GET("/images/:id", middleware.AuthRequired(), handlers.GetImage)
		api.PUT("/images/:id", middleware.AuthRequired(), handlers.UpdateImage)
		api.DELETE("/images/:id", middleware.AuthRequired(), handlers.DeleteImage)

		// Album routes
		api.POST("/albums", middleware.AuthRequired(), handlers.CreateAlbum)
		api.GET("/albums", middleware.AuthRequired(), handlers.GetAlbums)
		api.GET("/albums/:id", middleware.AuthRequired(), handlers.GetAlbum)
		api.PUT("/albums/:id", middleware.AuthRequired(), handlers.UpdateAlbum)
		api.DELETE("/albums/:id", middleware.AuthRequired(), handlers.DeleteAlbum)

		// Admin routes
		admin := api.Group("/admin")
		admin.Use(middleware.AuthRequired(), middleware.AdminRequired())
		{
			admin.GET("/users", handlers.GetUsers)
			admin.GET("/users/:id", handlers.GetUser)
			admin.PUT("/users/:id", handlers.UpdateUser)
			admin.DELETE("/users/:id", handlers.DeleteUser)
			admin.GET("/images", handlers.GetAllImages)
			admin.GET("/storage", handlers.GetStorageInfo)
		}

		// Health check
		api.GET("/health", func(c *gin.Context) {
			response.Success(c, gin.H{
				"status":  "ok",
				"service": "cloudbed",
			})
		})
	}

	// SPA fallback
	router.NoRoute(func(c *gin.Context) {
		// If it's an API request, return 404
		if strings.HasPrefix(c.Request.URL.Path, "/api/") {
			response.Error(c, http.StatusNotFound, "API endpoint not found")
			return
		}
		// If it's a static resource request, return 404
		if strings.HasPrefix(c.Request.URL.Path, "/assets/") ||
			strings.HasPrefix(c.Request.URL.Path, "/storage/") ||
			strings.HasPrefix(c.Request.URL.Path, "/static/") {
			c.Status(http.StatusNotFound)
			return
		}
		// All other routes return index.html for Vue Router
		serveIndexHTML(c)
	})
}

// serveIndexHTML serves the frontend index.html file
func serveIndexHTML(c *gin.Context) {
	// Set appropriate headers
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	// Check if file exists
	if _, err := os.Stat("./web/dist/index.html"); os.IsNotExist(err) {
		logger.Error("Frontend index.html not found")
		c.String(http.StatusNotFound, "Frontend files not found. Please build the frontend first.")
		return
	}

	c.File("./web/dist/index.html")
}
