<template>
  <div class="search-container">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2><i class="bi bi-search"></i> 搜索</h2>
          <p class="page-subtitle">查找您的图片和相册</p>
        </div>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="search-section">
      <SearchBox
        :placeholder="searchPlaceholder"
        :initial-query="searchQuery"
        :search-type="searchType"
        @search="handleSearch"
        @clear="handleClearSearch"
      />
    </div>

    <!-- 搜索类型切换 -->
    <div class="search-type-tabs">
      <button
        class="tab-btn"
        :class="{ active: searchType === 'images' }"
        @click="switchSearchType('images')"
      >
        <i class="bi bi-images"></i>
        图片
        <span v-if="searchResults.images" class="result-count">{{ searchResults.images.total }}</span>
      </button>
      <button
        class="tab-btn"
        :class="{ active: searchType === 'albums' }"
        @click="switchSearchType('albums')"
      >
        <i class="bi bi-folder"></i>
        相册
        <span v-if="searchResults.albums" class="result-count">{{ searchResults.albums.total }}</span>
      </button>
    </div>

    <!-- 搜索结果 -->
    <div class="search-results">
      <!-- 图片搜索结果 -->
      <div v-if="searchType === 'images'" class="images-results">
        <div v-if="searchResults.images && searchResults.images.total > 0" class="results-header">
          <h3>找到 {{ searchResults.images.total }} 张图片</h3>
          <div class="results-info">
            <span>第 {{ searchResults.images.page }} 页，共 {{ searchResults.images.total_pages }} 页</span>
          </div>
        </div>

        <ImageGrid
          :images="searchResults.images?.items || []"
          :loading="loading"
          :has-more="searchResults.images?.has_next || false"
          :search-query="searchQuery"
          :highlight-matches="true"
          :empty-message="getEmptyMessage()"
          :show-upload-button="false"
          @image-click="viewImage"
          @image-view="viewImage"
          @image-download="downloadImage"
          @image-delete="deleteImage"
          @load-more="loadMoreImages"
        />
      </div>

      <!-- 相册搜索结果 -->
      <div v-if="searchType === 'albums'" class="albums-results">
        <div v-if="searchResults.albums && searchResults.albums.total > 0" class="results-header">
          <h3>找到 {{ searchResults.albums.total }} 个相册</h3>
          <div class="results-info">
            <span>第 {{ searchResults.albums.page }} 页，共 {{ searchResults.albums.total_pages }} 页</span>
          </div>
        </div>

        <div v-if="loading" class="loading-state">
          <i class="bi bi-arrow-repeat spin"></i>
          <p>搜索中...</p>
        </div>

        <div v-else-if="searchResults.albums?.items?.length > 0" class="albums-grid">
          <div
            v-for="album in searchResults.albums.items"
            :key="album.id"
            class="album-card"
            @click="viewAlbum(album)"
          >
            <div class="album-cover">
              <i class="bi bi-folder"></i>
            </div>
            <div class="album-info">
              <h4 class="album-name">
                <span v-if="searchQuery" v-html="highlightText(album.name)"></span>
                <span v-else>{{ album.name }}</span>
              </h4>
              <p class="album-description" v-if="album.description">
                <span v-if="searchQuery" v-html="highlightText(album.description)"></span>
                <span v-else>{{ album.description }}</span>
              </p>
              <div class="album-meta">
                <span class="image-count">{{ album.image_count || 0 }} 张图片</span>
                <span class="created-date">{{ formatDate(album.created_at) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="!loading" class="empty-state">
          <i class="bi bi-folder"></i>
          <h3>{{ getEmptyMessage() }}</h3>
        </div>

        <!-- 分页 -->
        <div v-if="searchResults.albums && searchResults.albums.total_pages > 1" class="pagination">
          <button
            class="page-btn"
            :disabled="!searchResults.albums.has_prev"
            @click="changePage(searchResults.albums.page - 1)"
          >
            <i class="bi bi-chevron-left"></i>
            上一页
          </button>
          <span class="page-info">
            第 {{ searchResults.albums.page }} 页 / 共 {{ searchResults.albums.total_pages }} 页
          </span>
          <button
            class="page-btn"
            :disabled="!searchResults.albums.has_next"
            @click="changePage(searchResults.albums.page + 1)"
          >
            下一页
            <i class="bi bi-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索建议 -->
    <div v-if="searchResults.suggestions?.length > 0" class="search-suggestions">
      <h4>相关搜索</h4>
      <div class="suggestions-list">
        <button
          v-for="suggestion in searchResults.suggestions"
          :key="suggestion"
          class="suggestion-btn"
          @click="searchSuggestion(suggestion)"
        >
          {{ suggestion }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import SearchBox from '../components/SearchBox.vue'
import ImageGrid from '../components/ImageGrid.vue'

// 路由
const route = useRoute()
const router = useRouter()

// 搜索状态
const searchType = ref<'images' | 'albums'>('images')
const searchQuery = ref('')
const searchFilters = ref<any>({})
const loading = ref(false)

// 搜索结果
const searchResults = ref<{
  images?: any
  albums?: any
  suggestions?: string[]
}>({})

// 计算属性
const searchPlaceholder = computed(() => {
  return searchType.value === 'images' ? '搜索图片...' : '搜索相册...'
})

// 方法
const handleSearch = async (query: string, filters: any) => {
  searchQuery.value = query
  searchFilters.value = filters
  
  // 更新URL
  const queryParams: any = { q: query, type: searchType.value }
  if (Object.keys(filters).length > 0) {
    queryParams.filters = JSON.stringify(filters)
  }
  
  router.push({ query: queryParams })
  
  await performSearch()
}

const handleClearSearch = () => {
  searchQuery.value = ''
  searchFilters.value = {}
  searchResults.value = {}
  router.push({ query: {} })
}

const switchSearchType = async (type: 'images' | 'albums') => {
  searchType.value = type
  
  if (searchQuery.value) {
    const queryParams: any = { q: searchQuery.value, type }
    if (Object.keys(searchFilters.value).length > 0) {
      queryParams.filters = JSON.stringify(searchFilters.value)
    }
    router.push({ query: queryParams })
    
    await performSearch()
  }
}

const performSearch = async () => {
  if (!searchQuery.value.trim()) return
  
  loading.value = true
  
  try {
    if (searchType.value === 'images') {
      await searchImages()
    } else {
      await searchAlbums()
    }
  } catch (error) {
    console.error('Search failed:', error)
  } finally {
    loading.value = false
  }
}

const searchImages = async () => {
  try {
    // TODO: 调用搜索图片API
    // const response = await api.searchImages({
    //   query: searchQuery.value,
    //   ...searchFilters.value
    // })
    // searchResults.value.images = response.data
    
    // 模拟数据
    searchResults.value.images = {
      items: [],
      total: 0,
      page: 1,
      page_size: 20,
      total_pages: 0,
      has_next: false,
      has_prev: false
    }
  } catch (error) {
    console.error('Failed to search images:', error)
  }
}

const searchAlbums = async () => {
  try {
    // TODO: 调用搜索相册API
    // const response = await api.searchAlbums({
    //   query: searchQuery.value,
    //   ...searchFilters.value
    // })
    // searchResults.value.albums = response.data
    
    // 模拟数据
    searchResults.value.albums = {
      items: [],
      total: 0,
      page: 1,
      page_size: 20,
      total_pages: 0,
      has_next: false,
      has_prev: false
    }
  } catch (error) {
    console.error('Failed to search albums:', error)
  }
}

const loadMoreImages = async () => {
  if (!searchResults.value.images?.has_next || loading.value) return
  
  // TODO: 加载更多图片
}

const changePage = async (page: number) => {
  if (page < 1) return
  
  // TODO: 切换页面
}

const searchSuggestion = (suggestion: string) => {
  handleSearch(suggestion, {})
}

const viewImage = (image: any) => {
  // TODO: 查看图片详情
  console.log('View image:', image)
}

const downloadImage = (image: any) => {
  // TODO: 下载图片
  console.log('Download image:', image)
}

const deleteImage = (image: any) => {
  // TODO: 删除图片
  console.log('Delete image:', image)
}

const viewAlbum = (album: any) => {
  router.push(`/albums/${album.id}`)
}

const getEmptyMessage = () => {
  if (!searchQuery.value) {
    return searchType.value === 'images' ? '输入关键词搜索图片' : '输入关键词搜索相册'
  }
  return `没有找到包含 "${searchQuery.value}" 的${searchType.value === 'images' ? '图片' : '相册'}`
}

const highlightText = (text: string): string => {
  if (!searchQuery.value || !text) return text
  
  const regex = new RegExp(`(${escapeRegExp(searchQuery.value)})`, 'gi')
  return text.replace(regex, '<mark class="search-highlight">$1</mark>')
}

const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 从URL参数初始化搜索
  const query = route.query.q as string
  const type = route.query.type as string
  const filters = route.query.filters as string
  
  if (query) {
    searchQuery.value = query
  }
  
  if (type === 'albums') {
    searchType.value = 'albums'
  }
  
  if (filters) {
    try {
      searchFilters.value = JSON.parse(filters)
    } catch (error) {
      console.error('Failed to parse filters:', error)
    }
  }
  
  if (searchQuery.value) {
    performSearch()
  }
})

// 监听路由变化
watch(() => route.query, (newQuery) => {
  const query = newQuery.q as string
  const type = newQuery.type as string
  
  if (query !== searchQuery.value) {
    searchQuery.value = query || ''
  }
  
  if (type && type !== searchType.value) {
    searchType.value = type as 'images' | 'albums'
  }
})
</script>

<style scoped>
.search-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-subtitle {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 0.9rem;
}

.search-section {
  margin-bottom: 30px;
}

.search-type-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e9ecef;
}

.tab-btn {
  padding: 12px 20px;
  border: none;
  background: none;
  color: #6c757d;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
}

.tab-btn:hover {
  color: #007bff;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.result-count {
  background: #007bff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  min-width: 20px;
  text-align: center;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.results-header h3 {
  margin: 0;
  color: #333;
}

.results-info {
  color: #6c757d;
  font-size: 0.9rem;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6c757d;
}

.loading-state i {
  font-size: 2rem;
  margin-bottom: 10px;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.albums-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.album-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.album-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.album-cover {
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
}

.album-info {
  padding: 16px;
}

.album-name {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.album-description {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.album-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6c757d;
  text-align: center;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0;
  color: #495057;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
  padding: 20px;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: white;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.page-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #007bff;
  color: #007bff;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #6c757d;
  font-size: 0.9rem;
}

.search-suggestions {
  margin-top: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.search-suggestions h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1rem;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-btn {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  background: white;
  color: #6c757d;
  border-radius: 16px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.suggestion-btn:hover {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* 搜索高亮样式 */
:deep(.search-highlight) {
  background: linear-gradient(120deg, #ffd54f 0%, #ffeb3b 100%);
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: 600;
  color: #333;
  box-shadow: 0 1px 3px rgba(255, 193, 7, 0.3);
}
</style>
