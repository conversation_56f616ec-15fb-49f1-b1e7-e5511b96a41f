package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 统一响应格式
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ErrorResponse 错误响应格式
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Error   string `json:"error,omitempty"`
}

// 预定义的错误码
const (
	// 成功
	CodeSuccess = 200

	// 客户端错误 4xx
	CodeBadRequest          = 400
	CodeUnauthorized        = 401
	CodeForbidden           = 403
	CodeNotFound            = 404
	CodeMethodNotAllowed    = 405
	CodeConflict            = 409
	CodeUnprocessableEntity = 422
	CodeTooManyRequests     = 429

	// 服务器错误 5xx
	CodeInternalServerError = 500
	CodeBadGateway          = 502
	CodeServiceUnavailable  = 503
	CodeGatewayTimeout      = 504
)

// 预定义的错误消息
const (
	MsgSuccess             = "操作成功"
	MsgBadRequest          = "请求参数错误"
	MsgUnauthorized        = "未授权访问"
	MsgForbidden           = "权限不足"
	MsgNotFound            = "资源不存在"
	MsgMethodNotAllowed    = "请求方法不允许"
	MsgConflict            = "资源冲突"
	MsgUnprocessableEntity = "请求数据格式错误"
	MsgTooManyRequests     = "请求过于频繁"
	MsgInternalServerError = "服务器内部错误"
	MsgBadGateway          = "网关错误"
	MsgServiceUnavailable  = "服务不可用"
	MsgGatewayTimeout      = "网关超时"
)

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: MsgSuccess,
		Data:    data,
	})
}

// SuccessWithMessage 带自定义消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: message,
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, httpCode int, code int, message string) {
	c.JSON(httpCode, ErrorResponse{
		Code:    code,
		Message: message,
	})
}

// ErrorWithDetail 带详细错误信息的错误响应
func ErrorWithDetail(c *gin.Context, httpCode int, code int, message string, error string) {
	c.JSON(httpCode, ErrorResponse{
		Code:    code,
		Message: message,
		Error:   error,
	})
}

// BadRequest 400错误
func BadRequest(c *gin.Context, message string) {
	Error(c, http.StatusBadRequest, CodeBadRequest, message)
}

// BadRequestWithDetail 400错误带详情
func BadRequestWithDetail(c *gin.Context, message string, error string) {
	ErrorWithDetail(c, http.StatusBadRequest, CodeBadRequest, message, error)
}

// Unauthorized 401错误
func Unauthorized(c *gin.Context, message string) {
	Error(c, http.StatusUnauthorized, CodeUnauthorized, message)
}

// Forbidden 403错误
func Forbidden(c *gin.Context, message string) {
	Error(c, http.StatusForbidden, CodeForbidden, message)
}

// NotFound 404错误
func NotFound(c *gin.Context, message string) {
	Error(c, http.StatusNotFound, CodeNotFound, message)
}

// Conflict 409错误
func Conflict(c *gin.Context, message string) {
	Error(c, http.StatusConflict, CodeConflict, message)
}

// UnprocessableEntity 422错误
func UnprocessableEntity(c *gin.Context, message string) {
	Error(c, http.StatusUnprocessableEntity, CodeUnprocessableEntity, message)
}

// InternalServerError 500错误
func InternalServerError(c *gin.Context, message string) {
	Error(c, http.StatusInternalServerError, CodeInternalServerError, message)
}

// InternalServerErrorWithDetail 500错误带详情
func InternalServerErrorWithDetail(c *gin.Context, message string, error string) {
	ErrorWithDetail(c, http.StatusInternalServerError, CodeInternalServerError, message, error)
}
