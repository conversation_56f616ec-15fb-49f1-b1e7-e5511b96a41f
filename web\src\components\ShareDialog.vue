<template>
  <div class="share-dialog">
    <div class="dialog-header">
      <h3>分享设置</h3>
      <button class="close-btn" @click="$emit('close')">
        <i class="bi bi-x"></i>
      </button>
    </div>

    <div class="dialog-content">
      <!-- 基本信息 -->
      <div class="section">
        <h4>基本信息</h4>
        <div class="form-group">
          <label>分享标题</label>
          <input
            v-model="shareForm.title"
            type="text"
            class="form-control"
            placeholder="输入分享标题（可选）"
          />
        </div>
        <div class="form-group">
          <label>分享描述</label>
          <textarea
            v-model="shareForm.description"
            class="form-control"
            rows="3"
            placeholder="输入分享描述（可选）"
          ></textarea>
        </div>
      </div>

      <!-- 访问控制 -->
      <div class="section">
        <h4>访问控制</h4>
        <div class="form-group">
          <label>
            <input
              type="checkbox"
              v-model="shareForm.hasPassword"
              @change="onPasswordToggle"
            />
            设置访问密码
          </label>
          <input
            v-if="shareForm.hasPassword"
            v-model="shareForm.password"
            type="password"
            class="form-control mt-2"
            placeholder="输入访问密码"
          />
        </div>
        <div class="form-group">
          <label>
            <input type="checkbox" v-model="shareForm.allowGuest" />
            允许游客访问
          </label>
        </div>
        <div class="form-group">
          <label>
            <input type="checkbox" v-model="shareForm.isPublic" />
            公开分享（搜索引擎可索引）
          </label>
        </div>
      </div>

      <!-- 权限设置 -->
      <div class="section">
        <h4>权限设置</h4>
        <div class="permissions-grid">
          <label class="permission-item">
            <input
              type="checkbox"
              value="view"
              v-model="shareForm.permissions"
            />
            <div class="permission-info">
              <i class="bi bi-eye"></i>
              <span>查看</span>
            </div>
          </label>
          <label class="permission-item">
            <input
              type="checkbox"
              value="comment"
              v-model="shareForm.permissions"
            />
            <div class="permission-info">
              <i class="bi bi-chat"></i>
              <span>评论</span>
            </div>
          </label>
          <label class="permission-item">
            <input
              type="checkbox"
              value="download"
              v-model="shareForm.permissions"
            />
            <div class="permission-info">
              <i class="bi bi-download"></i>
              <span>下载</span>
            </div>
          </label>
          <label class="permission-item">
            <input
              type="checkbox"
              value="edit"
              v-model="shareForm.permissions"
            />
            <div class="permission-info">
              <i class="bi bi-pencil"></i>
              <span>编辑</span>
            </div>
          </label>
        </div>
      </div>

      <!-- 时效设置 -->
      <div class="section">
        <h4>时效设置</h4>
        <div class="form-group">
          <label>过期时间</label>
          <select v-model="shareForm.expiresIn" class="form-control">
            <option value="0">永不过期</option>
            <option value="1">1小时</option>
            <option value="24">1天</option>
            <option value="168">1周</option>
            <option value="720">1个月</option>
            <option value="8760">1年</option>
          </select>
        </div>
        <div class="form-group">
          <label>最大访问次数</label>
          <input
            v-model="shareForm.maxViews"
            type="number"
            class="form-control"
            min="0"
            placeholder="0表示无限制"
          />
        </div>
      </div>

      <!-- 分享结果 -->
      <div v-if="shareResult" class="section share-result">
        <h4>分享链接</h4>
        <div class="share-url-container">
          <input
            ref="shareUrlInput"
            :value="shareResult.share_url"
            readonly
            class="share-url-input"
          />
          <button class="copy-btn" @click="copyShareUrl">
            <i class="bi bi-copy"></i>
            复制
          </button>
        </div>
        
        <div class="share-actions">
          <button class="btn btn-secondary" @click="generateQRCode">
            <i class="bi bi-qr-code"></i>
            生成二维码
          </button>
          <button class="btn btn-secondary" @click="shareToSocial">
            <i class="bi bi-share"></i>
            社交分享
          </button>
          <button class="btn btn-secondary" @click="sendByEmail">
            <i class="bi bi-envelope"></i>
            邮件发送
          </button>
        </div>

        <!-- 二维码 -->
        <div v-if="showQRCode" class="qr-code-container">
          <img :src="qrCodeUrl" alt="分享二维码" class="qr-code" />
          <p>扫描二维码访问分享</p>
        </div>
      </div>
    </div>

    <div class="dialog-actions">
      <button class="btn btn-secondary" @click="$emit('close')">
        取消
      </button>
      <button
        class="btn btn-primary"
        @click="createShare"
        :disabled="creating || !isFormValid"
      >
        <i class="bi bi-arrow-repeat spin" v-if="creating"></i>
        {{ creating ? '创建中...' : (shareResult ? '更新分享' : '创建分享') }}
      </button>
    </div>

    <!-- 社交分享弹窗 -->
    <div v-if="showSocialShare" class="social-share-modal" @click="showSocialShare = false">
      <div class="social-share-content" @click.stop>
        <h4>分享到社交平台</h4>
        <div class="social-platforms">
          <button class="social-btn wechat" @click="shareToWechat">
            <i class="bi bi-wechat"></i>
            微信
          </button>
          <button class="social-btn weibo" @click="shareToWeibo">
            <i class="bi bi-sina-weibo"></i>
            微博
          </button>
          <button class="social-btn qq" @click="shareToQQ">
            <i class="bi bi-tencent-qq"></i>
            QQ
          </button>
          <button class="social-btn twitter" @click="shareToTwitter">
            <i class="bi bi-twitter"></i>
            Twitter
          </button>
          <button class="social-btn facebook" @click="shareToFacebook">
            <i class="bi bi-facebook"></i>
            Facebook
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue'

interface Props {
  resourceType: 'image' | 'album'
  resourceId: number
  existingShare?: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  shared: [shareResult: any]
}>()

// 响应式数据
const creating = ref(false)
const shareResult = ref<any>(null)
const showQRCode = ref(false)
const showSocialShare = ref(false)
const qrCodeUrl = ref('')
const shareUrlInput = ref<HTMLInputElement>()

// 分享表单
const shareForm = reactive({
  title: '',
  description: '',
  hasPassword: false,
  password: '',
  allowGuest: true,
  isPublic: false,
  permissions: ['view'] as string[],
  expiresIn: 0,
  maxViews: 0
})

// 计算属性
const isFormValid = computed(() => {
  if (shareForm.hasPassword && !shareForm.password) {
    return false
  }
  return shareForm.permissions.length > 0
})

// 方法
const onPasswordToggle = () => {
  if (!shareForm.hasPassword) {
    shareForm.password = ''
  }
}

const createShare = async () => {
  creating.value = true
  
  try {
    const requestData = {
      resource_type: props.resourceType,
      resource_id: props.resourceId,
      title: shareForm.title,
      description: shareForm.description,
      password: shareForm.hasPassword ? shareForm.password : '',
      allow_guest: shareForm.allowGuest,
      is_public: shareForm.isPublic,
      permissions: shareForm.permissions,
      expires_in: shareForm.expiresIn,
      max_views: shareForm.maxViews
    }

    // TODO: 调用API创建分享
    // const response = await api.createShare(requestData)
    // shareResult.value = response.data
    
    // 模拟API响应
    await new Promise(resolve => setTimeout(resolve, 1000))
    shareResult.value = {
      share: {
        id: Date.now(),
        share_token: 'mock_token_' + Date.now(),
        ...requestData
      },
      share_url: `https://example.com/share/mock_token_${Date.now()}`,
      qr_code_url: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://example.com/share/mock_token_${Date.now()}`
    }
    
    emit('shared', shareResult.value)
    
  } catch (error) {
    console.error('Failed to create share:', error)
  } finally {
    creating.value = false
  }
}

const copyShareUrl = async () => {
  if (!shareResult.value) return
  
  try {
    await navigator.clipboard.writeText(shareResult.value.share_url)
    // TODO: 显示成功提示
    console.log('分享链接已复制到剪贴板')
  } catch (error) {
    // 降级方案
    if (shareUrlInput.value) {
      shareUrlInput.value.select()
      document.execCommand('copy')
      console.log('分享链接已复制到剪贴板')
    }
  }
}

const generateQRCode = () => {
  if (!shareResult.value) return
  
  qrCodeUrl.value = shareResult.value.qr_code_url
  showQRCode.value = !showQRCode.value
}

const shareToSocial = () => {
  showSocialShare.value = true
}

const sendByEmail = () => {
  if (!shareResult.value) return
  
  const subject = encodeURIComponent(`分享：${shareForm.title || '图片分享'}`)
  const body = encodeURIComponent(`
我想与您分享一些内容：

${shareForm.title || '图片分享'}
${shareForm.description || ''}

访问链接：${shareResult.value.share_url}

${shareForm.hasPassword ? '访问密码：' + shareForm.password : ''}
  `.trim())
  
  window.open(`mailto:?subject=${subject}&body=${body}`)
}

const shareToWechat = () => {
  // 微信分享通常需要微信SDK
  console.log('分享到微信')
  showSocialShare.value = false
}

const shareToWeibo = () => {
  if (!shareResult.value) return
  
  const text = encodeURIComponent(`${shareForm.title || '图片分享'} ${shareResult.value.share_url}`)
  window.open(`https://service.weibo.com/share/share.php?url=${encodeURIComponent(shareResult.value.share_url)}&title=${text}`)
  showSocialShare.value = false
}

const shareToQQ = () => {
  if (!shareResult.value) return
  
  const url = encodeURIComponent(shareResult.value.share_url)
  const title = encodeURIComponent(shareForm.title || '图片分享')
  window.open(`https://connect.qq.com/widget/shareqq/index.html?url=${url}&title=${title}`)
  showSocialShare.value = false
}

const shareToTwitter = () => {
  if (!shareResult.value) return
  
  const text = encodeURIComponent(`${shareForm.title || '图片分享'} ${shareResult.value.share_url}`)
  window.open(`https://twitter.com/intent/tweet?text=${text}`)
  showSocialShare.value = false
}

const shareToFacebook = () => {
  if (!shareResult.value) return
  
  const url = encodeURIComponent(shareResult.value.share_url)
  window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`)
  showSocialShare.value = false
}

// 初始化
if (props.existingShare) {
  Object.assign(shareForm, props.existingShare)
  shareResult.value = props.existingShare
}
</script>

<style scoped>
.share-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.dialog-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.dialog-content {
  padding: 24px;
}

.section {
  margin-bottom: 24px;
}

.section h4 {
  margin: 0 0 16px 0;
  font-size: 1rem;
  color: #495057;
  font-weight: 600;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.permission-item:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.permission-item input[type="checkbox"] {
  margin: 0;
}

.permission-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
}

.permission-info i {
  font-size: 1rem;
  color: #6c757d;
}

.share-result {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.share-url-container {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.share-url-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  font-family: monospace;
  font-size: 0.875rem;
}

.copy-btn {
  padding: 10px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.copy-btn:hover {
  background: #0056b3;
}

.share-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.qr-code-container {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
}

.qr-code {
  max-width: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.social-share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.social-share-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
}

.social-share-content h4 {
  margin: 0 0 20px 0;
  text-align: center;
}

.social-platforms {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.social-btn {
  padding: 12px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.social-btn.wechat {
  background: #07c160;
  color: white;
}

.social-btn.weibo {
  background: #e6162d;
  color: white;
}

.social-btn.qq {
  background: #12b7f5;
  color: white;
}

.social-btn.twitter {
  background: #1da1f2;
  color: white;
}

.social-btn.facebook {
  background: #1877f2;
  color: white;
}

.social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.mt-2 {
  margin-top: 8px;
}
</style>
