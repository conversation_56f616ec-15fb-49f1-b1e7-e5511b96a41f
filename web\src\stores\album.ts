import { defineStore } from 'pinia';
import type { Album, AlbumCreateRequest, AlbumUpdateRequest } from '../types/image';
import api from '../services/api';

export const useAlbumStore = defineStore('album', {
  state: () => ({
    albums: [] as Album[],
    currentAlbum: null as Album | null,
    loading: false,
  }),

  getters: {
    getAlbumList: (state) => state.albums,
    getCurrentAlbum: (state) => state.currentAlbum,
    isLoading: (state) => state.loading,
    getAlbumById: (state) => (id: number) => state.albums.find(album => album.id === id),
  },

  actions: {
    // 获取相册列表
    async fetchAlbums() {
      this.loading = true;
      try {
        this.albums = await api.getAlbums();
        return { success: true };
      } catch (error: any) {
        console.error('Fetch albums error:', error);
        // 即使获取失败，也要确保albums是空数组而不是undefined
        this.albums = [];
        return {
          success: false,
          message: error.response?.data?.message || '获取相册列表失败'
        };
      } finally {
        this.loading = false;
      }
    },

    // 创建相册
    async createAlbum(data: AlbumCreateRequest) {
      this.loading = true;
      try {
        const newAlbum = await api.createAlbum(data);
        this.albums.push(newAlbum);
        return { success: true, album: newAlbum };
      } catch (error: any) {
        console.error('Create album error:', error);
        return { 
          success: false, 
          message: error.response?.data?.message || '创建相册失败' 
        };
      } finally {
        this.loading = false;
      }
    },

    // 获取单个相册详情
    async fetchAlbum(id: number) {
      this.loading = true;
      try {
        this.currentAlbum = await api.getAlbum(id);
        return { success: true, album: this.currentAlbum };
      } catch (error: any) {
        console.error('Fetch album error:', error);
        return { 
          success: false, 
          message: error.response?.data?.message || '获取相册详情失败' 
        };
      } finally {
        this.loading = false;
      }
    },

    // 更新相册
    async updateAlbum(id: number, data: AlbumUpdateRequest) {
      this.loading = true;
      try {
        const updatedAlbum = await api.updateAlbum(id, data);
        
        // 更新本地状态
        const index = this.albums.findIndex(album => album.id === id);
        if (index !== -1) {
          this.albums[index] = updatedAlbum;
        }
        
        if (this.currentAlbum && this.currentAlbum.id === id) {
          this.currentAlbum = updatedAlbum;
        }
        
        return { success: true, album: updatedAlbum };
      } catch (error: any) {
        console.error('Update album error:', error);
        return { 
          success: false, 
          message: error.response?.data?.message || '更新相册失败' 
        };
      } finally {
        this.loading = false;
      }
    },

    // 删除相册
    async deleteAlbum(id: number) {
      this.loading = true;
      try {
        await api.deleteAlbum(id);
        
        // 从本地状态中移除相册
        this.albums = this.albums.filter(album => album.id !== id);
        
        if (this.currentAlbum && this.currentAlbum.id === id) {
          this.currentAlbum = null;
        }
        
        return { success: true };
      } catch (error: any) {
        console.error('Delete album error:', error);
        return { 
          success: false, 
          message: error.response?.data?.message || '删除相册失败' 
        };
      } finally {
        this.loading = false;
      }
    },

    // 移动图片到相册
    async moveImagesToAlbum(imageIds: number[], albumId?: number) {
      try {
        await api.moveImagesToAlbum({ image_ids: imageIds, album_id: albumId });
        
        // 更新相册的图片数量（简单实现，实际应该重新获取）
        if (albumId) {
          const album = this.albums.find(a => a.id === albumId);
          if (album) {
            album.image_count += imageIds.length;
          }
        }
        
        return { success: true };
      } catch (error: any) {
        console.error('Move images error:', error);
        return { 
          success: false, 
          message: error.response?.data?.message || '移动图片失败' 
        };
      }
    },

    // 清空当前相册
    clearCurrentAlbum() {
      this.currentAlbum = null;
    },

    // 重置状态
    reset() {
      this.albums = [];
      this.currentAlbum = null;
      this.loading = false;
    }
  },
});
