<template>
  <div class="storage-quota-card">
    <div class="card-header">
      <h4><i class="bi bi-hdd"></i> 存储使用情况</h4>
      <button class="refresh-btn" @click="loadStorageInfo" :disabled="storageStore.loading">
        <i class="bi bi-arrow-clockwise" :class="{ 'spinning': storageStore.loading }"></i>
      </button>
    </div>
    
    <div class="card-body" v-if="storageStore.storageInfo">
      <!-- 存储使用进度 -->
      <div class="storage-progress">
        <div class="progress-header">
          <span class="usage-text">
            {{ formatBytes(storageStore.storageInfo.storage_used) }} / {{ formatBytes(storageStore.storageInfo.storage_quota) }}
          </span>
          <span class="usage-percent" :class="getUsageClass(storageStore.storageInfo.usage_percent)">
            {{ storageStore.storageInfo.usage_percent.toFixed(1) }}%
          </span>
        </div>
        <div class="progress-bar">
          <div
            class="progress-fill"
            :class="getUsageClass(storageStore.storageInfo.usage_percent)"
            :style="{ width: Math.min(storageStore.storageInfo.usage_percent, 100) + '%' }"
          ></div>
        </div>
      </div>

      <!-- 存储详情 -->
      <div class="storage-details">
        <div class="detail-item">
          <div class="detail-label">
            <i class="bi bi-hdd-stack"></i>
            总配额
          </div>
          <div class="detail-value">{{ formatBytes(storageStore.storageInfo.storage_quota) }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">
            <i class="bi bi-hdd-fill"></i>
            已使用
          </div>
          <div class="detail-value">{{ formatBytes(storageStore.storageInfo.storage_used) }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">
            <i class="bi bi-hdd"></i>
            可用空间
          </div>
          <div class="detail-value available">
            {{ formatBytes(storageStore.storageInfo.storage_quota - storageStore.storageInfo.storage_used) }}
          </div>
        </div>
      </div>

      <!-- 状态提示 -->
      <div class="storage-status" v-if="storageStore.storageInfo.usage_percent >= 80">
        <div class="status-alert" :class="getStatusAlertClass(storageStore.storageInfo.usage_percent)">
          <i class="bi" :class="getStatusIcon(storageStore.storageInfo.usage_percent)"></i>
          <div class="alert-content">
            <div class="alert-title">{{ getStatusTitle(storageStore.storageInfo.usage_percent) }}</div>
            <div class="alert-message">{{ getStatusMessage(storageStore.storageInfo.usage_percent) }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-body loading-state" v-else-if="storageStore.loading">
      <div class="loading-spinner">
        <i class="bi bi-arrow-clockwise spinning"></i>
        <span>加载中...</span>
      </div>
    </div>

    <div class="card-body error-state" v-else-if="storageStore.error">
      <div class="error-message">
        <i class="bi bi-exclamation-triangle"></i>
        <span>{{ storageStore.error }}</span>
        <button class="retry-btn" @click="loadStorageInfo">重试</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import { useStorageStore } from '../stores/storage';
import { formatBytes } from '../types/storage';

// 使用存储状态管理
const storageStore = useStorageStore();

// 定时器引用
const refreshTimer = ref<number | null>(null);

// 生命周期
onMounted(() => {
  storageStore.loadStorageInfo();

  // 设置定期刷新（每30秒刷新一次）
  refreshTimer.value = setInterval(() => {
    storageStore.refreshStorageInfo();
  }, 30000);
});

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
  }
});

// 方法
const loadStorageInfo = async () => {
  await storageStore.refreshStorageInfo();
};

const getUsageClass = (percent: number) => {
  if (percent >= 100) return 'danger';
  if (percent >= 80) return 'warning';
  return 'success';
};

const getStatusAlertClass = (percent: number) => {
  if (percent >= 100) return 'alert-danger';
  if (percent >= 90) return 'alert-warning';
  return 'alert-info';
};

const getStatusIcon = (percent: number) => {
  if (percent >= 100) return 'bi-exclamation-triangle-fill';
  if (percent >= 90) return 'bi-exclamation-triangle';
  return 'bi-info-circle';
};

const getStatusTitle = (percent: number) => {
  if (percent >= 100) return '存储空间已满';
  if (percent >= 90) return '存储空间不足';
  return '存储空间紧张';
};

const getStatusMessage = (percent: number) => {
  if (percent >= 100) return '无法上传新图片，请删除一些文件或联系管理员增加配额';
  if (percent >= 90) return '存储空间即将用完，建议清理不需要的图片';
  return '存储空间使用较多，建议定期清理';
};

// 暴露方法给父组件
defineExpose({
  loadStorageInfo
});
</script>

<style scoped>
.storage-quota-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h4 {
  margin: 0;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn {
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 6px 8px;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background: var(--hover-bg);
  border-color: #007bff;
  color: #007bff;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.card-body {
  padding: 20px;
}

.storage-progress {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.usage-text {
  font-family: monospace;
  font-weight: 500;
  color: var(--text-color);
}

.usage-percent {
  font-weight: 600;
  font-size: 14px;
}

.usage-percent.success {
  color: #28a745;
}

.usage-percent.warning {
  color: #ffc107;
}

.usage-percent.danger {
  color: #dc3545;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: var(--progress-bg);
  border-radius: 6px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 6px;
}

.progress-fill.success {
  background: linear-gradient(90deg, #28a745, #20c997);
}

.progress-fill.warning {
  background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.progress-fill.danger {
  background: linear-gradient(90deg, #dc3545, #e74c3c);
}

.storage-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.detail-item {
  text-align: center;
  padding: 15px;
  background: var(--card-bg);
  border-radius: 6px;
  border: 1px solid var(--border-light);
}

.detail-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  text-transform: uppercase;
  font-weight: 500;
}

.detail-value {
  font-family: monospace;
  font-weight: 600;
  font-size: 16px;
  color: var(--text-color);
}

.detail-value.available {
  color: #28a745;
}

.storage-status {
  margin-top: 15px;
}

.status-alert {
  padding: 12px;
  border-radius: 6px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.alert-info {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

.alert-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

.alert-danger {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.alert-message {
  font-size: 14px;
  line-height: 1.4;
}

.loading-state,
.error-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.retry-btn {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s;
}

.retry-btn:hover {
  background: #0056b3;
}


</style>
