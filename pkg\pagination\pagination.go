package pagination

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Request 分页请求参数
type Request struct {
	Page     int    `json:"page" form:"page"`           // 页码，从1开始
	PageSize int    `json:"page_size" form:"page_size"` // 每页大小
	SortBy   string `json:"sort_by" form:"sort_by"`     // 排序字段
	SortDesc bool   `json:"sort_desc" form:"sort_desc"` // 是否降序
}

// Response 分页响应
type Response struct {
	Data       interface{} `json:"data"`        // 数据列表
	Total      int64       `json:"total"`       // 总记录数
	Page       int         `json:"page"`        // 当前页码
	PageSize   int         `json:"page_size"`   // 每页大小
	TotalPages int         `json:"total_pages"` // 总页数
	HasNext    bool        `json:"has_next"`    // 是否有下一页
	HasPrev    bool        `json:"has_prev"`    // 是否有上一页
}

// DefaultPageSize 默认每页大小
const DefaultPageSize = 20

// MaxPageSize 最大每页大小
const MaxPageSize = 100

// ParseRequest 从Gin上下文解析分页参数
func ParseRequest(c *gin.Context) Request {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", strconv.Itoa(DefaultPageSize)))
	sortBy := c.Query("sort_by")
	sortDesc := c.DefaultQuery("sort_desc", "false") == "true"

	return Request{
		Page:     page,
		PageSize: pageSize,
		SortBy:   sortBy,
		SortDesc: sortDesc,
	}
}

// Validate 验证分页参数
func (r *Request) Validate() {
	if r.Page <= 0 {
		r.Page = 1
	}
	if r.PageSize <= 0 {
		r.PageSize = DefaultPageSize
	}
	if r.PageSize > MaxPageSize {
		r.PageSize = MaxPageSize
	}
}

// GetOffset 计算偏移量
func (r *Request) GetOffset() int {
	return (r.Page - 1) * r.PageSize
}

// ApplyToQuery 将分页参数应用到GORM查询
func (r *Request) ApplyToQuery(query *gorm.DB, defaultSortBy string) *gorm.DB {
	r.Validate()

	// 应用排序
	sortBy := defaultSortBy
	if r.SortBy != "" {
		sortBy = r.SortBy
	}
	if r.SortDesc {
		sortBy += " DESC"
	}
	query = query.Order(sortBy)

	// 应用分页
	return query.Offset(r.GetOffset()).Limit(r.PageSize)
}

// BuildResponse 构建分页响应
func BuildResponse(data interface{}, total int64, req Request) Response {
	req.Validate()

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return Response{
		Data:       data,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
		HasNext:    req.Page < totalPages,
		HasPrev:    req.Page > 1,
	}
}

// Paginate 通用分页函数
func Paginate(query *gorm.DB, req Request, result interface{}, defaultSortBy string) (Response, error) {
	req.Validate()

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return Response{}, err
	}

	// 应用分页和排序
	paginatedQuery := req.ApplyToQuery(query, defaultSortBy)

	// 执行查询
	if err := paginatedQuery.Find(result).Error; err != nil {
		return Response{}, err
	}

	// 构建响应
	return BuildResponse(result, total, req), nil
}

// PaginateWithPreload 带预加载的分页函数
func PaginateWithPreload(query *gorm.DB, req Request, result interface{}, defaultSortBy string, preloads ...string) (Response, error) {
	req.Validate()

	// 计算总数（不包含预加载）
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return Response{}, err
	}

	// 应用预加载
	for _, preload := range preloads {
		query = query.Preload(preload)
	}

	// 应用分页和排序
	paginatedQuery := req.ApplyToQuery(query, defaultSortBy)

	// 执行查询
	if err := paginatedQuery.Find(result).Error; err != nil {
		return Response{}, err
	}

	// 构建响应
	return BuildResponse(result, total, req), nil
}

// ImagePaginationRequest 图片分页请求
type ImagePaginationRequest struct {
	Request
	AlbumID *uint  `json:"album_id" form:"album_id"` // 相册ID过滤
	Search  string `json:"search" form:"search"`     // 搜索关键词
}

// AlbumPaginationRequest 相册分页请求
type AlbumPaginationRequest struct {
	Request
	Search string `json:"search" form:"search"` // 搜索关键词
}

// TagPaginationRequest 标签分页请求
type TagPaginationRequest struct {
	Request
	Search string `json:"search" form:"search"` // 搜索关键词
}

// ParseImageRequest 解析图片分页请求
func ParseImageRequest(c *gin.Context) ImagePaginationRequest {
	req := ParseRequest(c)

	var albumID *uint
	if albumIDStr := c.Query("album_id"); albumIDStr != "" && albumIDStr != "uncategorized" {
		if id, err := strconv.ParseUint(albumIDStr, 10, 32); err == nil {
			albumIDUint := uint(id)
			albumID = &albumIDUint
		}
	}

	return ImagePaginationRequest{
		Request: req,
		AlbumID: albumID,
		Search:  c.Query("search"),
	}
}

// ParseAlbumRequest 解析相册分页请求
func ParseAlbumRequest(c *gin.Context) AlbumPaginationRequest {
	req := ParseRequest(c)

	return AlbumPaginationRequest{
		Request: req,
		Search:  c.Query("search"),
	}
}

// ParseTagRequest 解析标签分页请求
func ParseTagRequest(c *gin.Context) TagPaginationRequest {
	req := ParseRequest(c)

	return TagPaginationRequest{
		Request: req,
		Search:  c.Query("search"),
	}
}
