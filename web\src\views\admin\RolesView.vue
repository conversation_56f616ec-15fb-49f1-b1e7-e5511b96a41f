<template>
  <div class="roles-container">
    <div class="page-header">
      <h2><i class="bi bi-key"></i> 角色权限管理</h2>
      <p class="page-subtitle">管理系统角色和权限分配</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="info-cards">
        <div class="info-card">
          <div class="info-icon">
            <i class="bi bi-shield-check"></i>
          </div>
          <div class="info-content">
            <div class="info-number">{{ roles.length }}</div>
            <div class="info-label">总角色数</div>
          </div>
        </div>
        <div class="info-card">
          <div class="info-icon">
            <i class="bi bi-people"></i>
          </div>
          <div class="info-content">
            <div class="info-number">{{ totalUsers }}</div>
            <div class="info-label">总用户数</div>
          </div>
        </div>
      </div>
      <button class="btn btn-primary" @click="showCreateModal = true">
        <i class="bi bi-plus-circle"></i> 创建角色
      </button>
    </div>

    <!-- 角色列表 -->
    <div class="roles-grid">
      <div 
        v-for="role in roles" 
        :key="role.id" 
        class="role-card"
        :class="{ 'system-role': role.is_system }"
      >
        <div class="role-header">
          <div class="role-info">
            <h3 class="role-name">{{ role.display_name }}</h3>
            <p class="role-description">{{ role.description }}</p>
          </div>
          <div class="role-badge" v-if="role.is_system">
            <i class="bi bi-shield-lock"></i>
            系统角色
          </div>
        </div>
        
        <div class="role-stats">
          <div class="stat-item">
            <i class="bi bi-people"></i>
            <span>{{ role.user_count }} 个用户</span>
          </div>
          <div class="stat-item">
            <i class="bi bi-key"></i>
            <span>{{ role.permissions.length }} 个权限</span>
          </div>
        </div>

        <div class="role-permissions">
          <h4>权限列表</h4>
          <div class="permissions-grid">
            <span 
              v-for="permission in role.permissions.slice(0, 6)" 
              :key="permission.id"
              class="permission-tag"
            >
              {{ permission.display_name }}
            </span>
            <span v-if="role.permissions.length > 6" class="permission-more">
              +{{ role.permissions.length - 6 }} 更多
            </span>
          </div>
        </div>

        <div class="role-actions">
          <button 
            class="btn btn-sm btn-outline" 
            @click="viewRole(role)"
          >
            <i class="bi bi-eye"></i> 查看
          </button>
          <button 
            class="btn btn-sm btn-outline" 
            @click="editRole(role)"
            v-if="!role.is_system"
          >
            <i class="bi bi-pencil"></i> 编辑
          </button>
          <button 
            class="btn btn-sm btn-danger" 
            @click="deleteRole(role)"
            v-if="!role.is_system && role.user_count === 0"
          >
            <i class="bi bi-trash"></i> 删除
          </button>
        </div>
      </div>
    </div>

    <!-- 创建/编辑角色模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModals">
      <div class="modal-content large-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ showCreateModal ? '创建角色' : '编辑角色' }}</h3>
          <button class="close-btn" @click="closeModals">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitRoleForm">
            <div class="form-row">
              <div class="form-group">
                <label for="name">角色名称（英文）</label>
                <input 
                  id="name" 
                  v-model="roleForm.name" 
                  type="text" 
                  required 
                  :disabled="loading || showEditModal"
                  placeholder="例如: custom_manager"
                />
              </div>
              <div class="form-group">
                <label for="display_name">显示名称</label>
                <input 
                  id="display_name" 
                  v-model="roleForm.display_name" 
                  type="text" 
                  required 
                  :disabled="loading"
                  placeholder="例如: 自定义管理员"
                />
              </div>
            </div>
            
            <div class="form-group">
              <label for="description">角色描述</label>
              <textarea 
                id="description" 
                v-model="roleForm.description" 
                :disabled="loading"
                placeholder="描述这个角色的职责和权限范围"
                rows="3"
              ></textarea>
            </div>
            
            <div class="form-group">
              <label>权限分配</label>
              <div class="permissions-section">
                <div 
                  v-for="(perms, resource) in groupedPermissions"
                  :key="resource"
                  class="permission-group"
                >
                  <h4 class="permission-group-title">
                    <i :class="getResourceIcon(String(resource))"></i>
                    {{ getResourceName(String(resource)) }}
                  </h4>
                  <div class="permission-checkboxes">
                    <label 
                      v-for="permission in perms" 
                      :key="permission.id"
                      class="permission-checkbox"
                    >
                      <input 
                        type="checkbox" 
                        :value="permission.id"
                        v-model="roleForm.permission_ids"
                        :disabled="loading"
                      />
                      <span class="checkbox-label">{{ permission.display_name }}</span>
                      <small class="permission-desc">{{ permission.description }}</small>
                    </label>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" @click="closeModals" :disabled="loading">
                取消
              </button>
              <button type="submit" class="btn btn-primary" :disabled="loading">
                {{ loading ? '处理中...' : (showCreateModal ? '创建角色' : '更新角色') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 查看角色详情模态框 -->
    <div v-if="showViewModal" class="modal-overlay" @click="closeModals">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ viewingRole?.display_name }}</h3>
          <button class="close-btn" @click="closeModals">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="role-detail">
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>角色名称</label>
                  <span>{{ viewingRole?.name }}</span>
                </div>
                <div class="detail-item">
                  <label>显示名称</label>
                  <span>{{ viewingRole?.display_name }}</span>
                </div>
                <div class="detail-item">
                  <label>角色类型</label>
                  <span>{{ viewingRole?.is_system ? '系统角色' : '自定义角色' }}</span>
                </div>
                <div class="detail-item">
                  <label>用户数量</label>
                  <span>{{ viewingRole?.user_count }} 个用户</span>
                </div>
              </div>
              <div class="detail-item full-width">
                <label>角色描述</label>
                <p>{{ viewingRole?.description }}</p>
              </div>
            </div>
            
            <div class="detail-section">
              <h4>权限列表 ({{ viewingRole?.permissions.length }})</h4>
              <div class="permissions-list">
                <div 
                  v-for="(perms, resource) in getGroupedPermissions(viewingRole?.permissions || [])"
                  :key="resource"
                  class="permission-group-view"
                >
                  <h5>
                    <i :class="getResourceIcon(String(resource))"></i>
                    {{ getResourceName(String(resource)) }}
                  </h5>
                  <div class="permission-tags">
                    <span 
                      v-for="permission in perms" 
                      :key="permission.id"
                      class="permission-tag"
                    >
                      {{ permission.display_name }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知 -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue';
import { useDialog } from '../../utils/dialog';

// 响应式数据
const roles = ref<any[]>([]);
const permissions = ref<any[]>([]);
const loading = ref(false);
const { confirmDelete } = useDialog();
const showCreateModal = ref(false);
const showEditModal = ref(false);
const showViewModal = ref(false);
const editingRole = ref<any>(null);
const viewingRole = ref<any>(null);
const totalUsers = ref(0);

// 表单数据
const roleForm = reactive({
  name: '',
  display_name: '',
  description: '',
  permission_ids: [] as number[]
});

// 通知
const notification = reactive({
  show: false,
  message: '',
  type: 'success'
});

// 计算属性
const groupedPermissions = computed(() => {
  const grouped: { [key: string]: any[] } = {};
  permissions.value.forEach((permission: any) => {
    if (!grouped[permission.resource]) {
      grouped[permission.resource] = [];
    }
    grouped[permission.resource].push(permission);
  });
  return grouped;
});

// 方法
const fetchRoles = async () => {
  // TODO: 实现获取角色列表的API调用
  console.log('Fetching roles...');
};

const fetchPermissions = async () => {
  // TODO: 实现获取权限列表的API调用
  console.log('Fetching permissions...');
};

const viewRole = (role: any) => {
  viewingRole.value = role;
  showViewModal.value = true;
};

const editRole = (role: any) => {
  editingRole.value = role;
  roleForm.name = role.name;
  roleForm.display_name = role.display_name;
  roleForm.description = role.description;
  roleForm.permission_ids = role.permissions.map((p: any) => p.id);
  showEditModal.value = true;
};

const submitRoleForm = async () => {
  // TODO: 实现创建/更新角色的API调用
  console.log('Submitting role form...');
};

const deleteRole = async (role: any) => {
  const confirmed = await confirmDelete(role.display_name, {
    details: '删除角色后，拥有该角色的用户将失去相应权限。'
  });

  if (confirmed) {
    // TODO: 实现删除角色的API调用
    console.log('Deleting role:', role.id);
  }
};

const closeModals = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  showViewModal.value = false;
  editingRole.value = null;
  viewingRole.value = null;
  Object.assign(roleForm, {
    name: '',
    display_name: '',
    description: '',
    permission_ids: []
  });
};

// 工具函数
const getResourceIcon = (resource: string) => {
  const icons: { [key: string]: string } = {
    'user': 'bi bi-people',
    'role': 'bi bi-shield',
    'permission': 'bi bi-key',
    'image': 'bi bi-image',
    'album': 'bi bi-collection',
    'system': 'bi bi-gear'
  };
  return icons[resource] || 'bi bi-circle';
};

const getResourceName = (resource: string) => {
  const names: { [key: string]: string } = {
    'user': '用户管理',
    'role': '角色管理',
    'permission': '权限管理',
    'image': '图片管理',
    'album': '相册管理',
    'system': '系统管理'
  };
  return names[resource] || resource;
};

const getGroupedPermissions = (perms: any[]) => {
  const grouped: { [key: string]: any[] } = {};
  perms.forEach((permission: any) => {
    if (!grouped[permission.resource]) {
      grouped[permission.resource] = [];
    }
    grouped[permission.resource].push(permission);
  });
  return grouped;
};

// const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
//   notification.message = message;
//   notification.type = type;
//   notification.show = true;
//   setTimeout(() => {
//     notification.show = false;
//   }, 3000);
// };

// 生命周期
onMounted(() => {
  fetchRoles();
  fetchPermissions();
});
</script>

<style scoped>
/* 样式将在下一个编辑中添加 */
</style>
