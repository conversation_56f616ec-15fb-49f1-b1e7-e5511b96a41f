package database

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/dbresolver"
)

// DatabaseCluster 数据库集群
type DatabaseCluster struct {
	master   *gorm.DB
	slaves   []*gorm.DB
	config   ClusterConfig
	metrics  *DatabaseMetrics
	mutex    sync.RWMutex
}

// ClusterConfig 集群配置
type ClusterConfig struct {
	Master DatabaseConfig   `json:"master"`
	Slaves []DatabaseConfig `json:"slaves"`
	
	// 连接池配置
	MaxOpenConns    int           `json:"max_open_conns"`
	MaxIdleConns    int           `json:"max_idle_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `json:"conn_max_idle_time"`
	
	// 读写分离配置
	ReadWriteSplit  bool    `json:"read_write_split"`
	SlaveWeight     []int   `json:"slave_weight"`
	FailoverEnabled bool    `json:"failover_enabled"`
	HealthCheck     bool    `json:"health_check"`
	
	// 分片配置
	ShardingEnabled bool              `json:"sharding_enabled"`
	ShardingRules   []ShardingRule    `json:"sharding_rules"`
	
	// 缓存配置
	CacheEnabled    bool          `json:"cache_enabled"`
	CacheTTL        time.Duration `json:"cache_ttl"`
	CacheSize       int           `json:"cache_size"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Database string `json:"database"`
	Username string `json:"username"`
	Password string `json:"password"`
	Charset  string `json:"charset"`
	TimeZone string `json:"timezone"`
}

// ShardingRule 分片规则
type ShardingRule struct {
	Table      string `json:"table"`
	ShardKey   string `json:"shard_key"`
	ShardCount int    `json:"shard_count"`
	Algorithm  string `json:"algorithm"` // hash, range, list
}

// DatabaseMetrics 数据库指标
type DatabaseMetrics struct {
	QueryCount      int64         `json:"query_count"`
	SlowQueryCount  int64         `json:"slow_query_count"`
	ErrorCount      int64         `json:"error_count"`
	AvgQueryTime    time.Duration `json:"avg_query_time"`
	ConnectionCount int           `json:"connection_count"`
	CacheHitRate    float64       `json:"cache_hit_rate"`
	LastUpdated     time.Time     `json:"last_updated"`
}

// QueryContext 查询上下文
type QueryContext struct {
	SQL        string
	Args       []interface{}
	ReadOnly   bool
	TableName  string
	ShardKey   interface{}
	CacheKey   string
	CacheTTL   time.Duration
}

// NewDatabaseCluster 创建数据库集群
func NewDatabaseCluster(config ClusterConfig) (*DatabaseCluster, error) {
	cluster := &DatabaseCluster{
		config:  config,
		metrics: &DatabaseMetrics{},
	}

	// 连接主数据库
	master, err := cluster.connectDatabase(config.Master)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to master database: %v", err)
	}
	cluster.master = master

	// 连接从数据库
	for _, slaveConfig := range config.Slaves {
		slave, err := cluster.connectDatabase(slaveConfig)
		if err != nil {
			// 从库连接失败不影响主库使用
			continue
		}
		cluster.slaves = append(cluster.slaves, slave)
	}

	// 配置读写分离
	if config.ReadWriteSplit && len(cluster.slaves) > 0 {
		err = cluster.setupReadWriteSplit()
		if err != nil {
			return nil, fmt.Errorf("failed to setup read-write split: %v", err)
		}
	}

	// 启动健康检查
	if config.HealthCheck {
		go cluster.healthCheck()
	}

	return cluster, nil
}

// connectDatabase 连接数据库
func (dc *DatabaseCluster) connectDatabase(config DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=%s",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
		config.Charset,
		config.TimeZone,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	sqlDB.SetMaxOpenConns(dc.config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(dc.config.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(dc.config.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(dc.config.ConnMaxIdleTime)

	return db, nil
}

// setupReadWriteSplit 设置读写分离
func (dc *DatabaseCluster) setupReadWriteSplit() error {
	var replicas []gorm.Dialector
	for _, slave := range dc.slaves {
		sqlDB, err := slave.DB()
		if err != nil {
			continue
		}
		replicas = append(replicas, mysql.New(mysql.Config{
			Conn: sqlDB,
		}))
	}

	if len(replicas) == 0 {
		return fmt.Errorf("no available slave databases")
	}

	err := dc.master.Use(dbresolver.Register(dbresolver.Config{
		Replicas: replicas,
		Policy:   dbresolver.RandomPolicy{},
	}))

	return err
}

// GetMaster 获取主数据库
func (dc *DatabaseCluster) GetMaster() *gorm.DB {
	return dc.master
}

// GetSlave 获取从数据库
func (dc *DatabaseCluster) GetSlave() *gorm.DB {
	dc.mutex.RLock()
	defer dc.mutex.RUnlock()

	if len(dc.slaves) == 0 {
		return dc.master
	}

	// 简单的轮询负载均衡
	index := int(time.Now().UnixNano()) % len(dc.slaves)
	return dc.slaves[index]
}

// Query 执行查询
func (dc *DatabaseCluster) Query(ctx context.Context, qctx *QueryContext) *gorm.DB {
	start := time.Now()
	defer func() {
		dc.updateMetrics(time.Since(start))
	}()

	var db *gorm.DB
	if qctx.ReadOnly && dc.config.ReadWriteSplit {
		db = dc.GetSlave()
	} else {
		db = dc.GetMaster()
	}

	// 应用分片规则
	if dc.config.ShardingEnabled {
		db = dc.applySharding(db, qctx)
	}

	return db.WithContext(ctx)
}

// applySharding 应用分片规则
func (dc *DatabaseCluster) applySharding(db *gorm.DB, qctx *QueryContext) *gorm.DB {
	for _, rule := range dc.config.ShardingRules {
		if rule.Table == qctx.TableName {
			shardSuffix := dc.calculateShard(qctx.ShardKey, rule)
			tableName := fmt.Sprintf("%s_%s", rule.Table, shardSuffix)
			return db.Table(tableName)
		}
	}
	return db
}

// calculateShard 计算分片
func (dc *DatabaseCluster) calculateShard(shardKey interface{}, rule ShardingRule) string {
	switch rule.Algorithm {
	case "hash":
		hash := dc.hashShardKey(shardKey)
		return fmt.Sprintf("%d", hash%rule.ShardCount)
	case "range":
		// TODO: 实现范围分片
		return "0"
	case "list":
		// TODO: 实现列表分片
		return "0"
	default:
		return "0"
	}
}

// hashShardKey 计算分片键哈希值
func (dc *DatabaseCluster) hashShardKey(key interface{}) int {
	// 简单的哈希实现
	str := fmt.Sprintf("%v", key)
	hash := 0
	for _, char := range str {
		hash = hash*31 + int(char)
	}
	if hash < 0 {
		hash = -hash
	}
	return hash
}

// healthCheck 健康检查
func (dc *DatabaseCluster) healthCheck() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			dc.checkDatabaseHealth()
		}
	}
}

// checkDatabaseHealth 检查数据库健康状态
func (dc *DatabaseCluster) checkDatabaseHealth() {
	// 检查主数据库
	if err := dc.pingDatabase(dc.master); err != nil {
		// 主数据库不可用，记录错误
		dc.metrics.ErrorCount++
	}

	// 检查从数据库
	dc.mutex.Lock()
	var healthySlaves []*gorm.DB
	for _, slave := range dc.slaves {
		if err := dc.pingDatabase(slave); err == nil {
			healthySlaves = append(healthySlaves, slave)
		}
	}
	dc.slaves = healthySlaves
	dc.mutex.Unlock()
}

// pingDatabase 检查数据库连接
func (dc *DatabaseCluster) pingDatabase(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// updateMetrics 更新指标
func (dc *DatabaseCluster) updateMetrics(duration time.Duration) {
	dc.metrics.QueryCount++
	dc.metrics.AvgQueryTime = (dc.metrics.AvgQueryTime + duration) / 2
	dc.metrics.LastUpdated = time.Now()

	// 慢查询检测
	if duration > 1*time.Second {
		dc.metrics.SlowQueryCount++
	}
}

// GetMetrics 获取数据库指标
func (dc *DatabaseCluster) GetMetrics() *DatabaseMetrics {
	// 更新连接数
	if sqlDB, err := dc.master.DB(); err == nil {
		stats := sqlDB.Stats()
		dc.metrics.ConnectionCount = stats.OpenConnections
	}

	return dc.metrics
}

// Close 关闭数据库连接
func (dc *DatabaseCluster) Close() error {
	// 关闭主数据库
	if sqlDB, err := dc.master.DB(); err == nil {
		sqlDB.Close()
	}

	// 关闭从数据库
	for _, slave := range dc.slaves {
		if sqlDB, err := slave.DB(); err == nil {
			sqlDB.Close()
		}
	}

	return nil
}

// Transaction 执行事务
func (dc *DatabaseCluster) Transaction(ctx context.Context, fn func(*gorm.DB) error) error {
	return dc.master.WithContext(ctx).Transaction(fn)
}

// 缓存相关方法
type QueryCache struct {
	data   map[string]interface{}
	expiry map[string]time.Time
	mutex  sync.RWMutex
}

func NewQueryCache() *QueryCache {
	cache := &QueryCache{
		data:   make(map[string]interface{}),
		expiry: make(map[string]time.Time),
	}
	
	// 启动清理协程
	go cache.cleanup()
	
	return cache
}

func (qc *QueryCache) Get(key string) (interface{}, bool) {
	qc.mutex.RLock()
	defer qc.mutex.RUnlock()
	
	if expiry, exists := qc.expiry[key]; exists {
		if time.Now().After(expiry) {
			delete(qc.data, key)
			delete(qc.expiry, key)
			return nil, false
		}
		
		if value, exists := qc.data[key]; exists {
			return value, true
		}
	}
	
	return nil, false
}

func (qc *QueryCache) Set(key string, value interface{}, ttl time.Duration) {
	qc.mutex.Lock()
	defer qc.mutex.Unlock()
	
	qc.data[key] = value
	qc.expiry[key] = time.Now().Add(ttl)
}

func (qc *QueryCache) Delete(key string) {
	qc.mutex.Lock()
	defer qc.mutex.Unlock()
	
	delete(qc.data, key)
	delete(qc.expiry, key)
}

func (qc *QueryCache) cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			qc.mutex.Lock()
			now := time.Now()
			for key, expiry := range qc.expiry {
				if now.After(expiry) {
					delete(qc.data, key)
					delete(qc.expiry, key)
				}
			}
			qc.mutex.Unlock()
		}
	}
}

// 数据库连接池监控
func (dc *DatabaseCluster) GetConnectionStats() map[string]sql.DBStats {
	stats := make(map[string]sql.DBStats)
	
	// 主数据库统计
	if sqlDB, err := dc.master.DB(); err == nil {
		stats["master"] = sqlDB.Stats()
	}
	
	// 从数据库统计
	for i, slave := range dc.slaves {
		if sqlDB, err := slave.DB(); err == nil {
			stats[fmt.Sprintf("slave_%d", i)] = sqlDB.Stats()
		}
	}
	
	return stats
}
