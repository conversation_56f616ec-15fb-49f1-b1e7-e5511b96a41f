package middleware

import (
	"log"
	"net/http"

	"cloudbed/pkg/errors"
	"cloudbed/pkg/response"

	"github.com/gin-gonic/gin"
)

// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 执行请求处理
		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			handleError(c, err.Err)
		}
	}
}

// handleError 处理错误并返回适当的响应
func handleError(c *gin.Context, err error) {
	if err == nil {
		return
	}

	// 如果是应用错误
	if appErr, ok := err.(*errors.AppError); ok {
		handleAppError(c, appErr)
		return
	}

	// 处理其他类型的错误
	log.Printf("Unhandled error: %v", err)
	response.InternalServerError(c, "服务器内部错误")
}

// handleAppError 处理应用错误
func handleAppError(c *gin.Context, appErr *errors.AppError) {
	var httpCode int
	var message string

	// 根据错误码确定HTTP状态码和消息
	switch appErr.Code {
	case errors.ErrCodeUnauthorized, errors.ErrCodeInvalidToken, errors.ErrCodeTokenExpired:
		httpCode = http.StatusUnauthorized
		message = appErr.Message
	case errors.ErrCodeInvalidPassword:
		httpCode = http.StatusUnauthorized
		message = "邮箱或密码错误"
	case errors.ErrCodeAccountDisabled:
		httpCode = http.StatusForbidden
		message = appErr.Message
	case errors.ErrCodeForbidden, errors.ErrCodeInsufficientPermission, errors.ErrCodeResourceNotOwned:
		httpCode = http.StatusForbidden
		message = appErr.Message
	case errors.ErrCodeNotFound, errors.ErrCodeFileNotFound:
		httpCode = http.StatusNotFound
		message = appErr.Message
	case errors.ErrCodeAlreadyExists:
		httpCode = http.StatusConflict
		message = appErr.Message
	case errors.ErrCodeValidationFailed, errors.ErrCodeInvalidFormat, errors.ErrCodeMissingParameter:
		httpCode = http.StatusBadRequest
		message = appErr.Message
	case errors.ErrCodeFileTypeNotAllowed, errors.ErrCodeFileTooLarge:
		httpCode = http.StatusBadRequest
		message = appErr.Message
	case errors.ErrCodeStorageQuotaExceeded:
		httpCode = http.StatusForbidden
		message = appErr.Message
	case errors.ErrCodeUploadFailed, errors.ErrCodeStorageNotAvailable:
		httpCode = http.StatusInternalServerError
		message = appErr.Message
	case errors.ErrCodeDatabaseError, errors.ErrCodeInternalError:
		httpCode = http.StatusInternalServerError
		message = "服务器内部错误"
		// 记录详细错误信息
		log.Printf("Internal error: %v", appErr)
	case errors.ErrCodeServiceUnavailable:
		httpCode = http.StatusServiceUnavailable
		message = appErr.Message
	default:
		httpCode = http.StatusInternalServerError
		message = "服务器内部错误"
		log.Printf("Unknown error code: %s, error: %v", appErr.Code, appErr)
	}

	// 返回错误响应
	if appErr.Details != "" {
		response.ErrorWithDetail(c, httpCode, httpCode, message, appErr.Details)
	} else {
		response.Error(c, httpCode, httpCode, message)
	}
}

// AbortWithError 中止请求并设置错误
func AbortWithError(c *gin.Context, err error) {
	c.Error(err)
	c.Abort()
}

// AbortWithAppError 中止请求并设置应用错误
func AbortWithAppError(c *gin.Context, appErr *errors.AppError) {
	c.Error(appErr)
	c.Abort()
}
