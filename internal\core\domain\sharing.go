package models

import (
	"time"
)

// ShareType 分享类型
type ShareType string

const (
	ShareTypeImage ShareType = "image"
	ShareTypeAlbum ShareType = "album"
)

// SharePermission 分享权限
type SharePermission string

const (
	PermissionView     SharePermission = "view"     // 只读
	PermissionComment  SharePermission = "comment"  // 评论
	PermissionDownload SharePermission = "download" // 下载
	PermissionEdit     SharePermission = "edit"     // 编辑
	PermissionManage   SharePermission = "manage"   // 管理
)

// ShareStatus 分享状态
type ShareStatus string

const (
	ShareStatusActive   ShareStatus = "active"   // 活跃
	ShareStatusExpired  ShareStatus = "expired"  // 已过期
	ShareStatusDisabled ShareStatus = "disabled" // 已禁用
	ShareStatusRevoked  ShareStatus = "revoked"  // 已撤销
)

// Share 分享记录
type Share struct {
	ID          uint        `json:"id" gorm:"primary_key"`
	ShareToken  string      `json:"share_token" gorm:"type:varchar(64);unique;not null"` // 分享令牌
	ShareType   ShareType   `json:"share_type" gorm:"type:varchar(20);not null"`         // 分享类型
	ResourceID  uint        `json:"resource_id" gorm:"not null"`                         // 资源ID（图片或相册）
	OwnerID     uint        `json:"owner_id" gorm:"not null"`                            // 分享者ID
	Owner       User        `json:"owner" gorm:"foreignKey:OwnerID"`                     // 分享者
	
	// 分享设置
	Title       string      `json:"title" gorm:"type:varchar(200)"`                      // 分享标题
	Description string      `json:"description" gorm:"type:text"`                        // 分享描述
	Password    string      `json:"-" gorm:"type:varchar(100)"`                          // 访问密码（加密存储）
	HasPassword bool        `json:"has_password" gorm:"default:false"`                   // 是否有密码
	
	// 权限设置
	Permissions []SharePermission `json:"permissions" gorm:"type:json"`                   // 权限列表
	AllowGuest  bool             `json:"allow_guest" gorm:"default:true"`                 // 允许游客访问
	
	// 时效设置
	ExpiresAt   *time.Time  `json:"expires_at"`                                          // 过期时间
	MaxViews    int         `json:"max_views" gorm:"default:0"`                          // 最大访问次数（0为无限制）
	ViewCount   int         `json:"view_count" gorm:"default:0"`                         // 当前访问次数
	
	// 状态
	Status      ShareStatus `json:"status" gorm:"type:varchar(20);default:'active'"`    // 分享状态
	IsPublic    bool        `json:"is_public" gorm:"default:false"`                     // 是否公开（搜索引擎可索引）
	
	// 统计信息
	DownloadCount int       `json:"download_count" gorm:"default:0"`                     // 下载次数
	CommentCount  int       `json:"comment_count" gorm:"default:0"`                      // 评论数量
	LikeCount     int       `json:"like_count" gorm:"default:0"`                         // 点赞数量
	
	CreatedAt   time.Time   `json:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at"`
}

// ShareAccess 分享访问记录
type ShareAccess struct {
	ID          uint      `json:"id" gorm:"primary_key"`
	ShareID     uint      `json:"share_id" gorm:"not null"`                    // 分享ID
	Share       Share     `json:"share" gorm:"foreignKey:ShareID"`             // 关联分享
	UserID      *uint     `json:"user_id"`                                     // 访问用户ID（可为空，游客访问）
	User        *User     `json:"user" gorm:"foreignKey:UserID"`               // 访问用户
	
	// 访问信息
	IPAddress   string    `json:"ip_address" gorm:"type:varchar(45)"`          // IP地址
	UserAgent   string    `json:"user_agent" gorm:"type:text"`                 // 用户代理
	Referer     string    `json:"referer" gorm:"type:varchar(500)"`            // 来源页面
	Country     string    `json:"country" gorm:"type:varchar(50)"`             // 国家
	City        string    `json:"city" gorm:"type:varchar(100)"`               // 城市
	
	// 访问行为
	Action      string    `json:"action" gorm:"type:varchar(50)"`              // 操作类型：view, download, comment
	Duration    int       `json:"duration" gorm:"default:0"`                   // 访问时长（秒）
	
	CreatedAt   time.Time `json:"created_at"`
}

// ShareCollaborator 分享协作者
type ShareCollaborator struct {
	ID          uint            `json:"id" gorm:"primary_key"`
	ShareID     uint            `json:"share_id" gorm:"not null"`                    // 分享ID
	Share       Share           `json:"share" gorm:"foreignKey:ShareID"`             // 关联分享
	UserID      uint            `json:"user_id" gorm:"not null"`                     // 协作者ID
	User        User            `json:"user" gorm:"foreignKey:UserID"`               // 协作者
	
	// 权限设置
	Permissions []SharePermission `json:"permissions" gorm:"type:json"`               // 权限列表
	Role        string           `json:"role" gorm:"type:varchar(50);default:'viewer'"` // 角色：viewer, editor, admin
	
	// 邀请信息
	InvitedBy   uint      `json:"invited_by" gorm:"not null"`                        // 邀请者ID
	Inviter     User      `json:"inviter" gorm:"foreignKey:InvitedBy"`               // 邀请者
	InviteToken string    `json:"invite_token" gorm:"type:varchar(64)"`              // 邀请令牌
	AcceptedAt  *time.Time `json:"accepted_at"`                                      // 接受时间
	
	// 状态
	Status      string    `json:"status" gorm:"type:varchar(20);default:'pending'"` // pending, accepted, declined, removed
	
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Comment 评论
type Comment struct {
	ID          uint      `json:"id" gorm:"primary_key"`
	ShareID     uint      `json:"share_id" gorm:"not null"`                    // 分享ID
	Share       Share     `json:"share" gorm:"foreignKey:ShareID"`             // 关联分享
	UserID      *uint     `json:"user_id"`                                     // 评论用户ID（可为空，游客评论）
	User        *User     `json:"user" gorm:"foreignKey:UserID"`               // 评论用户
	
	// 评论内容
	Content     string    `json:"content" gorm:"type:text;not null"`           // 评论内容
	AuthorName  string    `json:"author_name" gorm:"type:varchar(100)"`        // 作者名称（游客评论时使用）
	AuthorEmail string    `json:"author_email" gorm:"type:varchar(200)"`       // 作者邮箱（游客评论时使用）
	
	// 回复关系
	ParentID    *uint     `json:"parent_id"`                                   // 父评论ID
	Parent      *Comment  `json:"parent" gorm:"foreignKey:ParentID"`           // 父评论
	Replies     []Comment `json:"replies" gorm:"foreignKey:ParentID"`          // 回复列表
	
	// 位置信息（图片标注）
	PositionX   *float64  `json:"position_x"`                                  // X坐标（相对位置 0-1）
	PositionY   *float64  `json:"position_y"`                                  // Y坐标（相对位置 0-1）
	
	// 状态
	Status      string    `json:"status" gorm:"type:varchar(20);default:'active'"` // active, hidden, deleted
	LikeCount   int       `json:"like_count" gorm:"default:0"`                 // 点赞数
	
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CommentLike 评论点赞
type CommentLike struct {
	ID        uint      `json:"id" gorm:"primary_key"`
	CommentID uint      `json:"comment_id" gorm:"not null"`              // 评论ID
	Comment   Comment   `json:"comment" gorm:"foreignKey:CommentID"`     // 关联评论
	UserID    *uint     `json:"user_id"`                                 // 点赞用户ID
	User      *User     `json:"user" gorm:"foreignKey:UserID"`           // 点赞用户
	IPAddress string    `json:"ip_address" gorm:"type:varchar(45)"`      // IP地址（游客点赞）
	
	CreatedAt time.Time `json:"created_at"`
}

// ShareLike 分享点赞
type ShareLike struct {
	ID        uint      `json:"id" gorm:"primary_key"`
	ShareID   uint      `json:"share_id" gorm:"not null"`                // 分享ID
	Share     Share     `json:"share" gorm:"foreignKey:ShareID"`         // 关联分享
	UserID    *uint     `json:"user_id"`                                 // 点赞用户ID
	User      *User     `json:"user" gorm:"foreignKey:UserID"`           // 点赞用户
	IPAddress string    `json:"ip_address" gorm:"type:varchar(45)"`      // IP地址（游客点赞）
	
	CreatedAt time.Time `json:"created_at"`
}

// ShareRequest 分享请求结构
type ShareRequest struct {
	ResourceType string            `json:"resource_type" binding:"required,oneof=image album"` // 资源类型
	ResourceID   uint              `json:"resource_id" binding:"required"`                     // 资源ID
	Title        string            `json:"title"`                                              // 分享标题
	Description  string            `json:"description"`                                        // 分享描述
	Password     string            `json:"password"`                                           // 访问密码
	Permissions  []SharePermission `json:"permissions"`                                        // 权限列表
	AllowGuest   bool              `json:"allow_guest"`                                        // 允许游客
	ExpiresIn    int               `json:"expires_in"`                                         // 过期时间（小时）
	MaxViews     int               `json:"max_views"`                                          // 最大访问次数
	IsPublic     bool              `json:"is_public"`                                          // 是否公开
}

// ShareResponse 分享响应结构
type ShareResponse struct {
	Share     Share  `json:"share"`
	ShareURL  string `json:"share_url"`
	QRCodeURL string `json:"qr_code_url"`
}

// CollaboratorInviteRequest 协作者邀请请求
type CollaboratorInviteRequest struct {
	ShareID     uint              `json:"share_id" binding:"required"`     // 分享ID
	UserEmail   string            `json:"user_email" binding:"required"`   // 用户邮箱
	Permissions []SharePermission `json:"permissions"`                     // 权限列表
	Role        string            `json:"role"`                            // 角色
	Message     string            `json:"message"`                         // 邀请消息
}

// CommentRequest 评论请求
type CommentRequest struct {
	ShareID     uint     `json:"share_id" binding:"required"`     // 分享ID
	Content     string   `json:"content" binding:"required"`      // 评论内容
	ParentID    *uint    `json:"parent_id"`                       // 父评论ID
	PositionX   *float64 `json:"position_x"`                      // X坐标
	PositionY   *float64 `json:"position_y"`                      // Y坐标
	AuthorName  string   `json:"author_name"`                     // 作者名称（游客）
	AuthorEmail string   `json:"author_email"`                    // 作者邮箱（游客）
}

// ShareStatistics 分享统计
type ShareStatistics struct {
	ShareID       uint                     `json:"share_id"`
	TotalViews    int                      `json:"total_views"`
	UniqueViews   int                      `json:"unique_views"`
	TotalDownloads int                     `json:"total_downloads"`
	TotalComments int                      `json:"total_comments"`
	TotalLikes    int                      `json:"total_likes"`
	ViewsByDate   map[string]int           `json:"views_by_date"`
	ViewsByCountry map[string]int          `json:"views_by_country"`
	TopReferrers  []map[string]interface{} `json:"top_referrers"`
	RecentAccess  []ShareAccess            `json:"recent_access"`
}

// TableName 指定表名
func (Share) TableName() string {
	return "shares"
}

func (ShareAccess) TableName() string {
	return "share_access"
}

func (ShareCollaborator) TableName() string {
	return "share_collaborators"
}

func (Comment) TableName() string {
	return "comments"
}

func (CommentLike) TableName() string {
	return "comment_likes"
}

func (ShareLike) TableName() string {
	return "share_likes"
}

// 验证方法
func (s *Share) IsExpired() bool {
	if s.ExpiresAt != nil && s.ExpiresAt.Before(time.Now()) {
		return true
	}
	if s.MaxViews > 0 && s.ViewCount >= s.MaxViews {
		return true
	}
	return false
}

func (s *Share) IsActive() bool {
	return s.Status == ShareStatusActive && !s.IsExpired()
}

func (s *Share) HasPermission(permission SharePermission) bool {
	for _, p := range s.Permissions {
		if p == permission {
			return true
		}
	}
	return false
}

func (sc *ShareCollaborator) HasPermission(permission SharePermission) bool {
	for _, p := range sc.Permissions {
		if p == permission {
			return true
		}
	}
	return false
}
