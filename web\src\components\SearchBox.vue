<template>
  <div class="search-box">
    <!-- 基础搜索 -->
    <div class="search-input-container">
      <div class="search-input-wrapper">
        <i class="bi bi-search search-icon"></i>
        <input
          ref="searchInput"
          v-model="searchQuery"
          type="text"
          class="search-input"
          :placeholder="placeholder"
          @input="onSearchInput"
          @keydown.enter="performSearch"
          @keydown.down="navigateSuggestions(1)"
          @keydown.up="navigateSuggestions(-1)"
          @keydown.escape="hideSuggestions"
          @focus="showSuggestions = true"
          @blur="onInputBlur"
        />
        <button
          v-if="searchQuery"
          class="clear-btn"
          @click="clearSearch"
          title="清除搜索"
        >
          <i class="bi bi-x"></i>
        </button>
        <button
          class="filter-btn"
          :class="{ active: showAdvancedFilters }"
          @click="toggleAdvancedFilters"
          title="高级筛选"
        >
          <i class="bi bi-funnel"></i>
          <span v-if="hasActiveFilters" class="filter-indicator"></span>
        </button>
      </div>

      <!-- 搜索建议 -->
      <div
        v-if="showSuggestions && suggestions.length > 0"
        class="suggestions-dropdown"
      >
        <div
          v-for="(suggestion, index) in suggestions"
          :key="index"
          class="suggestion-item"
          :class="{ active: selectedSuggestionIndex === index }"
          @click="selectSuggestion(suggestion)"
          @mouseenter="selectedSuggestionIndex = index"
        >
          <i class="bi bi-search suggestion-icon"></i>
          <span class="suggestion-text" v-html="highlightMatch(suggestion)"></span>
        </div>
      </div>
    </div>

    <!-- 高级筛选 -->
    <div v-if="showAdvancedFilters" class="advanced-filters">
      <div class="filters-header">
        <h4>高级筛选</h4>
        <button class="reset-filters-btn" @click="resetFilters">
          <i class="bi bi-arrow-clockwise"></i>
          重置
        </button>
      </div>

      <div class="filters-grid">
        <!-- 相册筛选 -->
        <div class="filter-group">
          <label>相册</label>
          <select v-model="filters.albumId" class="filter-select">
            <option value="">所有相册</option>
            <option value="0">未分类</option>
            <option
              v-for="album in albums"
              :key="album.id"
              :value="album.id"
            >
              {{ album.name }}
            </option>
          </select>
        </div>

        <!-- 格式筛选 -->
        <div class="filter-group">
          <label>格式</label>
          <select v-model="filters.format" class="filter-select">
            <option value="">所有格式</option>
            <option
              v-for="format in formats"
              :key="format"
              :value="format"
            >
              {{ format.toUpperCase() }}
            </option>
          </select>
        </div>

        <!-- 文件大小筛选 -->
        <div class="filter-group">
          <label>文件大小</label>
          <div class="size-filter">
            <input
              v-model="filters.minSize"
              type="number"
              placeholder="最小(KB)"
              class="size-input"
            />
            <span class="separator">-</span>
            <input
              v-model="filters.maxSize"
              type="number"
              placeholder="最大(KB)"
              class="size-input"
            />
          </div>
        </div>

        <!-- 图片尺寸筛选 -->
        <div class="filter-group">
          <label>图片尺寸</label>
          <div class="dimension-filter">
            <div class="dimension-row">
              <span class="dimension-label">宽度:</span>
              <input
                v-model="filters.minWidth"
                type="number"
                placeholder="最小"
                class="dimension-input"
              />
              <span class="separator">-</span>
              <input
                v-model="filters.maxWidth"
                type="number"
                placeholder="最大"
                class="dimension-input"
              />
            </div>
            <div class="dimension-row">
              <span class="dimension-label">高度:</span>
              <input
                v-model="filters.minHeight"
                type="number"
                placeholder="最小"
                class="dimension-input"
              />
              <span class="separator">-</span>
              <input
                v-model="filters.maxHeight"
                type="number"
                placeholder="最大"
                class="dimension-input"
              />
            </div>
          </div>
        </div>

        <!-- 日期筛选 -->
        <div class="filter-group">
          <label>上传日期</label>
          <div class="date-filter">
            <input
              v-model="filters.startDate"
              type="date"
              class="date-input"
            />
            <span class="separator">至</span>
            <input
              v-model="filters.endDate"
              type="date"
              class="date-input"
            />
          </div>
        </div>

        <!-- 排序 -->
        <div class="filter-group">
          <label>排序</label>
          <div class="sort-filter">
            <select v-model="filters.sortBy" class="sort-select">
              <option value="created_at">上传时间</option>
              <option value="name">文件名</option>
              <option value="size">文件大小</option>
              <option value="width">图片宽度</option>
              <option value="height">图片高度</option>
            </select>
            <select v-model="filters.sortOrder" class="sort-select">
              <option value="desc">降序</option>
              <option value="asc">升序</option>
            </select>
          </div>
        </div>
      </div>

      <div class="filters-actions">
        <button class="apply-filters-btn" @click="applyFilters">
          <i class="bi bi-check"></i>
          应用筛选
        </button>
      </div>
    </div>

    <!-- 活跃筛选器标签 -->
    <div v-if="activeFilterTags.length > 0" class="active-filters">
      <div class="filter-tags">
        <span
          v-for="tag in activeFilterTags"
          :key="tag.key"
          class="filter-tag"
        >
          {{ tag.label }}
          <button class="remove-tag-btn" @click="removeFilter(tag.key)">
            <i class="bi bi-x"></i>
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'

interface Props {
  placeholder?: string
  initialQuery?: string
  searchType?: 'images' | 'albums'
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索图片...',
  initialQuery: '',
  searchType: 'images'
})

const emit = defineEmits<{
  search: [query: string, filters: any]
  clear: []
}>()

// 搜索状态
const searchInput = ref<HTMLInputElement>()
const searchQuery = ref(props.initialQuery)
const showSuggestions = ref(false)
const suggestions = ref<string[]>([])
const selectedSuggestionIndex = ref(-1)
const isLoadingSuggestions = ref(false)

// 筛选器状态
const showAdvancedFilters = ref(false)
const filters = ref({
  albumId: '',
  format: '',
  minSize: '',
  maxSize: '',
  minWidth: '',
  maxWidth: '',
  minHeight: '',
  maxHeight: '',
  startDate: '',
  endDate: '',
  sortBy: 'created_at',
  sortOrder: 'desc'
})

// 筛选器选项
const albums = ref<any[]>([])
const formats = ref<string[]>([])

// 计算属性
const hasActiveFilters = computed(() => {
  return Object.values(filters.value).some(value => value !== '')
})

const activeFilterTags = computed(() => {
  const tags: Array<{ key: string; label: string }> = []
  
  if (filters.value.albumId) {
    const albumName = filters.value.albumId === '0' 
      ? '未分类' 
      : albums.value.find(a => a.id.toString() === filters.value.albumId)?.name || '未知相册'
    tags.push({ key: 'albumId', label: `相册: ${albumName}` })
  }
  
  if (filters.value.format) {
    tags.push({ key: 'format', label: `格式: ${filters.value.format.toUpperCase()}` })
  }
  
  if (filters.value.minSize || filters.value.maxSize) {
    const sizeLabel = `大小: ${filters.value.minSize || '0'}KB - ${filters.value.maxSize || '∞'}KB`
    tags.push({ key: 'size', label: sizeLabel })
  }
  
  if (filters.value.startDate || filters.value.endDate) {
    const dateLabel = `日期: ${filters.value.startDate || '开始'} - ${filters.value.endDate || '结束'}`
    tags.push({ key: 'date', label: dateLabel })
  }
  
  return tags
})

// 方法
const onSearchInput = async () => {
  if (searchQuery.value.length >= 2) {
    await loadSuggestions()
    showSuggestions.value = true
  } else {
    suggestions.value = []
    showSuggestions.value = false
  }
  selectedSuggestionIndex.value = -1
}

const loadSuggestions = async () => {
  if (isLoadingSuggestions.value) return
  
  isLoadingSuggestions.value = true
  try {
    // TODO: 调用API获取搜索建议
    // const response = await api.getSearchSuggestions(searchQuery.value)
    // suggestions.value = response.data.suggestions
    
    // 模拟数据
    suggestions.value = [
      'vacation photos',
      'family dinner',
      'birthday party',
      'nature landscape'
    ].filter(s => s.toLowerCase().includes(searchQuery.value.toLowerCase()))
  } catch (error) {
    console.error('Failed to load suggestions:', error)
  } finally {
    isLoadingSuggestions.value = false
  }
}

const navigateSuggestions = (direction: number) => {
  if (suggestions.value.length === 0) return
  
  selectedSuggestionIndex.value += direction
  
  if (selectedSuggestionIndex.value < 0) {
    selectedSuggestionIndex.value = suggestions.value.length - 1
  } else if (selectedSuggestionIndex.value >= suggestions.value.length) {
    selectedSuggestionIndex.value = 0
  }
}

const selectSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion
  hideSuggestions()
  performSearch()
}

const hideSuggestions = () => {
  showSuggestions.value = false
  selectedSuggestionIndex.value = -1
}

const onInputBlur = () => {
  // 延迟隐藏建议，以便点击建议项
  setTimeout(() => {
    hideSuggestions()
  }, 200)
}

const performSearch = () => {
  if (selectedSuggestionIndex.value >= 0) {
    selectSuggestion(suggestions.value[selectedSuggestionIndex.value])
    return
  }
  
  hideSuggestions()
  emit('search', searchQuery.value, buildSearchFilters())
}

const clearSearch = () => {
  searchQuery.value = ''
  hideSuggestions()
  emit('clear')
}

const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value
}

const applyFilters = () => {
  emit('search', searchQuery.value, buildSearchFilters())
}

const resetFilters = () => {
  filters.value = {
    albumId: '',
    format: '',
    minSize: '',
    maxSize: '',
    minWidth: '',
    maxWidth: '',
    minHeight: '',
    maxHeight: '',
    startDate: '',
    endDate: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  }
  applyFilters()
}

const removeFilter = (key: string) => {
  switch (key) {
    case 'albumId':
      filters.value.albumId = ''
      break
    case 'format':
      filters.value.format = ''
      break
    case 'size':
      filters.value.minSize = ''
      filters.value.maxSize = ''
      break
    case 'date':
      filters.value.startDate = ''
      filters.value.endDate = ''
      break
  }
  applyFilters()
}

const buildSearchFilters = () => {
  const searchFilters: any = {}
  
  Object.entries(filters.value).forEach(([key, value]) => {
    if (value !== '') {
      searchFilters[key] = value
    }
  })
  
  return searchFilters
}

const highlightMatch = (text: string) => {
  if (!searchQuery.value) return text
  
  const regex = new RegExp(`(${searchQuery.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const loadFilterOptions = async () => {
  try {
    // TODO: 调用API获取筛选器选项
    // const response = await api.getSearchFilters()
    // albums.value = response.data.albums
    // formats.value = response.data.formats
    
    // 模拟数据
    albums.value = [
      { id: 1, name: '家庭照片' },
      { id: 2, name: '旅行回忆' },
      { id: 3, name: '工作相关' }
    ]
    formats.value = ['jpg', 'png', 'gif', 'webp']
  } catch (error) {
    console.error('Failed to load filter options:', error)
  }
}

// 生命周期
onMounted(() => {
  loadFilterOptions()
})

// 监听搜索查询变化
watch(() => props.initialQuery, (newQuery) => {
  searchQuery.value = newQuery
})
</script>

<style scoped>
.search-box {
  width: 100%;
}

.search-input-container {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #6c757d;
  font-size: 1.1rem;
  z-index: 2;
}

.search-input {
  flex: 1;
  padding: 12px 16px 12px 40px;
  border: none;
  outline: none;
  font-size: 1rem;
  background: transparent;
}

.clear-btn,
.filter-btn {
  position: relative;
  padding: 8px;
  border: none;
  background: none;
  color: #6c757d;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  margin-right: 4px;
}

.clear-btn:hover,
.filter-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.filter-btn.active {
  background: #007bff;
  color: white;
}

.filter-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 6px;
  height: 6px;
  background: #dc3545;
  border-radius: 50%;
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover,
.suggestion-item.active {
  background: #f8f9fa;
}

.suggestion-icon {
  margin-right: 8px;
  color: #6c757d;
  font-size: 0.9rem;
}

.suggestion-text {
  flex: 1;
}

.suggestion-text :deep(mark) {
  background: #fff3cd;
  padding: 0 2px;
  border-radius: 2px;
}

.advanced-filters {
  margin-top: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filters-header h4 {
  margin: 0;
  color: #495057;
  font-size: 1.1rem;
}

.reset-filters-btn {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  background: white;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.reset-filters-btn:hover {
  background: #e9ecef;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  margin-bottom: 6px;
  font-weight: 500;
  color: #495057;
  font-size: 0.875rem;
}

.filter-select,
.size-input,
.dimension-input,
.date-input,
.sort-select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.filter-select:focus,
.size-input:focus,
.dimension-input:focus,
.date-input:focus,
.sort-select:focus {
  outline: none;
  border-color: #007bff;
}

.size-filter,
.sort-filter {
  display: flex;
  gap: 8px;
  align-items: center;
}

.dimension-filter {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dimension-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dimension-label {
  min-width: 40px;
  font-size: 0.8rem;
  color: #6c757d;
}

.dimension-input {
  flex: 1;
}

.date-filter {
  display: flex;
  gap: 8px;
  align-items: center;
}

.separator {
  color: #6c757d;
  font-size: 0.875rem;
}

.filters-actions {
  margin-top: 20px;
  text-align: right;
}

.apply-filters-btn {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.apply-filters-btn:hover {
  background: #0056b3;
}

.active-filters {
  margin-top: 12px;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 16px;
  font-size: 0.8rem;
  gap: 4px;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  font-size: 0.7rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.remove-tag-btn:hover {
  opacity: 1;
}
</style>
