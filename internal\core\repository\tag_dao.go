package dao

import (
	"fmt"
	"strings"

	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
	"cloudbed/pkg/pagination"

	"gorm.io/gorm"
)

// CreateTag 创建标签
func CreateTag(tag *models.Tag) error {
	// 标准化标签名称
	tag.Name = models.NormalizeTagName(tag.Name)

	// 验证标签名称
	if err := models.ValidateTagName(tag.Name); err != nil {
		return err
	}

	// 验证颜色
	if err := models.ValidateTagColor(tag.Color); err != nil {
		return err
	}

	// 设置默认颜色
	if tag.Color == "" {
		tag.Color = models.GetRandomColor()
	}

	return database.DB.Create(tag).Error
}

// GetTagByID 根据ID获取标签
func GetTagByID(id uint) (*models.Tag, error) {
	var tag models.Tag
	err := database.DB.First(&tag, id).Error
	if err != nil {
		return nil, err
	}
	return &tag, nil
}

// GetTagByName 根据名称获取用户的标签
func GetTagByName(userID uint, name string) (*models.Tag, error) {
	var tag models.Tag
	name = models.NormalizeTagName(name)
	err := database.DB.Where("user_id = ? AND name = ?", userID, name).First(&tag).Error
	if err != nil {
		return nil, err
	}
	return &tag, nil
}

// GetTagsByUserID 获取用户的所有标签
func GetTagsByUserID(userID uint) ([]models.Tag, error) {
	var tags []models.Tag
	err := database.DB.Where("user_id = ?", userID).
		Order("usage_count DESC, name ASC").
		Find(&tags).Error
	return tags, err
}

// GetTagsByUserIDPaginated 分页获取用户标签
func GetTagsByUserIDPaginated(userID uint, req pagination.TagPaginationRequest) (pagination.Response, error) {
	var tags []models.Tag

	query := database.DB.Model(&models.Tag{}).Where("user_id = ?", userID)

	// 搜索过滤
	if req.Search != "" {
		query = query.Where("name LIKE ?", "%"+req.Search+"%")
	}

	// 使用分页工具进行查询
	return pagination.Paginate(query, req.Request, &tags, "usage_count DESC, name ASC")
}

// UpdateTag 更新标签
func UpdateTag(tag *models.Tag) error {
	// 标准化标签名称
	tag.Name = models.NormalizeTagName(tag.Name)

	// 验证标签名称
	if err := models.ValidateTagName(tag.Name); err != nil {
		return err
	}

	// 验证颜色
	if err := models.ValidateTagColor(tag.Color); err != nil {
		return err
	}

	return database.DB.Save(tag).Error
}

// DeleteTag 删除标签
func DeleteTag(id uint) error {
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 删除图片标签关联
		if err := tx.Where("tag_id = ?", id).Delete(&models.ImageTag{}).Error; err != nil {
			return err
		}

		// 删除标签分类关联
		if err := tx.Where("tag_id = ?", id).Delete(&models.TagCategoryRelation{}).Error; err != nil {
			return err
		}

		// 删除标签
		if err := tx.Delete(&models.Tag{}, id).Error; err != nil {
			return err
		}

		return nil
	})
}

// GetTagsWithStats 获取带统计信息的标签
func GetTagsWithStats(userID uint) ([]models.TagWithStats, error) {
	var results []models.TagWithStats

	err := database.DB.Table("tags").
		Select("tags.*, COALESCE(tag_stats.image_count, 0) as image_count, tag_stats.last_used").
		Joins("LEFT JOIN (SELECT tag_id, COUNT(*) as image_count, MAX(created_at) as last_used FROM image_tags GROUP BY tag_id) as tag_stats ON tags.id = tag_stats.tag_id").
		Where("tags.user_id = ?", userID).
		Order("tags.usage_count DESC, tags.name ASC").
		Scan(&results).Error

	return results, err
}

// GetPopularTags 获取热门标签
func GetPopularTags(userID uint, limit int) ([]models.PopularTag, error) {
	var results []models.PopularTag

	err := database.DB.Table("tags").
		Select("tags.id, tags.name, tags.color, tags.usage_count, COALESCE(tag_stats.image_count, 0) as image_count").
		Joins("LEFT JOIN (SELECT tag_id, COUNT(*) as image_count FROM image_tags GROUP BY tag_id) as tag_stats ON tags.id = tag_stats.tag_id").
		Where("tags.user_id = ?", userID).
		Order("tags.usage_count DESC, tag_stats.image_count DESC").
		Limit(limit).
		Scan(&results).Error

	return results, err
}

// AddTagToImage 为图片添加标签
func AddTagToImage(imageID, tagID, userID uint) error {
	// 检查关联是否已存在
	var count int64
	database.DB.Model(&models.ImageTag{}).
		Where("image_id = ? AND tag_id = ?", imageID, tagID).
		Count(&count)

	if count > 0 {
		return fmt.Errorf("标签已存在")
	}

	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 创建关联
		imageTag := &models.ImageTag{
			ImageID: imageID,
			TagID:   tagID,
			UserID:  userID,
		}
		if err := tx.Create(imageTag).Error; err != nil {
			return err
		}

		// 更新标签使用次数
		if err := tx.Model(&models.Tag{}).Where("id = ?", tagID).
			UpdateColumn("usage_count", gorm.Expr("usage_count + 1")).Error; err != nil {
			return err
		}

		return nil
	})
}

// RemoveTagFromImage 从图片移除标签
func RemoveTagFromImage(imageID, tagID uint) error {
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 删除关联
		result := tx.Where("image_id = ? AND tag_id = ?", imageID, tagID).
			Delete(&models.ImageTag{})
		if result.Error != nil {
			return result.Error
		}

		// 如果确实删除了关联，更新标签使用次数
		if result.RowsAffected > 0 {
			if err := tx.Model(&models.Tag{}).Where("id = ?", tagID).
				UpdateColumn("usage_count", gorm.Expr("GREATEST(usage_count - 1, 0)")).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// GetImageTags 获取图片的标签
func GetImageTags(imageID uint) ([]models.Tag, error) {
	var tags []models.Tag
	err := database.DB.Table("tags").
		Joins("JOIN image_tags ON tags.id = image_tags.tag_id").
		Where("image_tags.image_id = ?", imageID).
		Order("tags.name ASC").
		Find(&tags).Error
	return tags, err
}

// GetTagImages 获取标签下的图片
func GetTagImages(tagID uint, req pagination.Request) (pagination.Response, error) {
	var images []models.Image

	query := database.DB.Table("images").
		Joins("JOIN image_tags ON images.id = image_tags.image_id").
		Where("image_tags.tag_id = ?", tagID)

	// 使用分页工具进行查询
	return pagination.Paginate(query, req, &images, "images.created_at DESC")
}

// BatchAddTagsToImages 批量为图片添加标签
func BatchAddTagsToImages(imageIDs []uint, tagIDs []uint, userID uint) error {
	if len(imageIDs) == 0 || len(tagIDs) == 0 {
		return fmt.Errorf("图片ID或标签ID不能为空")
	}

	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 获取现有关联
		var existingRelations []models.ImageTag
		tx.Where("image_id IN ? AND tag_id IN ?", imageIDs, tagIDs).
			Find(&existingRelations)

		// 创建关联映射
		existingMap := make(map[string]bool)
		for _, rel := range existingRelations {
			key := fmt.Sprintf("%d-%d", rel.ImageID, rel.TagID)
			existingMap[key] = true
		}

		// 批量创建新关联
		var newRelations []models.ImageTag
		tagUsageCount := make(map[uint]int)

		for _, imageID := range imageIDs {
			for _, tagID := range tagIDs {
				key := fmt.Sprintf("%d-%d", imageID, tagID)
				if !existingMap[key] {
					newRelations = append(newRelations, models.ImageTag{
						ImageID: imageID,
						TagID:   tagID,
						UserID:  userID,
					})
					tagUsageCount[tagID]++
				}
			}
		}

		// 批量插入新关联
		if len(newRelations) > 0 {
			if err := tx.CreateInBatches(newRelations, 100).Error; err != nil {
				return err
			}
		}

		// 更新标签使用次数
		for tagID, count := range tagUsageCount {
			if err := tx.Model(&models.Tag{}).Where("id = ?", tagID).
				UpdateColumn("usage_count", gorm.Expr("usage_count + ?", count)).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// BatchRemoveTagsFromImages 批量从图片移除标签
func BatchRemoveTagsFromImages(imageIDs []uint, tagIDs []uint) error {
	if len(imageIDs) == 0 || len(tagIDs) == 0 {
		return fmt.Errorf("图片ID或标签ID不能为空")
	}

	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 统计要删除的关联数量
		var deleteCounts []struct {
			TagID uint
			Count int64
		}

		tx.Model(&models.ImageTag{}).
			Select("tag_id, COUNT(*) as count").
			Where("image_id IN ? AND tag_id IN ?", imageIDs, tagIDs).
			Group("tag_id").
			Scan(&deleteCounts)

		// 删除关联
		if err := tx.Where("image_id IN ? AND tag_id IN ?", imageIDs, tagIDs).
			Delete(&models.ImageTag{}).Error; err != nil {
			return err
		}

		// 更新标签使用次数
		for _, item := range deleteCounts {
			if err := tx.Model(&models.Tag{}).Where("id = ?", item.TagID).
				UpdateColumn("usage_count", gorm.Expr("GREATEST(usage_count - ?, 0)", item.Count)).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// SearchTags 搜索标签
func SearchTags(userID uint, query string, limit int) ([]models.Tag, error) {
	var tags []models.Tag

	searchQuery := "%" + strings.ToLower(query) + "%"
	err := database.DB.Where("user_id = ? AND LOWER(name) LIKE ?", userID, searchQuery).
		Order("usage_count DESC, name ASC").
		Limit(limit).
		Find(&tags).Error

	return tags, err
}

// GetOrCreateTag 获取或创建标签
func GetOrCreateTag(userID uint, name, color string) (*models.Tag, error) {
	// 标准化名称
	name = models.NormalizeTagName(name)

	// 先尝试获取现有标签
	tag, err := GetTagByName(userID, name)
	if err == nil {
		return tag, nil
	}

	// 如果不存在，创建新标签
	if err == gorm.ErrRecordNotFound {
		newTag := &models.Tag{
			Name:   name,
			Color:  color,
			UserID: userID,
		}

		if newTag.Color == "" {
			newTag.Color = models.GetRandomColor()
		}

		if err := CreateTag(newTag); err != nil {
			return nil, err
		}

		return newTag, nil
	}

	return nil, err
}
