<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <div class="logo">
          <i class="bi bi-images" style="font-size: 2rem; color: #007bff;"></i>
        </div>
        <h2>{{ isLogin ? '用户登录' : '用户注册' }}</h2>
        <p class="subtitle">{{ isLogin ? '登录您的账户以继续' : '创建新账户以开始使用' }}</p>
      </div>
      
      <div class="auth-tabs">
        <button 
          :class="{ active: isLogin }" 
          @click="isLogin = true"
        >
          登录
        </button>
        <button 
          :class="{ active: !isLogin }" 
          @click="isLogin = false"
        >
          注册
        </button>
      </div>
      
      <form @submit.prevent="handleSubmit" class="auth-form">
        <div class="form-group" v-if="!isLogin">
          <label for="username">用户名</label>
          <div class="input-group">
            <span class="input-icon"><i class="bi bi-person"></i></span>
            <input
              id="username"
              v-model="form.username"
              type="text"
              required
              :disabled="loading"
              placeholder="请输入用户名"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="email">邮箱</label>
          <div class="input-group">
            <span class="input-icon"><i class="bi bi-envelope"></i></span>
            <input
              id="email"
              v-model="form.email"
              type="email"
              required
              :disabled="loading"
              placeholder="请输入邮箱地址"
            />
          </div>
        </div>
        
        <div class="form-group">
          <label for="password">密码</label>
          <div class="input-group">
            <span class="input-icon"><i class="bi bi-lock"></i></span>
            <input 
              id="password" 
              v-model="form.password" 
              type="password" 
              required 
              :disabled="loading"
              placeholder="请输入密码"
            />
          </div>
        </div>
        
        <div class="form-group" v-if="!isLogin">
          <label for="confirmPassword">确认密码</label>
          <div class="input-group">
            <span class="input-icon"><i class="bi bi-lock-fill"></i></span>
            <input 
              id="confirmPassword" 
              v-model="form.confirmPassword" 
              type="password" 
              required 
              :disabled="loading"
              placeholder="请再次输入密码"
            />
          </div>
        </div>
        
        <button 
          type="submit" 
          class="submit-btn" 
          :disabled="loading"
        >
          <span v-if="loading" class="spinner"><i class="bi bi-arrow-repeat spin"></i></span>
          {{ loading ? (isLogin ? '登录中...' : '注册中...') : (isLogin ? '登录' : '注册') }}
        </button>
        
        <div v-if="error" class="error-message">
          <i class="bi bi-exclamation-circle"></i> {{ error }}
        </div>
      </form>
    </div>
    
    <div class="auth-footer">
      <p>© 2025 图床应用. 保留所有权利.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';

const authStore = useAuthStore();
const router = useRouter();

const isLogin = ref(true);
const loading = ref(false);
const error = ref('');

const form = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
});

const handleSubmit = async () => {
  error.value = '';
  
  if (!isLogin.value && form.password !== form.confirmPassword) {
    error.value = '两次输入的密码不一致';
    return;
  }
  
  loading.value = true;
  
  try {
    if (isLogin.value) {
      // 登录
      const result = await authStore.login(form.email, form.password);
      if (result.success) {
        router.push('/dashboard');
      } else {
        error.value = result.message;
      }
    } else {
      // 注册
      const result = await authStore.register(form.username, form.email, form.password);
      if (result.success) {
        error.value = result.message;
        isLogin.value = true;
      } else {
        error.value = result.message;
      }
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.auth-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* 深色主题下的认证容器背景 */
body.dark-theme .auth-container {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.auth-card {
  width: 100%;
  max-width: 400px;
  background: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 40px;
  margin-bottom: 20px;
  border: 1px solid var(--border);
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h2 {
  margin: 15px 0 10px 0;
  color: var(--text-color);
  font-weight: 600;
}

.subtitle {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

.auth-tabs {
  display: flex;
  margin-bottom: 30px;
  border-bottom: 1px solid var(--border);
}

.auth-tabs button {
  flex: 1;
  padding: 12px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 16px;
  color: var(--text-secondary);
  outline: none;
  transition: all 0.3s;
  font-weight: 500;
}

.auth-tabs button.active {
  color: #007bff;
  border-bottom: 2px solid #007bff;
}

body.dark-theme .auth-tabs button.active {
  color: #66b3ff;
  border-bottom: 2px solid #66b3ff;
}

.auth-form {
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.9rem;
}

.input-group {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  z-index: 1;
}

.form-group input {
  width: 100%;
  padding: 12px 12px 12px 45px;
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 16px;
  box-sizing: border-box;
  transition: border-color 0.3s, box-shadow 0.3s;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.form-group input:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

body.dark-theme .form-group input:focus {
  border-color: #66b3ff;
  box-shadow: 0 0 0 3px rgba(102, 179, 255, 0.1);
}

.form-group input:disabled {
  background-color: var(--header-bg);
  cursor: not-allowed;
}

.submit-btn {
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

body.dark-theme .submit-btn {
  background: linear-gradient(135deg, #3a57b0 0%, #4a2f8c 100%);
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.submit-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spinner {
  margin-right: 8px;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  margin-top: 20px;
  padding: 12px 15px;
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  text-align: center;
  font-size: 0.9rem;
}

body.dark-theme .error-message {
  background-color: #721c24;
  color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.error-message i {
  margin-right: 5px;
}

.auth-footer {
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  font-size: 0.8rem;
}

body.dark-theme .auth-footer {
  color: rgba(255, 255, 255, 0.5);
}

@media (max-width: 480px) {
  .auth-card {
    padding: 30px 20px;
  }
}
</style>