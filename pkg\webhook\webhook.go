package webhook

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// WebhookEvent Webhook事件类型
type WebhookEvent string

const (
	EventUserRegistered    WebhookEvent = "user.registered"
	EventUserUpdated       WebhookEvent = "user.updated"
	EventUserDeleted       WebhookEvent = "user.deleted"
	EventImageUploaded     WebhookEvent = "image.uploaded"
	EventImageUpdated      WebhookEvent = "image.updated"
	EventImageDeleted      WebhookEvent = "image.deleted"
	EventAlbumCreated      WebhookEvent = "album.created"
	EventAlbumUpdated      WebhookEvent = "album.updated"
	EventAlbumDeleted      WebhookEvent = "album.deleted"
	EventShareCreated      WebhookEvent = "share.created"
	EventShareAccessed     WebhookEvent = "share.accessed"
	EventShareExpired      WebhookEvent = "share.expired"
	EventCommentCreated    WebhookEvent = "comment.created"
	EventBackupCompleted   WebhookEvent = "backup.completed"
	EventBackupFailed      WebhookEvent = "backup.failed"
	EventStorageQuotaExceeded WebhookEvent = "storage.quota_exceeded"
)

// WebhookPayload Webhook载荷
type WebhookPayload struct {
	ID        string                 `json:"id"`
	Event     WebhookEvent           `json:"event"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
	UserID    uint                   `json:"user_id,omitempty"`
	Signature string                 `json:"signature,omitempty"`
	Version   string                 `json:"version"`
}

// WebhookEndpoint Webhook端点
type WebhookEndpoint struct {
	ID          uint         `json:"id" gorm:"primary_key"`
	UserID      uint         `json:"user_id" gorm:"not null"`
	Name        string       `json:"name" gorm:"type:varchar(100);not null"`
	URL         string       `json:"url" gorm:"type:varchar(500);not null"`
	Secret      string       `json:"secret" gorm:"type:varchar(100);not null"`
	Events      []string     `json:"events" gorm:"type:json"`
	Active      bool         `json:"active" gorm:"default:true"`
	Headers     []WebhookHeader `json:"headers" gorm:"type:json"`
	Timeout     int          `json:"timeout" gorm:"default:30"`
	RetryCount  int          `json:"retry_count" gorm:"default:3"`
	RetryDelay  int          `json:"retry_delay" gorm:"default:60"`
	LastSuccess *time.Time   `json:"last_success"`
	LastError   string       `json:"last_error" gorm:"type:text"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
}

// WebhookHeader 自定义头部
type WebhookHeader struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

// WebhookDelivery Webhook投递记录
type WebhookDelivery struct {
	ID           uint         `json:"id" gorm:"primary_key"`
	EndpointID   uint         `json:"endpoint_id" gorm:"not null"`
	PayloadID    string       `json:"payload_id" gorm:"type:varchar(100);not null"`
	Event        WebhookEvent `json:"event" gorm:"type:varchar(50);not null"`
	URL          string       `json:"url" gorm:"type:varchar(500);not null"`
	StatusCode   int          `json:"status_code"`
	ResponseBody string       `json:"response_body" gorm:"type:text"`
	ErrorMessage string       `json:"error_message" gorm:"type:text"`
	Duration     int64        `json:"duration"` // 毫秒
	Attempts     int          `json:"attempts" gorm:"default:1"`
	Success      bool         `json:"success" gorm:"default:false"`
	CreatedAt    time.Time    `json:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at"`
}

// WebhookManager Webhook管理器
type WebhookManager struct {
	endpoints map[uint]*WebhookEndpoint
	client    *http.Client
	queue     chan *WebhookTask
	workers   int
	running   bool
	stopChan  chan struct{}
}

// WebhookTask Webhook任务
type WebhookTask struct {
	Endpoint *WebhookEndpoint
	Payload  *WebhookPayload
	Attempt  int
}

// WebhookStats Webhook统计
type WebhookStats struct {
	TotalDeliveries    int64   `json:"total_deliveries"`
	SuccessfulDeliveries int64 `json:"successful_deliveries"`
	FailedDeliveries   int64   `json:"failed_deliveries"`
	SuccessRate        float64 `json:"success_rate"`
	AvgResponseTime    int64   `json:"avg_response_time"`
	LastDelivery       *time.Time `json:"last_delivery"`
}

// NewWebhookManager 创建Webhook管理器
func NewWebhookManager(workers int) *WebhookManager {
	return &WebhookManager{
		endpoints: make(map[uint]*WebhookEndpoint),
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		queue:    make(chan *WebhookTask, 1000),
		workers:  workers,
		stopChan: make(chan struct{}),
	}
}

// Start 启动Webhook管理器
func (wm *WebhookManager) Start() {
	if wm.running {
		return
	}
	
	wm.running = true
	
	// 启动工作协程
	for i := 0; i < wm.workers; i++ {
		go wm.worker()
	}
	
	// 启动重试协程
	go wm.retryWorker()
}

// Stop 停止Webhook管理器
func (wm *WebhookManager) Stop() {
	if !wm.running {
		return
	}
	
	wm.running = false
	close(wm.stopChan)
}

// RegisterEndpoint 注册Webhook端点
func (wm *WebhookManager) RegisterEndpoint(endpoint *WebhookEndpoint) {
	wm.endpoints[endpoint.ID] = endpoint
}

// UnregisterEndpoint 注销Webhook端点
func (wm *WebhookManager) UnregisterEndpoint(endpointID uint) {
	delete(wm.endpoints, endpointID)
}

// TriggerEvent 触发事件
func (wm *WebhookManager) TriggerEvent(event WebhookEvent, data map[string]interface{}, userID uint) {
	payload := &WebhookPayload{
		ID:        generatePayloadID(),
		Event:     event,
		Timestamp: time.Now(),
		Data:      data,
		UserID:    userID,
		Version:   "1.0",
	}
	
	// 查找匹配的端点
	for _, endpoint := range wm.endpoints {
		if !endpoint.Active {
			continue
		}
		
		// 检查用户权限
		if endpoint.UserID != userID && userID != 0 {
			continue
		}
		
		// 检查事件过滤
		if !wm.isEventMatched(endpoint, event) {
			continue
		}
		
		// 添加到队列
		task := &WebhookTask{
			Endpoint: endpoint,
			Payload:  payload,
			Attempt:  1,
		}
		
		select {
		case wm.queue <- task:
		default:
			// 队列满了，记录错误
			fmt.Printf("Webhook queue is full, dropping task for endpoint %d\n", endpoint.ID)
		}
	}
}

// worker 工作协程
func (wm *WebhookManager) worker() {
	for wm.running {
		select {
		case <-wm.stopChan:
			return
		case task := <-wm.queue:
			wm.deliverWebhook(task)
		}
	}
}

// retryWorker 重试工作协程
func (wm *WebhookManager) retryWorker() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	
	for wm.running {
		select {
		case <-wm.stopChan:
			return
		case <-ticker.C:
			wm.processRetries()
		}
	}
}

// deliverWebhook 投递Webhook
func (wm *WebhookManager) deliverWebhook(task *WebhookTask) {
	start := time.Now()
	
	// 生成签名
	task.Payload.Signature = wm.generateSignature(task.Payload, task.Endpoint.Secret)
	
	// 序列化载荷
	payloadBytes, err := json.Marshal(task.Payload)
	if err != nil {
		wm.recordDelivery(task, 0, "", fmt.Sprintf("Failed to marshal payload: %v", err), time.Since(start), false)
		return
	}
	
	// 创建请求
	req, err := http.NewRequest("POST", task.Endpoint.URL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		wm.recordDelivery(task, 0, "", fmt.Sprintf("Failed to create request: %v", err), time.Since(start), false)
		return
	}
	
	// 设置头部
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "ImageBackup-Webhook/1.0")
	req.Header.Set("X-Webhook-Event", string(task.Payload.Event))
	req.Header.Set("X-Webhook-ID", task.Payload.ID)
	req.Header.Set("X-Webhook-Signature", task.Payload.Signature)
	req.Header.Set("X-Webhook-Timestamp", fmt.Sprintf("%d", task.Payload.Timestamp.Unix()))
	
	// 添加自定义头部
	for _, header := range task.Endpoint.Headers {
		req.Header.Set(header.Name, header.Value)
	}
	
	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(task.Endpoint.Timeout)*time.Second)
	defer cancel()
	req = req.WithContext(ctx)
	
	// 发送请求
	resp, err := wm.client.Do(req)
	if err != nil {
		wm.recordDelivery(task, 0, "", fmt.Sprintf("Request failed: %v", err), time.Since(start), false)
		wm.scheduleRetry(task)
		return
	}
	defer resp.Body.Close()
	
	// 读取响应
	responseBody := make([]byte, 1024) // 限制响应大小
	n, _ := resp.Body.Read(responseBody)
	responseBodyStr := string(responseBody[:n])
	
	// 判断是否成功
	success := resp.StatusCode >= 200 && resp.StatusCode < 300
	duration := time.Since(start)
	
	// 记录投递结果
	wm.recordDelivery(task, resp.StatusCode, responseBodyStr, "", duration, success)
	
	// 更新端点状态
	if success {
		now := time.Now()
		task.Endpoint.LastSuccess = &now
		task.Endpoint.LastError = ""
	} else {
		task.Endpoint.LastError = fmt.Sprintf("HTTP %d: %s", resp.StatusCode, responseBodyStr)
		wm.scheduleRetry(task)
	}
}

// scheduleRetry 安排重试
func (wm *WebhookManager) scheduleRetry(task *WebhookTask) {
	if task.Attempt >= task.Endpoint.RetryCount {
		return
	}
	
	// 计算延迟时间（指数退避）
	delay := time.Duration(task.Endpoint.RetryDelay) * time.Second * time.Duration(task.Attempt)
	
	go func() {
		time.Sleep(delay)
		
		retryTask := &WebhookTask{
			Endpoint: task.Endpoint,
			Payload:  task.Payload,
			Attempt:  task.Attempt + 1,
		}
		
		select {
		case wm.queue <- retryTask:
		default:
			// 队列满了，放弃重试
		}
	}()
}

// processRetries 处理重试
func (wm *WebhookManager) processRetries() {
	// TODO: 从数据库加载失败的投递记录并重试
}

// generateSignature 生成签名
func (wm *WebhookManager) generateSignature(payload *WebhookPayload, secret string) string {
	payloadBytes, _ := json.Marshal(payload)
	
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(payloadBytes)
	
	return "sha256=" + hex.EncodeToString(h.Sum(nil))
}

// VerifySignature 验证签名
func (wm *WebhookManager) VerifySignature(payload []byte, signature, secret string) bool {
	expectedSignature := wm.generateSignatureFromBytes(payload, secret)
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// generateSignatureFromBytes 从字节生成签名
func (wm *WebhookManager) generateSignatureFromBytes(payload []byte, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(payload)
	
	return "sha256=" + hex.EncodeToString(h.Sum(nil))
}

// recordDelivery 记录投递结果
func (wm *WebhookManager) recordDelivery(task *WebhookTask, statusCode int, responseBody, errorMessage string, duration time.Duration, success bool) {
	delivery := &WebhookDelivery{
		EndpointID:   task.Endpoint.ID,
		PayloadID:    task.Payload.ID,
		Event:        task.Payload.Event,
		URL:          task.Endpoint.URL,
		StatusCode:   statusCode,
		ResponseBody: responseBody,
		ErrorMessage: errorMessage,
		Duration:     duration.Milliseconds(),
		Attempts:     task.Attempt,
		Success:      success,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	
	// TODO: 保存到数据库
	fmt.Printf("Webhook delivery: %+v\n", delivery)
}

// isEventMatched 检查事件是否匹配
func (wm *WebhookManager) isEventMatched(endpoint *WebhookEndpoint, event WebhookEvent) bool {
	if len(endpoint.Events) == 0 {
		return true // 没有过滤器，接收所有事件
	}
	
	for _, e := range endpoint.Events {
		if e == string(event) || e == "*" {
			return true
		}
		
		// 支持通配符匹配
		if wm.matchWildcard(e, string(event)) {
			return true
		}
	}
	
	return false
}

// matchWildcard 通配符匹配
func (wm *WebhookManager) matchWildcard(pattern, str string) bool {
	// 简单的通配符实现
	if pattern == "*" {
		return true
	}
	
	if len(pattern) > 0 && pattern[len(pattern)-1] == '*' {
		prefix := pattern[:len(pattern)-1]
		return len(str) >= len(prefix) && str[:len(prefix)] == prefix
	}
	
	return pattern == str
}

// GetStats 获取统计信息
func (wm *WebhookManager) GetStats(endpointID uint) (*WebhookStats, error) {
	// TODO: 从数据库查询统计信息
	return &WebhookStats{
		TotalDeliveries:      100,
		SuccessfulDeliveries: 95,
		FailedDeliveries:     5,
		SuccessRate:          0.95,
		AvgResponseTime:      250,
		LastDelivery:         &time.Time{},
	}, nil
}

// 辅助函数
func generatePayloadID() string {
	return fmt.Sprintf("wh_%d", time.Now().UnixNano())
}

// TableName 指定表名
func (WebhookEndpoint) TableName() string {
	return "webhook_endpoints"
}

func (WebhookDelivery) TableName() string {
	return "webhook_deliveries"
}
