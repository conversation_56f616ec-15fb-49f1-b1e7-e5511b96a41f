package dao

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"

	"gorm.io/gorm"
)

// CreateShare 创建分享
func CreateShare(share *models.Share) error {
	// 生成分享令牌
	token, err := generateShareToken()
	if err != nil {
		return err
	}
	share.ShareToken = token

	return database.DB.Create(share).Error
}

// GetShareByToken 根据令牌获取分享
func GetShareByToken(token string) (*models.Share, error) {
	var share models.Share
	err := database.DB.Preload("Owner").
		Where("share_token = ? AND status = ?", token, models.ShareStatusActive).
		First(&share).Error
	return &share, err
}

// GetShareByID 根据ID获取分享
func GetShareByID(id uint) (*models.Share, error) {
	var share models.Share
	err := database.DB.Preload("Owner").First(&share, id).Error
	return &share, err
}

// GetSharesByOwner 获取用户的分享列表
func GetSharesByOwner(ownerID uint, page, pageSize int) ([]models.Share, int64, error) {
	var shares []models.Share
	var total int64

	offset := (page - 1) * pageSize

	// 计算总数
	database.DB.Model(&models.Share{}).Where("owner_id = ?", ownerID).Count(&total)

	// 获取分页数据
	err := database.DB.Where("owner_id = ?", ownerID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&shares).Error

	return shares, total, err
}

// UpdateShare 更新分享
func UpdateShare(share *models.Share) error {
	return database.DB.Save(share).Error
}

// DeleteShare 删除分享
func DeleteShare(id uint) error {
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 删除相关的访问记录
		if err := tx.Where("share_id = ?", id).Delete(&models.ShareAccess{}).Error; err != nil {
			return err
		}

		// 删除相关的协作者
		if err := tx.Where("share_id = ?", id).Delete(&models.ShareCollaborator{}).Error; err != nil {
			return err
		}

		// 删除相关的评论
		if err := tx.Where("share_id = ?", id).Delete(&models.Comment{}).Error; err != nil {
			return err
		}

		// 删除相关的点赞
		if err := tx.Where("share_id = ?", id).Delete(&models.ShareLike{}).Error; err != nil {
			return err
		}

		// 删除分享记录
		return tx.Delete(&models.Share{}, id).Error
	})
}

// RecordShareAccess 记录分享访问
func RecordShareAccess(access *models.ShareAccess) error {
	return database.DB.Create(access).Error
}

// GetShareAccessHistory 获取分享访问历史
func GetShareAccessHistory(shareID uint, page, pageSize int) ([]models.ShareAccess, int64, error) {
	var accesses []models.ShareAccess
	var total int64

	offset := (page - 1) * pageSize

	// 计算总数
	database.DB.Model(&models.ShareAccess{}).Where("share_id = ?", shareID).Count(&total)

	// 获取分页数据
	err := database.DB.Preload("User").
		Where("share_id = ?", shareID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&accesses).Error

	return accesses, total, err
}

// CreateShareCollaborator 创建分享协作者
func CreateShareCollaborator(collaborator *models.ShareCollaborator) error {
	// 生成邀请令牌
	token, err := generateInviteToken()
	if err != nil {
		return err
	}
	collaborator.InviteToken = token

	return database.DB.Create(collaborator).Error
}

// GetShareCollaborators 获取分享协作者列表
func GetShareCollaborators(shareID uint) ([]models.ShareCollaborator, error) {
	var collaborators []models.ShareCollaborator
	err := database.DB.Preload("User").
		Preload("Inviter").
		Where("share_id = ?", shareID).
		Find(&collaborators).Error
	return collaborators, err
}

// GetCollaboratorByToken 根据邀请令牌获取协作者
func GetCollaboratorByToken(token string) (*models.ShareCollaborator, error) {
	var collaborator models.ShareCollaborator
	err := database.DB.Preload("Share").
		Preload("User").
		Where("invite_token = ?", token).
		First(&collaborator).Error
	return &collaborator, err
}

// UpdateShareCollaborator 更新分享协作者
func UpdateShareCollaborator(collaborator *models.ShareCollaborator) error {
	return database.DB.Save(collaborator).Error
}

// DeleteShareCollaborator 删除分享协作者
func DeleteShareCollaborator(id uint) error {
	return database.DB.Delete(&models.ShareCollaborator{}, id).Error
}

// CreateComment 创建评论
func CreateComment(comment *models.Comment) error {
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 创建评论
		if err := tx.Create(comment).Error; err != nil {
			return err
		}

		// 更新分享的评论数量
		return tx.Model(&models.Share{}).
			Where("id = ?", comment.ShareID).
			UpdateColumn("comment_count", gorm.Expr("comment_count + 1")).Error
	})
}

// GetCommentsByShare 获取分享的评论列表
func GetCommentsByShare(shareID uint, page, pageSize int) ([]models.Comment, int64, error) {
	var comments []models.Comment
	var total int64

	offset := (page - 1) * pageSize

	// 计算总数（只计算顶级评论）
	database.DB.Model(&models.Comment{}).
		Where("share_id = ? AND parent_id IS NULL AND status = ?", shareID, "active").
		Count(&total)

	// 获取分页数据（包含回复）
	err := database.DB.Preload("User").
		Preload("Replies", func(db *gorm.DB) *gorm.DB {
			return db.Preload("User").Where("status = ?", "active").Order("created_at ASC")
		}).
		Where("share_id = ? AND parent_id IS NULL AND status = ?", shareID, "active").
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&comments).Error

	return comments, total, err
}

// UpdateComment 更新评论
func UpdateComment(comment *models.Comment) error {
	return database.DB.Save(comment).Error
}

// DeleteComment 删除评论
func DeleteComment(id uint) error {
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 获取评论信息
		var comment models.Comment
		if err := tx.First(&comment, id).Error; err != nil {
			return err
		}

		// 软删除评论（标记为已删除）
		if err := tx.Model(&comment).Update("status", "deleted").Error; err != nil {
			return err
		}

		// 更新分享的评论数量
		return tx.Model(&models.Share{}).
			Where("id = ?", comment.ShareID).
			UpdateColumn("comment_count", gorm.Expr("comment_count - 1")).Error
	})
}

// CreateCommentLike 创建评论点赞
func CreateCommentLike(like *models.CommentLike) error {
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 检查是否已经点赞
		var existingLike models.CommentLike
		query := tx.Where("comment_id = ?", like.CommentID)
		if like.UserID != nil {
			query = query.Where("user_id = ?", *like.UserID)
		} else {
			query = query.Where("ip_address = ?", like.IPAddress)
		}

		if err := query.First(&existingLike).Error; err == nil {
			return fmt.Errorf("already liked")
		}

		// 创建点赞记录
		if err := tx.Create(like).Error; err != nil {
			return err
		}

		// 更新评论的点赞数量
		return tx.Model(&models.Comment{}).
			Where("id = ?", like.CommentID).
			UpdateColumn("like_count", gorm.Expr("like_count + 1")).Error
	})
}

// DeleteCommentLike 删除评论点赞
func DeleteCommentLike(commentID uint, userID *uint, ipAddress string) error {
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 查找点赞记录
		query := tx.Where("comment_id = ?", commentID)
		if userID != nil {
			query = query.Where("user_id = ?", *userID)
		} else {
			query = query.Where("ip_address = ?", ipAddress)
		}

		var like models.CommentLike
		if err := query.First(&like).Error; err != nil {
			return err
		}

		// 删除点赞记录
		if err := tx.Delete(&like).Error; err != nil {
			return err
		}

		// 更新评论的点赞数量
		return tx.Model(&models.Comment{}).
			Where("id = ?", commentID).
			UpdateColumn("like_count", gorm.Expr("like_count - 1")).Error
	})
}

// CreateShareLike 创建分享点赞
func CreateShareLike(like *models.ShareLike) error {
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 检查是否已经点赞
		var existingLike models.ShareLike
		query := tx.Where("share_id = ?", like.ShareID)
		if like.UserID != nil {
			query = query.Where("user_id = ?", *like.UserID)
		} else {
			query = query.Where("ip_address = ?", like.IPAddress)
		}

		if err := query.First(&existingLike).Error; err == nil {
			return fmt.Errorf("already liked")
		}

		// 创建点赞记录
		if err := tx.Create(like).Error; err != nil {
			return err
		}

		// 更新分享的点赞数量
		return tx.Model(&models.Share{}).
			Where("id = ?", like.ShareID).
			UpdateColumn("like_count", gorm.Expr("like_count + 1")).Error
	})
}

// DeleteShareLike 删除分享点赞
func DeleteShareLike(shareID uint, userID *uint, ipAddress string) error {
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 查找点赞记录
		query := tx.Where("share_id = ?", shareID)
		if userID != nil {
			query = query.Where("user_id = ?", *userID)
		} else {
			query = query.Where("ip_address = ?", ipAddress)
		}

		var like models.ShareLike
		if err := query.First(&like).Error; err != nil {
			return err
		}

		// 删除点赞记录
		if err := tx.Delete(&like).Error; err != nil {
			return err
		}

		// 更新分享的点赞数量
		return tx.Model(&models.Share{}).
			Where("id = ?", shareID).
			UpdateColumn("like_count", gorm.Expr("like_count - 1")).Error
	})
}

// GetShareStatistics 获取分享统计
func GetShareStatistics(shareID uint) (*models.ShareStatistics, error) {
	stats := &models.ShareStatistics{
		ShareID: shareID,
	}

	// 总访问次数
	var totalViews int64
	database.DB.Model(&models.ShareAccess{}).
		Where("share_id = ?", shareID).
		Count(&totalViews)
	stats.TotalViews = int(totalViews)

	// 唯一访问次数
	var uniqueViews int64
	database.DB.Model(&models.ShareAccess{}).
		Where("share_id = ?", shareID).
		Distinct("ip_address").
		Count(&uniqueViews)
	stats.UniqueViews = int(uniqueViews)

	// 下载次数
	var totalDownloads int64
	database.DB.Model(&models.ShareAccess{}).
		Where("share_id = ? AND action = ?", shareID, "download").
		Count(&totalDownloads)
	stats.TotalDownloads = int(totalDownloads)

	// 评论数量
	var totalComments int64
	database.DB.Model(&models.Comment{}).
		Where("share_id = ? AND status = ?", shareID, "active").
		Count(&totalComments)
	stats.TotalComments = int(totalComments)

	// 点赞数量
	var totalLikes int64
	database.DB.Model(&models.ShareLike{}).
		Where("share_id = ?", shareID).
		Count(&totalLikes)
	stats.TotalLikes = int(totalLikes)

	// 按日期统计访问量
	stats.ViewsByDate = make(map[string]int)
	var dateViews []struct {
		Date  string `json:"date"`
		Count int    `json:"count"`
	}
	database.DB.Model(&models.ShareAccess{}).
		Select("DATE(created_at) as date, COUNT(*) as count").
		Where("share_id = ?", shareID).
		Group("DATE(created_at)").
		Order("date DESC").
		Limit(30).
		Find(&dateViews)

	for _, dv := range dateViews {
		stats.ViewsByDate[dv.Date] = dv.Count
	}

	// 按国家统计访问量
	stats.ViewsByCountry = make(map[string]int)
	var countryViews []struct {
		Country string `json:"country"`
		Count   int    `json:"count"`
	}
	database.DB.Model(&models.ShareAccess{}).
		Select("country, COUNT(*) as count").
		Where("share_id = ? AND country != ''", shareID).
		Group("country").
		Order("count DESC").
		Limit(10).
		Find(&countryViews)

	for _, cv := range countryViews {
		stats.ViewsByCountry[cv.Country] = cv.Count
	}

	// 最近访问记录
	database.DB.Preload("User").
		Where("share_id = ?", shareID).
		Order("created_at DESC").
		Limit(10).
		Find(&stats.RecentAccess)

	return stats, nil
}

// 辅助函数
func generateShareToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func generateInviteToken() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// UpdateShareViewCount 更新分享访问次数
func UpdateShareViewCount(shareID uint) error {
	return database.DB.Model(&models.Share{}).
		Where("id = ?", shareID).
		UpdateColumn("view_count", gorm.Expr("view_count + 1")).Error
}

// UpdateShareDownloadCount 更新分享下载次数
func UpdateShareDownloadCount(shareID uint) error {
	return database.DB.Model(&models.Share{}).
		Where("id = ?", shareID).
		UpdateColumn("download_count", gorm.Expr("download_count + 1")).Error
}

// GetExpiredShares 获取过期的分享
func GetExpiredShares() ([]models.Share, error) {
	var shares []models.Share
	now := time.Now()

	err := database.DB.Where("status = ? AND (expires_at < ? OR (max_views > 0 AND view_count >= max_views))",
		models.ShareStatusActive, now).Find(&shares).Error

	return shares, err
}

// UpdateExpiredShares 更新过期分享状态
func UpdateExpiredShares() error {
	now := time.Now()
	return database.DB.Model(&models.Share{}).
		Where("status = ? AND (expires_at < ? OR (max_views > 0 AND view_count >= max_views))",
			models.ShareStatusActive, now).
		Update("status", models.ShareStatusExpired).Error
}
