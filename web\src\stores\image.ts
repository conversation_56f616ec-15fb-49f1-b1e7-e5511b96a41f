import { defineStore } from 'pinia';
import type { Image } from '../types/image';
import api from '../services/api';

export const useImageStore = defineStore('image', {
  state: () => ({
    images: [] as Image[],
    currentAlbumImages: [] as Image[],
    loading: false,
  }),

  getters: {
    getImageList: (state) => state.images,
    getCurrentAlbumImages: (state) => state.currentAlbumImages,
    isLoading: (state) => state.loading,
  },

  actions: {
    // 获取图片列表
    async fetchImages(albumId?: number | 'uncategorized') {
      this.loading = true;
      try {
        this.images = await api.getImages(albumId);
        return { success: true };
      } catch (error: any) {
        console.error('Fetch images error:', error);
        return {
          success: false,
          message: error.response?.data?.message || '获取图片列表失败'
        };
      } finally {
        this.loading = false;
      }
    },

    // 获取相册图片
    async fetchAlbumImages(albumId: number | 'uncategorized') {
      this.loading = true;
      try {
        this.currentAlbumImages = await api.getAlbumImages(albumId);
        return { success: true };
      } catch (error: any) {
        console.error('Fetch album images error:', error);
        return {
          success: false,
          message: error.response?.data?.message || '获取相册图片失败'
        };
      } finally {
        this.loading = false;
      }
    },

    // 上传图片
    async uploadImage(file: File, albumId?: number) {
      this.loading = true;
      try {
        const result = await api.uploadImage(file, albumId);

        if (result.success) {
          // 添加到本地状态
          this.images.push(result.image);
          return { success: true, message: result.message, image: result.image };
        } else {
          return { success: false, message: result.message || '上传失败' };
        }
      } catch (error: any) {
        console.error('Upload image error:', error);
        return {
          success: false,
          message: error.response?.data?.message || '上传失败'
        };
      } finally {
        this.loading = false;
      }
    },

    // 删除图片
    async deleteImage(id: string | number) {
      try {
        await api.deleteImage(id);

        // 从本地状态中移除图片
        this.images = this.images.filter(image =>
          typeof id === 'string' ? image.name !== id : image.id !== id
        );
        this.currentAlbumImages = this.currentAlbumImages.filter(image =>
          typeof id === 'string' ? image.name !== id : image.id !== id
        );

        return { success: true, message: '删除成功' };
      } catch (error: any) {
        console.error('Delete image error:', error);
        return {
          success: false,
          message: error.response?.data?.message || '删除失败'
        };
      }
    },

    // 移动图片到相册
    async moveImageToAlbum(imageId: number, albumId?: number) {
      try {
        await api.moveImageToAlbum(imageId, albumId);

        // 更新本地状态中的图片
        const updateImage = (image: Image) => {
          if (image.id === imageId) {
            image.album_id = albumId;
          }
          return image;
        };

        this.images = this.images.map(updateImage);
        this.currentAlbumImages = this.currentAlbumImages.map(updateImage);

        return { success: true, message: '移动成功' };
      } catch (error: any) {
        console.error('Move image error:', error);
        return {
          success: false,
          message: error.response?.data?.message || '移动图片失败'
        };
      }
    },

    // 重置状态
    reset() {
      this.images = [];
      this.currentAlbumImages = [];
      this.loading = false;
    }
  },
});