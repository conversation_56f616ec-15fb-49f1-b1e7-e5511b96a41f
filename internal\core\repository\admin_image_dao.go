package dao

import (
	"time"

	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
)

// GetAllImagesPaginated 分页获取所有图片（管理员）
func GetAllImagesPaginated(req models.ImageListRequest) (*models.ImageListResponse, error) {
	var images []models.Image
	var total int64

	query := database.DB.Model(&models.Image{}).Preload("User").Preload("Album")

	// 应用搜索条件
	if req.Search != "" {
		query = query.Where("name LIKE ?", "%"+req.Search+"%")
	}

	// 应用用户过滤
	if req.UserID != 0 {
		query = query.Where("user_id = ?", req.UserID)
	}

	// 应用相册过滤
	if req.AlbumID != 0 {
		if req.AlbumID == 999999 { // 特殊值表示未分类
			query = query.Where("album_id IS NULL")
		} else {
			query = query.Where("album_id = ?", req.AlbumID)
		}
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 应用排序
	sortBy := "created_at"
	if req.SortBy != "" {
		sortBy = req.SortBy
	}
	if req.SortDesc {
		sortBy += " DESC"
	}
	query = query.Order(sortBy)

	// 应用分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	if err := query.Find(&images).Error; err != nil {
		return nil, err
	}

	// 转换为响应格式
	var imageResponses []models.AdminImageResponse
	for _, image := range images {
		imageResponse := models.AdminImageResponse{
			ID:        image.ID,
			Name:      image.Name,
			URL:       image.URL,
			Size:      image.Size,
			UserID:    image.UserID,
			AlbumID:   image.AlbumID,
			CreatedAt: image.CreatedAt,
			UpdatedAt: image.UpdatedAt,
		}

		// 添加用户信息
		if image.User.ID != 0 {
			imageResponse.User = &models.UserResponse{
				ID:    image.User.ID,
				Email: image.User.Email,
				Role: models.RoleResponse{
					ID:          image.User.Role.ID,
					Name:        image.User.Role.Name,
					DisplayName: image.User.Role.DisplayName,
					Description: image.User.Role.Description,
					IsSystem:    image.User.Role.IsSystem,
				},
				Status:      image.User.Status,
				LastLoginAt: image.User.LastLoginAt,
				CreatedAt:   image.User.CreatedAt,
				UpdatedAt:   image.User.UpdatedAt,
			}
		}

		// 添加相册信息
		if image.Album != nil {
			imageResponse.Album = &models.AlbumResponse{
				ID:          image.Album.ID,
				Name:        image.Album.Name,
				Description: image.Album.Description,
				CreatedAt:   image.Album.CreatedAt,
				UpdatedAt:   image.Album.UpdatedAt,
			}
		}

		imageResponses = append(imageResponses, imageResponse)
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &models.ImageListResponse{
		Images:     imageResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetImageStats 获取图片统计信息
func GetImageStats() (*models.ImageStats, error) {
	var stats models.ImageStats

	// 总图片数
	if err := database.DB.Model(&models.Image{}).Count(&stats.TotalImages).Error; err != nil {
		return nil, err
	}

	// 总存储大小
	var totalSize struct {
		Total int64
	}
	if err := database.DB.Model(&models.Image{}).Select("COALESCE(SUM(size), 0) as total").Scan(&totalSize).Error; err != nil {
		return nil, err
	}
	stats.TotalSize = totalSize.Total

	// 活跃用户数（有上传图片的用户）
	if err := database.DB.Model(&models.Image{}).Distinct("user_id").Count(&stats.ActiveUsers).Error; err != nil {
		return nil, err
	}

	// 今日上传数
	today := time.Now().Truncate(24 * time.Hour)
	if err := database.DB.Model(&models.Image{}).Where("created_at >= ?", today).Count(&stats.TodayUploads).Error; err != nil {
		return nil, err
	}

	// 本周上传数
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	if err := database.DB.Model(&models.Image{}).Where("created_at >= ?", weekStart).Count(&stats.WeekUploads).Error; err != nil {
		return nil, err
	}

	// 本月上传数
	monthStart := time.Date(today.Year(), today.Month(), 1, 0, 0, 0, 0, today.Location())
	if err := database.DB.Model(&models.Image{}).Where("created_at >= ?", monthStart).Count(&stats.MonthUploads).Error; err != nil {
		return nil, err
	}

	return &stats, nil
}

// GetImageDetailByID 获取图片详细信息
func GetImageDetailByID(imageID uint) (*models.AdminImageResponse, error) {
	var image models.Image
	if err := database.DB.Preload("User").Preload("User.Role").Preload("Album").First(&image, imageID).Error; err != nil {
		return nil, err
	}

	imageResponse := &models.AdminImageResponse{
		ID:        image.ID,
		Name:      image.Name,
		URL:       image.URL,
		Size:      image.Size,
		UserID:    image.UserID,
		AlbumID:   image.AlbumID,
		CreatedAt: image.CreatedAt,
		UpdatedAt: image.UpdatedAt,
	}

	// 添加用户信息
	if image.User.ID != 0 {
		imageResponse.User = &models.UserResponse{
			ID:    image.User.ID,
			Email: image.User.Email,
			Role: models.RoleResponse{
				ID:          image.User.Role.ID,
				Name:        image.User.Role.Name,
				DisplayName: image.User.Role.DisplayName,
				Description: image.User.Role.Description,
				IsSystem:    image.User.Role.IsSystem,
			},
			Status:      image.User.Status,
			LastLoginAt: image.User.LastLoginAt,
			CreatedAt:   image.User.CreatedAt,
			UpdatedAt:   image.User.UpdatedAt,
		}
	}

	// 添加相册信息
	if image.Album != nil {
		imageResponse.Album = &models.AlbumResponse{
			ID:          image.Album.ID,
			Name:        image.Album.Name,
			Description: image.Album.Description,
			CreatedAt:   image.Album.CreatedAt,
			UpdatedAt:   image.Album.UpdatedAt,
		}
	}

	return imageResponse, nil
}

// BulkDeleteImages 批量删除图片
func BulkDeleteImages(imageIDs []uint) (int64, error) {
	result := database.DB.Where("id IN ?", imageIDs).Delete(&models.Image{})
	return result.RowsAffected, result.Error
}

// GetUsersForFilter 获取用户列表用于过滤
func GetUsersForFilter() ([]models.UserForFilter, error) {
	var users []models.UserForFilter
	err := database.DB.Model(&models.User{}).Select("id, email").Find(&users).Error
	return users, err
}

// GetAlbumsForFilter 获取相册列表用于过滤
func GetAlbumsForFilter() ([]models.AlbumForFilter, error) {
	var albums []models.AlbumForFilter
	err := database.DB.Model(&models.Album{}).Select("id, name").Find(&albums).Error
	return albums, err
}
