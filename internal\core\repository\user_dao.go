package dao

import (
	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
	"time"
)

// CreateUser 创建新用户
func CreateUser(user *models.User) error {
	// 设置默认角色为普通用户
	if user.RoleID == 0 {
		user.RoleID = 3 // 普通用户角色ID
	}

	// 设置默认状态
	if user.Status == "" {
		user.Status = "active"
	}

	// 开始事务
	tx := database.DB.Begin()

	// 创建用户
	if err := tx.Create(user).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 将用户添加到默认用户组
	var defaultGroup models.Group
	if tx.Where("is_default = ? AND is_active = ?", true, true).First(&defaultGroup).Error == nil {
		userGroup := models.UserGroup{
			UserID:  user.ID,
			GroupID: defaultGroup.ID,
			AddedBy: 1, // 系统添加
		}
		if err := tx.Create(&userGroup).Error; err != nil {
			// 如果添加到默认组失败，不影响用户创建，只记录错误
			// 可以在这里添加日志记录
		}
	}

	return tx.Commit().Error
}

// GetUserByUsername 根据用户名获取用户（已废弃，使用邮箱登录）
// func GetUserByUsername(username string) (*models.User, error) {
// 	var user models.User
// 	err := database.DB.Where("username = ?", username).First(&user).Error
// 	return &user, err
// }

// GetUserByID 根据ID获取用户
func GetUserByID(id uint) (*models.User, error) {
	var user models.User
	err := database.DB.First(&user, id).Error
	return &user, err
}

// GetUserByUsername 根据用户名获取用户
func GetUserByUsername(username string) (*models.User, error) {
	var user models.User
	err := database.DB.Preload("Role").Where("username = ?", username).First(&user).Error
	return &user, err
}

// GetUserByEmail 根据邮箱获取用户
func GetUserByEmail(email string) (*models.User, error) {
	var user models.User
	err := database.DB.Preload("Role").Where("email = ?", email).First(&user).Error
	return &user, err
}

// UpdateUser 更新用户信息
func UpdateUser(user *models.User) error {
	return database.DB.Save(user).Error
}

// DeleteUser 删除用户
func DeleteUser(id uint) error {
	return database.DB.Delete(&models.User{}, id).Error
}

// GetUserWithDefaultAlbum 获取用户及其默认相册信息
func GetUserWithDefaultAlbum(id uint) (*models.User, error) {
	var user models.User
	err := database.DB.Preload("DefaultAlbum").First(&user, id).Error
	return &user, err
}

// UpdateUserDefaultAlbum 更新用户的默认相册
func UpdateUserDefaultAlbum(userID uint, albumID *uint) error {
	return database.DB.Model(&models.User{}).
		Where("id = ?", userID).
		Update("default_album_id", albumID).Error
}

// GetUserWithRole 获取用户及其角色信息
func GetUserWithRole(userID uint) (*models.User, error) {
	var user models.User
	err := database.DB.Preload("Role.Permissions").Preload("UserPermissions.Permission").First(&user, userID).Error
	return &user, err
}

// GetAllUsersWithRole 获取所有用户及其角色信息
func GetAllUsersWithRole() ([]models.User, error) {
	var users []models.User
	err := database.DB.Preload("Role").Find(&users).Error
	return users, err
}

// GetAllUsersWithRoleAndGroups 获取所有用户及其角色和用户组信息
func GetAllUsersWithRoleAndGroups() ([]models.User, error) {
	var users []models.User
	err := database.DB.Preload("Role").Preload("Groups").Find(&users).Error
	return users, err
}

// AssignUserToGroups 将用户分配到用户组
func AssignUserToGroups(userID uint, groupIDs []uint, addedBy uint) error {
	// 开始事务
	tx := database.DB.Begin()

	// 删除用户现有的组关联
	if err := tx.Where("user_id = ?", userID).Delete(&models.UserGroup{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 添加新的组关联
	for _, groupID := range groupIDs {
		userGroup := models.UserGroup{
			UserID:  userID,
			GroupID: groupID,
			AddedBy: addedBy,
		}

		if err := tx.Create(&userGroup).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// UpdateUserStatus 更新用户状态
func UpdateUserStatus(userID uint, status models.UserStatus) error {
	return database.DB.Model(&models.User{}).Where("id = ?", userID).Update("status", status).Error
}

// UpdateUserLastLogin 更新用户最后登录时间
func UpdateUserLastLogin(userID uint) error {
	now := time.Now()
	return database.DB.Model(&models.User{}).Where("id = ?", userID).Update("last_login_at", &now).Error
}

// GetUsersByRole 根据角色获取用户列表
func GetUsersByRole(roleID uint) ([]models.User, error) {
	var users []models.User
	err := database.DB.Where("role_id = ?", roleID).Find(&users).Error
	return users, err
}

// CountUsersByRole 统计指定角色的用户数量
func CountUsersByRole(roleID uint) (int64, error) {
	var count int64
	err := database.DB.Model(&models.User{}).Where("role_id = ?", roleID).Count(&count).Error
	return count, err
}

// GetUsersPaginated 分页获取用户列表
func GetUsersPaginated(req models.UserListRequest) (*models.UserListResponse, error) {
	var users []models.User
	var total int64

	query := database.DB.Model(&models.User{}).Preload("Role")

	// 应用过滤条件
	if req.Email != "" {
		query = query.Where("email LIKE ?", "%"+req.Email+"%")
	}
	if req.RoleID != 0 {
		query = query.Where("role_id = ?", req.RoleID)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 应用排序
	sortBy := "created_at"
	if req.SortBy != "" {
		sortBy = req.SortBy
	}
	if req.SortDesc {
		sortBy += " DESC"
	}
	query = query.Order(sortBy)

	// 应用分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	if err := query.Find(&users).Error; err != nil {
		return nil, err
	}

	// 转换为响应格式
	var userResponses []models.UserResponse
	for _, user := range users {
		userResponses = append(userResponses, models.UserResponse{
			ID:    user.ID,
			Email: user.Email,
			Role: models.RoleResponse{
				ID:          user.Role.ID,
				Name:        user.Role.Name,
				DisplayName: user.Role.DisplayName,
				Description: user.Role.Description,
				IsSystem:    user.Role.IsSystem,
			},
			Status:      user.Status,
			LastLoginAt: user.LastLoginAt,
			CreatedAt:   user.CreatedAt,
			UpdatedAt:   user.UpdatedAt,
		})
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &models.UserListResponse{
		Users:      userResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetUserWithStorageStats 获取用户及其存储统计信息（优化版本）
func GetUserWithStorageStats(id uint) (*models.UserWithStorageStats, error) {
	var result struct {
		models.User
		ImageCount     int64 `json:"image_count"`
		AlbumCount     int64 `json:"album_count"`
		RemainingQuota int64 `json:"remaining_quota"`
	}

	// 使用单个查询获取所有信息，避免N+1查询
	sql := `
		SELECT
			u.*,
			r.id as "role__id",
			r.name as "role__name",
			r.display_name as "role__display_name",
			r.description as "role__description",
			COUNT(DISTINCT i.id) as image_count,
			COUNT(DISTINCT a.id) as album_count,
			GREATEST(u.storage_quota - u.storage_used, 0) as remaining_quota
		FROM users u
		LEFT JOIN roles r ON u.role_id = r.id
		LEFT JOIN images i ON u.id = i.user_id
		LEFT JOIN albums a ON u.id = a.user_id
		WHERE u.id = ?
		GROUP BY u.id, r.id
	`

	err := database.DB.Raw(sql, id).Scan(&result).Error
	if err != nil {
		return nil, err
	}

	return &models.UserWithStorageStats{
		User:           result.User,
		ImageCount:     result.ImageCount,
		AlbumCount:     result.AlbumCount,
		RemainingQuota: result.RemainingQuota,
	}, nil
}

// GetAllUsersWithStorageStats 获取所有用户及其存储统计信息（优化版本）
func GetAllUsersWithStorageStats() ([]models.UserWithStorageStats, error) {
	var results []struct {
		models.User
		ImageCount     int64 `json:"image_count"`
		AlbumCount     int64 `json:"album_count"`
		RemainingQuota int64 `json:"remaining_quota"`
	}

	// 使用单个查询获取所有用户的统计信息，避免N+1查询
	sql := `
		SELECT
			u.*,
			r.id as "role__id",
			r.name as "role__name",
			r.display_name as "role__display_name",
			r.description as "role__description",
			COUNT(DISTINCT i.id) as image_count,
			COUNT(DISTINCT a.id) as album_count,
			GREATEST(u.storage_quota - u.storage_used, 0) as remaining_quota
		FROM users u
		LEFT JOIN roles r ON u.role_id = r.id
		LEFT JOIN images i ON u.id = i.user_id
		LEFT JOIN albums a ON u.id = a.user_id
		GROUP BY u.id, r.id
		ORDER BY u.created_at DESC
	`

	err := database.DB.Raw(sql).Scan(&results).Error
	if err != nil {
		return nil, err
	}

	var usersWithStats []models.UserWithStorageStats
	for _, result := range results {
		usersWithStats = append(usersWithStats, models.UserWithStorageStats{
			User:           result.User,
			ImageCount:     result.ImageCount,
			AlbumCount:     result.AlbumCount,
			RemainingQuota: result.RemainingQuota,
		})
	}

	return usersWithStats, nil
}
