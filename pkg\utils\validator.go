package utils

import (
	"fmt"
	"regexp"
	"strings"
)

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("%s: %s", e.Field, e.Message)
}

// ValidationErrors 多个验证错误
type ValidationErrors []ValidationError

func (e ValidationErrors) Error() string {
	var messages []string
	for _, err := range e {
		messages = append(messages, err.Error())
	}
	return strings.Join(messages, "; ")
}

// Validator 验证器接口
type Validator interface {
	Validate() error
}

// EmailValidator 邮箱验证器
type EmailValidator struct {
	Email string
	Field string
}

func (v EmailValidator) Validate() error {
	if v.Email == "" {
		return ValidationError{
			Field:   v.Field,
			Message: "邮箱不能为空",
		}
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(v.Email) {
		return ValidationError{
			Field:   v.Field,
			Message: "邮箱格式无效",
		}
	}

	return nil
}

// PasswordValidator 密码验证器
type PasswordValidator struct {
	Password string
	Field    string
	MinLen   int
}

func (v PasswordValidator) Validate() error {
	if v.Password == "" {
		return ValidationError{
			Field:   v.Field,
			Message: "密码不能为空",
		}
	}

	minLen := v.MinLen
	if minLen == 0 {
		minLen = 8 // 默认最小长度
	}

	if len(v.Password) < minLen {
		return ValidationError{
			Field:   v.Field,
			Message: fmt.Sprintf("密码长度不能少于%d位", minLen),
		}
	}

	return nil
}

// StringValidator 字符串验证器
type StringValidator struct {
	Value  string
	Field  string
	MinLen int
	MaxLen int
}

func (v StringValidator) Validate() error {
	if v.Value == "" {
		return ValidationError{
			Field:   v.Field,
			Message: "不能为空",
		}
	}

	if v.MinLen > 0 && len(v.Value) < v.MinLen {
		return ValidationError{
			Field:   v.Field,
			Message: fmt.Sprintf("长度不能少于%d位", v.MinLen),
		}
	}

	if v.MaxLen > 0 && len(v.Value) > v.MaxLen {
		return ValidationError{
			Field:   v.Field,
			Message: fmt.Sprintf("长度不能超过%d位", v.MaxLen),
		}
	}

	return nil
}

// FileValidator 文件验证器
type FileValidator struct {
	Filename    string
	ContentType string
	Size        int64
	Field       string
	MaxSize     int64
	AllowedTypes []string
}

func (v FileValidator) Validate() error {
	var errors ValidationErrors

	if v.Filename == "" {
		errors = append(errors, ValidationError{
			Field:   v.Field,
			Message: "文件名不能为空",
		})
	}

	if v.Size == 0 {
		errors = append(errors, ValidationError{
			Field:   v.Field,
			Message: "文件不能为空",
		})
	}

	if v.MaxSize > 0 && v.Size > v.MaxSize {
		errors = append(errors, ValidationError{
			Field:   v.Field,
			Message: fmt.Sprintf("文件大小不能超过%d字节", v.MaxSize),
		})
	}

	if len(v.AllowedTypes) > 0 {
		allowed := false
		for _, allowedType := range v.AllowedTypes {
			if v.ContentType == allowedType {
				allowed = true
				break
			}
		}
		if !allowed {
			errors = append(errors, ValidationError{
				Field:   v.Field,
				Message: fmt.Sprintf("不支持的文件类型: %s", v.ContentType),
			})
		}
	}

	if len(errors) > 0 {
		return errors
	}

	return nil
}

// ValidateStruct 验证结构体
func ValidateStruct(validators ...Validator) error {
	var errors ValidationErrors

	for _, validator := range validators {
		if err := validator.Validate(); err != nil {
			if validationErrors, ok := err.(ValidationErrors); ok {
				errors = append(errors, validationErrors...)
			} else if validationError, ok := err.(ValidationError); ok {
				errors = append(errors, validationError)
			} else {
				errors = append(errors, ValidationError{
					Field:   "unknown",
					Message: err.Error(),
				})
			}
		}
	}

	if len(errors) > 0 {
		return errors
	}

	return nil
}

// IsValidEmail 检查邮箱格式是否有效
func IsValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// IsValidPassword 检查密码是否有效
func IsValidPassword(password string, minLen int) bool {
	if minLen == 0 {
		minLen = 8
	}
	return len(password) >= minLen
}

// IsValidImageType 检查是否为有效的图片类型
func IsValidImageType(contentType string) bool {
	allowedTypes := []string{
		"image/jpeg",
		"image/png",
		"image/gif",
		"image/webp",
		"image/svg+xml",
	}

	for _, allowedType := range allowedTypes {
		if contentType == allowedType {
			return true
		}
	}

	return false
}

// FormatFileSize 格式化文件大小
func FormatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// SanitizeFilename 清理文件名
func SanitizeFilename(filename string) string {
	// 移除或替换不安全的字符
	reg := regexp.MustCompile(`[<>:"/\\|?*]`)
	sanitized := reg.ReplaceAllString(filename, "_")
	
	// 限制长度
	if len(sanitized) > 255 {
		sanitized = sanitized[:255]
	}
	
	return sanitized
}
