<template>
  <!-- 全局确认弹窗 -->
  <ConfirmDialog
    :show="dialogStore.confirmDialog.show"
    :title="dialogStore.confirmDialog.title"
    :message="dialogStore.confirmDialog.message"
    :details="dialogStore.confirmDialog.details"
    :type="dialogStore.confirmDialog.type"
    :confirm-text="dialogStore.confirmDialog.confirmText"
    :cancel-text="dialogStore.confirmDialog.cancelText"
    :loading-text="dialogStore.confirmDialog.loadingText"
    :loading="dialogStore.confirmDialog.loading"
    :close-on-overlay="dialogStore.confirmDialog.closeOnOverlay"
    @confirm="dialogStore.confirmDialog.onConfirm"
    @cancel="dialogStore.confirmDialog.onCancel"
  />

  <!-- 全局通知 -->
  <Teleport to="body">
    <div class="global-notifications">
      <TransitionGroup name="notification" tag="div">
        <div
          v-for="notification in dialogStore.notifications"
          :key="notification.id"
          class="notification-toast"
          :class="getNotificationClass(notification.type)"
        >
          <div class="notification-icon">
            <i :class="getIconClass(notification.type)"></i>
          </div>
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div v-if="notification.message" class="notification-message">
              {{ notification.message }}
            </div>
          </div>
          <button 
            class="notification-close"
            @click="dialogStore.removeNotification(notification.id)"
          >
            <i class="bi bi-x"></i>
          </button>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import ConfirmDialog from './ConfirmDialog.vue'
import { useDialogStore } from '../stores/dialog'

const dialogStore = useDialogStore()

const getNotificationClass = (type: string) => {
  const classes = {
    success: 'notification-success',
    error: 'notification-error',
    warning: 'notification-warning',
    info: 'notification-info'
  }
  return classes[type as keyof typeof classes]
}

const getIconClass = (type: string) => {
  const icons = {
    success: 'bi bi-check-circle-fill',
    error: 'bi bi-x-circle-fill',
    warning: 'bi bi-exclamation-triangle-fill',
    info: 'bi bi-info-circle-fill'
  }
  return icons[type as keyof typeof icons]
}
</script>

<style scoped>
.global-notifications {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  pointer-events: none;
}

.notification-toast {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  max-width: 400px;
  pointer-events: auto;
  position: relative;
}

.notification-success {
  border-left-color: #28a745;
}

.notification-error {
  border-left-color: #dc3545;
}

.notification-warning {
  border-left-color: #ffc107;
}

.notification-info {
  border-left-color: #17a2b8;
}

.notification-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}

.notification-success .notification-icon {
  color: #28a745;
}

.notification-error .notification-icon {
  color: #dc3545;
}

.notification-warning .notification-icon {
  color: #ffc107;
}

.notification-info .notification-icon {
  color: #17a2b8;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.notification-close {
  flex-shrink: 0;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.notification-close:hover {
  background-color: #f8f9fa;
  color: #666;
}

/* 动画效果 */
.notification-enter-active {
  transition: all 0.3s ease-out;
}

.notification-leave-active {
  transition: all 0.3s ease-in;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
