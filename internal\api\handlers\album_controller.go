package controllers

import (
	"net/http"
	"strconv"

	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"
	"cloudbed/pkg/pagination"
	"cloudbed/pkg/response"

	"github.com/gin-gonic/gin"
)

// CreateAlbum 创建相册
func CreateAlbum(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req models.AlbumCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	album := models.Album{
		Name:        req.Name,
		Description: req.Description,
		UserID:      userID.(uint),
	}

	if err := dao.CreateAlbum(&album); err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to create album"})
		return
	}

	c.<PERSON>(http.StatusCreated, gin.H{
		"success": true,
		"message": "Album created successfully",
		"album":   album,
	})
}

// GetAlbums 获取用户的相册列表（分页）
func GetAlbums(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 解析分页参数
	req := pagination.ParseAlbumRequest(c)

	// 分页获取相册
	result, err := dao.GetAlbumsByUserIDPaginated(userID.(uint), req)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取相册列表失败", err.Error())
		return
	}

	response.Success(c, result)
}

// GetAlbum 获取单个相册详情
func GetAlbum(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	albumIDStr := c.Param("id")
	albumID, err := strconv.ParseUint(albumIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid album ID"})
		return
	}

	album, err := dao.GetAlbumByIDAndUserID(uint(albumID), userID.(uint))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Album not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"album":   album,
	})
}

// UpdateAlbum 更新相册信息
func UpdateAlbum(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	albumIDStr := c.Param("id")
	albumID, err := strconv.ParseUint(albumIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid album ID"})
		return
	}

	var req models.AlbumUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查相册是否属于当前用户
	album, err := dao.GetAlbumByIDAndUserID(uint(albumID), userID.(uint))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Album not found"})
		return
	}

	// 更新相册信息
	album.Name = req.Name
	album.Description = req.Description
	if req.CoverImage != "" {
		album.CoverImage = req.CoverImage
	}

	if err := dao.UpdateAlbum(album); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update album"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Album updated successfully",
		"album":   album,
	})
}

// DeleteAlbum 删除相册
func DeleteAlbum(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	albumIDStr := c.Param("id")
	albumID, err := strconv.ParseUint(albumIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid album ID"})
		return
	}

	// 检查相册是否属于当前用户
	_, err = dao.GetAlbumByIDAndUserID(uint(albumID), userID.(uint))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Album not found"})
		return
	}

	if err := dao.DeleteAlbumByUserID(uint(albumID), userID.(uint)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete album"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Album deleted successfully",
	})
}

// MoveImagesToAlbum 将图片移动到相册
func MoveImagesToAlbum(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req struct {
		ImageIDs []uint `json:"image_ids" binding:"required"`
		AlbumID  *uint  `json:"album_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 如果指定了相册ID，检查相册是否属于当前用户
	if req.AlbumID != nil {
		owned, err := dao.CheckAlbumOwnership(*req.AlbumID, userID.(uint))
		if err != nil || !owned {
			c.JSON(http.StatusForbidden, gin.H{"error": "Album not found or access denied"})
			return
		}
	}

	// 移动图片到相册
	if err := dao.MoveImagesByIDsToAlbum(req.ImageIDs, req.AlbumID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to move images"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Images moved successfully",
	})
}

// GetAlbumImages 获取相册中的图片
func GetAlbumImages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	albumIDStr := c.Param("id")
	if albumIDStr == "uncategorized" {
		// 获取未分类图片
		images, err := dao.GetUncategorizedImagesByUserID(userID.(uint))
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get images"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"images":  images,
		})
		return
	}

	albumID, err := strconv.ParseUint(albumIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid album ID"})
		return
	}

	// 检查相册是否属于当前用户
	_, err = dao.GetAlbumByIDAndUserID(uint(albumID), userID.(uint))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Album not found"})
		return
	}

	images, err := dao.GetImagesByAlbumID(uint(albumID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get images"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"images":  images,
	})
}
