@import 'bootstrap-icons/font/bootstrap-icons.css';

:root {
  /* 浅色主题变量 */
  --bg-color: #f5f7fa;
  --text-color: #333333;
  --text-secondary: #666666;
  --border: #e1e5eb;
  --card-bg: #ffffff;
  --header-bg: #ffffff;
  --sidebar-bg: linear-gradient(135deg, #2c3e50, #4a6491);
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  
  /* 滚动条样式 */
  --scrollbar-thumb: #c1c1c1;
  --scrollbar-track: #f1f1f1;
  --scrollbar-thumb-hover: #a1a1a1;
}

/* 深色主题变量 */
body.dark-theme {
  --bg-color: #1a1a1a;
  --text-color: #f0f0f0;
  --text-secondary: #aaaaaa;
  --border: #333333;
  --card-bg: #252525;
  --header-bg: #2d2d2d;
  --sidebar-bg: linear-gradient(135deg, #1c2c38, #2a3a50);
  --input-bg: #333333;
  --input-border: #444444;
  
  /* 滚动条样式 */
  --scrollbar-thumb: #424242;
  --scrollbar-track: #2d2d2d;
  --scrollbar-thumb-hover: #555555;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s, color 0.3s;
  /* 防止文本在缩放时模糊 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 确保在高DPI屏幕上清晰显示 */
  text-rendering: optimizeLegibility;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* 基础卡片样式 */
.card {
  background: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 25px;
  overflow: hidden;
  border: 1px solid var(--border);
  /* 确保卡片在缩放时保持清晰 */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--header-bg);
}

.card-header h4 {
  margin: 0;
  color: var(--text-color);
  font-weight: 500;
}

.card-body {
  padding: 20px;
  background-color: var(--card-bg);
}

/* 按钮样式 */
.btn {
  padding: 8px 15px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  background-color: var(--card-bg);
  color: var(--text-color);
  /* 确保按钮在缩放时保持清晰 */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.85rem;
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--border);
  color: var(--text-color);
}

.btn-outline:hover {
  background-color: var(--header-bg);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff, #00bcd4);
  color: white;
}

body.dark-theme .btn-primary {
  background: linear-gradient(135deg, #3a57b0, #1a8fc9);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

body.dark-theme .btn-primary:hover {
  box-shadow: 0 5px 15px rgba(102, 179, 255, 0.3);
}

/* 表单元素样式 */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 16px;
  box-sizing: border-box;
  transition: border-color 0.3s, box-shadow 0.3s;
  background-color: var(--input-bg);
  color: var(--text-color);
  /* 确保表单元素在缩放时保持清晰 */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;
}

.form-control:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

body.dark-theme .form-control:focus {
  border-color: #66b3ff;
  box-shadow: 0 0 0 3px rgba(102, 179, 255, 0.1);
}

.form-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-top: 5px;
}

/* 进度条样式 */
.progress {
  height: 12px;
  background-color: var(--header-bg);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 15px;
  border: 1px solid var(--border);
  /* 确保进度条在缩放时保持清晰 */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  transition: width 0.3s;
}

/* 通知消息样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  color: white;
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
  z-index: 1000;
  animation: slideIn 0.3s, fadeOut 0.5s 2.5s forwards;
  display: flex;
  align-items: center;
  gap: 10px;
  max-width: 350px;
  /* 确保通知在缩放时保持清晰 */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;
}

.notification.success {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.notification.error {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.notification.info {
  background: linear-gradient(135deg, #17a2b8, #00bcd4);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .notification {
    right: 10px;
    left: 10px;
    max-width: none;
  }
}

/* 针对浏览器缩放的优化 */
@media screen and (-webkit-min-device-pixel-ratio: 1.25), 
       screen and (min-resolution: 120dpi) {
  body {
    /* 在高DPI屏幕上优化渲染 */
    image-rendering: -webkit-optimize-contrast;
  }
  
  .card,
  .btn,
  .form-control,
  .progress,
  .notification {
    /* 确保在缩放时保持清晰 */
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: transform;
  }
}

/* 针对更大缩放比例的优化 */
@media screen and (-webkit-min-device-pixel-ratio: 1.5), 
       screen and (min-resolution: 144dpi) {
  :root {
    font-size: 16px;
  }
  
  .btn {
    padding: 10px 16px;
  }
  
  .btn-sm {
    padding: 8px 14px;
  }
  
  .form-control {
    padding: 14px;
  }
  
  .card-header {
    padding: 22px;
  }

  .card-body {
    padding: 22px;
  }
}