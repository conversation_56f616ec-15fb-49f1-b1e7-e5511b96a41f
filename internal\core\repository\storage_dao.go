package dao

import (
	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
)

// GetUserStorageInfo 获取用户存储信息
func GetUserStorageInfo(userID uint) (*models.StorageQuotaResponse, error) {
	var user models.User
	err := database.DB.Select("id, username, email, storage_quota, storage_used").
		Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, err
	}

	return calculateStorageInfo(&user), nil
}

// GetAllUsersStorageInfo 获取所有用户的存储信息
func GetAllUsersStorageInfo() ([]models.StorageQuotaResponse, error) {
	var users []models.User
	err := database.DB.Select("id, username, email, storage_quota, storage_used").Find(&users).Error
	if err != nil {
		return nil, err
	}

	var result []models.StorageQuotaResponse
	for _, user := range users {
		result = append(result, *calculateStorageInfo(&user))
	}

	return result, nil
}

// UpdateUserStorageQuota 更新用户存储配额
func UpdateUserStorageQuota(userID uint, quota int64) error {
	return database.DB.Model(&models.User{}).
		Where("id = ?", userID).
		Update("storage_quota", quota).Error
}

// UpdateUserStorageUsed 更新用户已使用存储
func UpdateUserStorageUsed(userID uint, used int64) error {
	return database.DB.Model(&models.User{}).
		Where("id = ?", userID).
		Update("storage_used", used).Error
}

// IncrementUserStorageUsed 增加用户已使用存储
func IncrementUserStorageUsed(userID uint, increment int64) error {
	return database.DB.Model(&models.User{}).
		Where("id = ?", userID).
		Update("storage_used", database.DB.Raw("storage_used + ?", increment)).Error
}

// DecrementUserStorageUsed 减少用户已使用存储
func DecrementUserStorageUsed(userID uint, decrement int64) error {
	return database.DB.Model(&models.User{}).
		Where("id = ? AND storage_used >= ?", userID, decrement).
		Update("storage_used", database.DB.Raw("storage_used - ?", decrement)).Error
}

// CheckStorageQuota 检查用户是否有足够的存储空间
func CheckStorageQuota(userID uint, requiredSize int64) (bool, error) {
	var user models.User
	err := database.DB.Select("storage_quota, storage_used").
		Where("id = ?", userID).First(&user).Error
	if err != nil {
		return false, err
	}

	available := user.StorageQuota - user.StorageUsed
	return available >= requiredSize, nil
}

// RecalculateUserStorageUsed 重新计算用户的存储使用量
func RecalculateUserStorageUsed(userID uint) error {
	var totalSize int64
	err := database.DB.Model(&models.Image{}).
		Where("user_id = ?", userID).
		Select("COALESCE(SUM(size), 0)").Scan(&totalSize).Error
	if err != nil {
		return err
	}

	return UpdateUserStorageUsed(userID, totalSize)
}

// calculateStorageInfo 计算存储信息
func calculateStorageInfo(user *models.User) *models.StorageQuotaResponse {
	const GB = 1024 * 1024 * 1024

	quotaGB := float64(user.StorageQuota) / GB
	usedGB := float64(user.StorageUsed) / GB
	availableGB := quotaGB - usedGB

	var usagePercent float64
	if user.StorageQuota > 0 {
		usagePercent = (float64(user.StorageUsed) / float64(user.StorageQuota)) * 100
	}

	return &models.StorageQuotaResponse{
		UserID:       user.ID,
		Username:     user.Username,
		Email:        user.Email,
		StorageQuota: user.StorageQuota,
		StorageUsed:  user.StorageUsed,
		UsagePercent: usagePercent,
		QuotaGB:      quotaGB,
		UsedGB:       usedGB,
		AvailableGB:  availableGB,
	}
}
