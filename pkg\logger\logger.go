package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
	FATAL
)

// String 返回日志级别的字符串表示
func (l LogLevel) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	case FATAL:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// Logger 结构化日志器
type Logger struct {
	level  LogLevel
	output io.Writer
	prefix string
}

// Fields 日志字段
type Fields map[string]any

// Entry 日志条目
type Entry struct {
	logger *Logger
	fields Fields
}

var defaultLogger *Logger

// init 初始化默认日志器
func init() {
	defaultLogger = New(INFO, os.Stdout, "[IMAGE-BACKUP] ")
}

// New 创建新的日志器
func New(level LogLevel, output io.Writer, prefix string) *Logger {
	return &Logger{
		level:  level,
		output: output,
		prefix: prefix,
	}
}

// SetLevel 设置日志级别
func (l *Logger) SetLevel(level LogLevel) {
	l.level = level
}

// WithFields 添加字段
func (l *Logger) WithFields(fields Fields) *Entry {
	return &Entry{
		logger: l,
		fields: fields,
	}
}

// log 记录日志
func (l *Logger) log(level LogLevel, msg string, fields Fields) {
	if level < l.level {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")

	// 构建日志消息
	logMsg := fmt.Sprintf("%s%s [%s] %s", l.prefix, timestamp, level.String(), msg)

	// 添加字段
	if len(fields) > 0 {
		logMsg += " |"
		for key, value := range fields {
			logMsg += fmt.Sprintf(" %s=%v", key, value)
		}
	}

	logMsg += "\n"

	// 写入输出
	l.output.Write([]byte(logMsg))
}

// Debug 记录调试日志
func (l *Logger) Debug(msg string) {
	l.log(DEBUG, msg, nil)
}

// Info 记录信息日志
func (l *Logger) Info(msg string) {
	l.log(INFO, msg, nil)
}

// Warn 记录警告日志
func (l *Logger) Warn(msg string) {
	l.log(WARN, msg, nil)
}

// Error 记录错误日志
func (l *Logger) Error(msg string) {
	l.log(ERROR, msg, nil)
}

// Fatal 记录致命错误日志并退出
func (l *Logger) Fatal(msg string) {
	l.log(FATAL, msg, nil)
	os.Exit(1)
}

// Entry 方法
func (e *Entry) Debug(msg string) {
	e.logger.log(DEBUG, msg, e.fields)
}

func (e *Entry) Info(msg string) {
	e.logger.log(INFO, msg, e.fields)
}

func (e *Entry) Warn(msg string) {
	e.logger.log(WARN, msg, e.fields)
}

func (e *Entry) Error(msg string) {
	e.logger.log(ERROR, msg, e.fields)
}

func (e *Entry) Fatal(msg string) {
	e.logger.log(FATAL, msg, e.fields)
	os.Exit(1)
}

// 全局函数
func SetLevel(level LogLevel) {
	defaultLogger.SetLevel(level)
}

func WithFields(fields Fields) *Entry {
	return defaultLogger.WithFields(fields)
}

func Debug(msg string) {
	defaultLogger.Debug(msg)
}

func Info(msg string) {
	defaultLogger.Info(msg)
}

func Warn(msg string) {
	defaultLogger.Warn(msg)
}

func Error(msg string) {
	defaultLogger.Error(msg)
}

func Fatal(msg string) {
	defaultLogger.Fatal(msg)
}

// InitFileLogger 初始化文件日志器
func InitFileLogger(logDir string) error {
	// 确保日志目录存在
	if err := os.MkdirAll(logDir, os.ModePerm); err != nil {
		return fmt.Errorf("failed to create log directory: %w", err)
	}

	// 创建日志文件
	logFile := filepath.Join(logDir, fmt.Sprintf("app_%s.log", time.Now().Format("2006-01-02")))
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("failed to open log file: %w", err)
	}

	// 创建多重写入器（同时写入文件和控制台）
	multiWriter := io.MultiWriter(os.Stdout, file)

	// 更新默认日志器
	defaultLogger = New(INFO, multiWriter, "[IMAGE-BACKUP] ")

	return nil
}

// LogUserAction 记录用户操作
func LogUserAction(userID uint, action string, details Fields) {
	fields := Fields{
		"user_id": userID,
		"action":  action,
	}

	// 合并详细信息
	for k, v := range details {
		fields[k] = v
	}

	WithFields(fields).Info("User action")
}

// LogSystemEvent 记录系统事件
func LogSystemEvent(event string, details Fields) {
	fields := Fields{
		"event": event,
	}

	// 合并详细信息
	for k, v := range details {
		fields[k] = v
	}

	WithFields(fields).Info("System event")
}

// LogError 记录错误
func LogError(err error, context string, details Fields) {
	fields := Fields{
		"error":   err.Error(),
		"context": context,
	}

	// 合并详细信息
	for k, v := range details {
		fields[k] = v
	}

	WithFields(fields).Error("Error occurred")
}

// LogAPIRequest 记录API请求
func LogAPIRequest(method, path string, userID uint, statusCode int, duration time.Duration) {
	fields := Fields{
		"method":      method,
		"path":        path,
		"user_id":     userID,
		"status_code": statusCode,
		"duration_ms": duration.Milliseconds(),
	}

	WithFields(fields).Info("API request")
}
