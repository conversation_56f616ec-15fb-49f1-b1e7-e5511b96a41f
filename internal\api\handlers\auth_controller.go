package controllers

import (
	"fmt"
	"time"

	"cloudbed/internal/config"
	models "cloudbed/internal/core/domain"
	dao "cloudbed/internal/core/repository"
	"cloudbed/pkg/logger"
	"cloudbed/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

// 获取JWT密钥的函数
func getJWTKey() []byte {
	return []byte(config.LoadConfig().JWT.Secret)
}

// Credentials 登录凭据结构（只使用邮箱）
type Credentials struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest 注册请求结构
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// Claims JWT声明结构
type Claims struct {
	UserID uint   `json:"user_id"`
	Email  string `json:"email"`
	jwt.RegisteredClaims
}

// RegisterUser 注册新用户（只使用邮箱）
func RegisterUser(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 检查用户名是否已存在
	if _, err := dao.GetUserByUsername(req.Username); err == nil {
		response.Conflict(c, "用户名已存在")
		return
	}

	// 检查邮箱是否已存在
	if _, err := dao.GetUserByEmail(req.Email); err == nil {
		response.Conflict(c, "邮箱已被注册")
		return
	}

	// 哈希密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), 8)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "密码加密失败", err.Error())
		return
	}

	// 创建用户
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: string(hashedPassword),
	}

	// 保存到数据库
	if err := dao.CreateUser(&user); err != nil {
		response.InternalServerErrorWithDetail(c, "创建用户失败", err.Error())
		return
	}

	// 不返回密码
	user.Password = ""

	// 记录用户注册日志
	logger.LogUserAction(user.ID, "user_register", logger.Fields{
		"username": user.Username,
		"email":    user.Email,
	})

	response.SuccessWithMessage(c, "注册成功", user)
}

// Login 用户登录（只使用邮箱）
func Login(c *gin.Context) {
	var creds Credentials
	if err := c.ShouldBindJSON(&creds); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 根据邮箱查找用户（包含角色信息）
	user, err := dao.GetUserByEmail(creds.Email)
	if err != nil {
		response.Unauthorized(c, "邮箱或密码错误")
		return
	}

	// 检查用户状态
	if user.Status != models.UserStatusActive {
		response.Forbidden(c, "账户未激活")
		return
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(creds.Password)); err != nil {
		response.Unauthorized(c, "邮箱或密码错误")
		return
	}

	// 更新最后登录时间
	dao.UpdateUserLastLogin(user.ID)

	// 创建JWT token
	expirationTime := time.Now().Add(24 * time.Hour)
	claims := &Claims{
		UserID: user.ID,
		Email:  user.Email,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(getJWTKey())
	if err != nil {
		response.InternalServerErrorWithDetail(c, "生成访问令牌失败", err.Error())
		return
	}

	// 不返回密码
	user.Password = ""

	// 记录用户登录日志
	logger.LogUserAction(user.ID, "user_login", logger.Fields{
		"username": user.Username,
		"email":    user.Email,
	})

	response.Success(c, gin.H{
		"user":  user,
		"token": tokenString,
	})
}

// Logout 用户登出
func Logout(c *gin.Context) {
	// 在实际应用中，可以将token加入黑名单
	// 这里简单返回成功消息
	response.SuccessWithMessage(c, "退出登录成功", nil)
}

// ValidateToken 验证JWT token的中间件
func ValidateToken(tokenString string) (*Claims, error) {
	claims := &Claims{}

	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		// 确保token使用的是预期的签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return getJWTKey(), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	return claims, nil
}
