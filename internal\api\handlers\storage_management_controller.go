package controllers

import (
	"encoding/json"
	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"
	"cloudbed/internal/core/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// GetStorageConfigs 获取所有存储配置
func GetStorageConfigs(c *gin.Context) {
	configs, err := dao.GetAllStorageConfigs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get storage configs"})
		return
	}

	var responses []models.StorageConfigResponse
	for _, config := range configs {
		detail, err := dao.ParseStorageConfig(&config)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse storage config"})
			return
		}

		responses = append(responses, models.StorageConfigResponse{
			ID:          config.ID,
			Name:        config.Name,
			Provider:    config.Provider,
			IsDefault:   config.IsDefault,
			IsEnabled:   config.IsEnabled,
			AccessURL:   config.AccessURL,
			StoragePath: config.StoragePath,
			Config:      detail.ParsedConfig,
			CreatedAt:   config.CreatedAt,
			UpdatedAt:   config.UpdatedAt,
		})
	}

	c.JSON(http.StatusOK, models.StorageConfigListResponse{
		Configs: responses,
		Total:   int64(len(responses)),
	})
}

// GetStorageConfigByID 获取指定存储配置
func GetStorageConfigByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid storage config ID"})
		return
	}

	config, err := dao.GetStorageConfigByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Storage config not found"})
		return
	}

	detail, err := dao.ParseStorageConfig(config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse storage config"})
		return
	}

	response := models.StorageConfigResponse{
		ID:          config.ID,
		Name:        config.Name,
		Provider:    config.Provider,
		IsDefault:   config.IsDefault,
		IsEnabled:   config.IsEnabled,
		AccessURL:   config.AccessURL,
		StoragePath: config.StoragePath,
		Config:      detail.ParsedConfig,
		CreatedAt:   config.CreatedAt,
		UpdatedAt:   config.UpdatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// CreateStorageConfig 创建存储配置
func CreateStorageConfig(c *gin.Context) {
	var req models.StorageConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证配置
	if err := validateStorageConfig(req.Provider, req.Config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 序列化配置
	configJSON, err := json.Marshal(req.Config)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid config format"})
		return
	}

	config := &models.StorageConfig{
		Name:        req.Name,
		Provider:    req.Provider,
		IsDefault:   req.IsDefault,
		IsEnabled:   req.IsEnabled,
		AccessURL:   req.AccessURL,
		StoragePath: req.StoragePath,
		Config:      string(configJSON),
	}

	if err := dao.CreateStorageConfig(config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create storage config"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Storage config created successfully",
		"id":      config.ID,
	})
}

// UpdateStorageConfig 更新存储配置
func UpdateStorageConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid storage config ID"})
		return
	}

	var req models.StorageConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证配置
	if err := validateStorageConfig(req.Provider, req.Config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 序列化配置
	configJSON, err := json.Marshal(req.Config)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid config format"})
		return
	}

	config := &models.StorageConfig{
		Name:        req.Name,
		Provider:    req.Provider,
		IsDefault:   req.IsDefault,
		IsEnabled:   req.IsEnabled,
		AccessURL:   req.AccessURL,
		StoragePath: req.StoragePath,
		Config:      string(configJSON),
	}

	if err := dao.UpdateStorageConfig(uint(id), config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update storage config"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Storage config updated successfully"})
}

// DeleteStorageConfig 删除存储配置
func DeleteStorageConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid storage config ID"})
		return
	}

	if err := dao.DeleteStorageConfig(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete storage config"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Storage config deleted successfully"})
}

// SetDefaultStorageConfig 设置默认存储配置
func SetDefaultStorageConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid storage config ID"})
		return
	}

	if err := dao.SetDefaultStorageConfig(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to set default storage config"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Default storage config set successfully"})
}

// ToggleStorageConfigStatus 切换存储配置状态
func ToggleStorageConfigStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid storage config ID"})
		return
	}

	if err := dao.ToggleStorageConfigStatus(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to toggle storage config status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Storage config status toggled successfully"})
}

// TestStorageConfig 测试存储配置
func TestStorageConfig(c *gin.Context) {
	var req models.StorageTestRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证配置
	if err := validateStorageConfig(req.Provider, req.Config); err != nil {
		c.JSON(http.StatusBadRequest, models.StorageTestResponse{
			Success: false,
			Message: "Configuration validation failed",
			Details: err.Error(),
		})
		return
	}

	// 测试连接
	result := services.TestStorageConnection(req.Provider, req.Config)
	c.JSON(http.StatusOK, result)
}

// GetStorageStats 获取存储统计信息
func GetStorageStats(c *gin.Context) {
	stats, err := dao.GetStorageStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get storage stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// validateStorageConfig 验证存储配置
func validateStorageConfig(provider models.StorageProvider, config interface{}) error {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return err
	}

	switch provider {
	case models.StorageProviderLocal:
		var localConfig models.LocalStorageConfig
		return json.Unmarshal(configBytes, &localConfig)
	case models.StorageProviderAliOSS:
		var aliConfig models.AliOSSConfig
		return json.Unmarshal(configBytes, &aliConfig)
	case models.StorageProviderTencentCOS:
		var tencentConfig models.TencentCOSConfig
		return json.Unmarshal(configBytes, &tencentConfig)
	case models.StorageProviderQiniuKodo:
		var qiniuConfig models.QiniuKodoConfig
		return json.Unmarshal(configBytes, &qiniuConfig)
	case models.StorageProviderAWSS3:
		var awsConfig models.AWSS3Config
		return json.Unmarshal(configBytes, &awsConfig)
	case models.StorageProviderFTP:
		var ftpConfig models.FTPConfig
		return json.Unmarshal(configBytes, &ftpConfig)
	case models.StorageProviderSFTP:
		var sftpConfig models.SFTPConfig
		return json.Unmarshal(configBytes, &sftpConfig)
	default:
		return nil // 允许未知类型
	}
}
