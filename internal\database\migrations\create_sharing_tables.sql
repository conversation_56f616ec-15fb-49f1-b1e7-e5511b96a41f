-- 分享表
CREATE TABLE IF NOT EXISTS shares (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    share_token VARCHAR(64) UNIQUE NOT NULL COMMENT '分享令牌',
    share_type VARCHAR(20) NOT NULL COMMENT '分享类型：image, album',
    resource_id BIGINT UNSIGNED NOT NULL COMMENT '资源ID',
    owner_id BIGINT UNSIGNED NOT NULL COMMENT '分享者ID',
    
    -- 分享设置
    title VARCHAR(200) COMMENT '分享标题',
    description TEXT COMMENT '分享描述',
    password VARCHAR(100) COMMENT '访问密码（加密存储）',
    has_password BOOLEAN DEFAULT FALSE COMMENT '是否有密码',
    
    -- 权限设置
    permissions JSON COMMENT '权限列表',
    allow_guest BOOLEAN DEFAULT TRUE COMMENT '允许游客访问',
    
    -- 时效设置
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    max_views INT DEFAULT 0 COMMENT '最大访问次数（0为无限制）',
    view_count INT DEFAULT 0 COMMENT '当前访问次数',
    
    -- 状态
    status VARCHAR(20) DEFAULT 'active' COMMENT '分享状态：active, expired, disabled, revoked',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开（搜索引擎可索引）',
    
    -- 统计信息
    download_count INT DEFAULT 0 COMMENT '下载次数',
    comment_count INT DEFAULT 0 COMMENT '评论数量',
    like_count INT DEFAULT 0 COMMENT '点赞数量',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_share_token (share_token),
    INDEX idx_owner_id (owner_id),
    INDEX idx_resource (share_type, resource_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享表';

-- 分享访问记录表
CREATE TABLE IF NOT EXISTS share_access (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    share_id BIGINT UNSIGNED NOT NULL COMMENT '分享ID',
    user_id BIGINT UNSIGNED NULL COMMENT '访问用户ID（可为空，游客访问）',
    
    -- 访问信息
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    referer VARCHAR(500) COMMENT '来源页面',
    country VARCHAR(50) COMMENT '国家',
    city VARCHAR(100) COMMENT '城市',
    
    -- 访问行为
    action VARCHAR(50) COMMENT '操作类型：view, download, comment',
    duration INT DEFAULT 0 COMMENT '访问时长（秒）',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_share_id (share_id),
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (share_id) REFERENCES shares(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享访问记录表';

-- 分享协作者表
CREATE TABLE IF NOT EXISTS share_collaborators (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    share_id BIGINT UNSIGNED NOT NULL COMMENT '分享ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '协作者ID',
    
    -- 权限设置
    permissions JSON COMMENT '权限列表',
    role VARCHAR(50) DEFAULT 'viewer' COMMENT '角色：viewer, editor, admin',
    
    -- 邀请信息
    invited_by BIGINT UNSIGNED NOT NULL COMMENT '邀请者ID',
    invite_token VARCHAR(64) COMMENT '邀请令牌',
    accepted_at TIMESTAMP NULL COMMENT '接受时间',
    
    -- 状态
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态：pending, accepted, declined, removed',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_share_user (share_id, user_id),
    INDEX idx_invite_token (invite_token),
    INDEX idx_invited_by (invited_by),
    INDEX idx_status (status),
    
    FOREIGN KEY (share_id) REFERENCES shares(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享协作者表';

-- 评论表
CREATE TABLE IF NOT EXISTS comments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    share_id BIGINT UNSIGNED NOT NULL COMMENT '分享ID',
    user_id BIGINT UNSIGNED NULL COMMENT '评论用户ID（可为空，游客评论）',
    
    -- 评论内容
    content TEXT NOT NULL COMMENT '评论内容',
    author_name VARCHAR(100) COMMENT '作者名称（游客评论时使用）',
    author_email VARCHAR(200) COMMENT '作者邮箱（游客评论时使用）',
    
    -- 回复关系
    parent_id BIGINT UNSIGNED NULL COMMENT '父评论ID',
    
    -- 位置信息（图片标注）
    position_x DECIMAL(5,4) NULL COMMENT 'X坐标（相对位置 0-1）',
    position_y DECIMAL(5,4) NULL COMMENT 'Y坐标（相对位置 0-1）',
    
    -- 状态
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active, hidden, deleted',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_share_id (share_id),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (share_id) REFERENCES shares(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';

-- 评论点赞表
CREATE TABLE IF NOT EXISTS comment_likes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    comment_id BIGINT UNSIGNED NOT NULL COMMENT '评论ID',
    user_id BIGINT UNSIGNED NULL COMMENT '点赞用户ID',
    ip_address VARCHAR(45) COMMENT 'IP地址（游客点赞）',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_comment_id (comment_id),
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    
    FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论点赞表';

-- 分享点赞表
CREATE TABLE IF NOT EXISTS share_likes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    share_id BIGINT UNSIGNED NOT NULL COMMENT '分享ID',
    user_id BIGINT UNSIGNED NULL COMMENT '点赞用户ID',
    ip_address VARCHAR(45) COMMENT 'IP地址（游客点赞）',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_share_id (share_id),
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    
    FOREIGN KEY (share_id) REFERENCES shares(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享点赞表';

-- 创建触发器：自动更新过期分享状态
DELIMITER $$

CREATE TRIGGER update_expired_shares
BEFORE SELECT ON shares
FOR EACH ROW
BEGIN
    -- 这个触发器在MySQL中不能直接实现
    -- 需要通过定时任务或应用程序逻辑来处理
END$$

DELIMITER ;

-- 创建视图：分享统计视图
CREATE VIEW share_statistics AS
SELECT 
    s.id as share_id,
    s.share_token,
    s.title,
    s.view_count,
    s.download_count,
    s.comment_count,
    s.like_count,
    COUNT(DISTINCT sa.ip_address) as unique_visitors,
    COUNT(sa.id) as total_visits,
    MAX(sa.created_at) as last_visit,
    s.created_at as share_created_at
FROM shares s
LEFT JOIN share_access sa ON s.id = sa.share_id
WHERE s.status = 'active'
GROUP BY s.id;

-- 创建索引优化查询性能
CREATE INDEX idx_share_access_date ON share_access (share_id, DATE(created_at));
CREATE INDEX idx_comments_share_parent ON comments (share_id, parent_id, status);
CREATE INDEX idx_shares_owner_status ON shares (owner_id, status);

-- 插入示例数据（可选）
-- INSERT INTO shares (share_token, share_type, resource_id, owner_id, title, permissions, allow_guest) 
-- VALUES ('example_token_123', 'image', 1, 1, '示例分享', '["view", "comment"]', true);
