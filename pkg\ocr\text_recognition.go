package ocr

import (
	"fmt"
	"image"
	"image/color"
	"os"
	"regexp"
	"sort"
	"strings"
	"unicode"
)

// OCRResult OCR识别结果
type OCRResult struct {
	Text       string       `json:"text"`       // 识别的文本
	Confidence float64      `json:"confidence"` // 置信度 0-1
	Language   string       `json:"language"`   // 语言
	Regions    []TextRegion `json:"regions"`    // 文本区域
	Words      []Word       `json:"words"`      // 单词列表
	Lines      []Line       `json:"lines"`      // 行列表
}

// TextRegion 文本区域
type TextRegion struct {
	X          int     `json:"x"`
	Y          int     `json:"y"`
	Width      int     `json:"width"`
	Height     int     `json:"height"`
	Text       string  `json:"text"`
	Confidence float64 `json:"confidence"`
}

// Word 单词
type Word struct {
	Text       string  `json:"text"`
	X          int     `json:"x"`
	Y          int     `json:"y"`
	Width      int     `json:"width"`
	Height     int     `json:"height"`
	Confidence float64 `json:"confidence"`
}

// Line 文本行
type Line struct {
	Text       string  `json:"text"`
	X          int     `json:"x"`
	Y          int     `json:"y"`
	Width      int     `json:"width"`
	Height     int     `json:"height"`
	Confidence float64 `json:"confidence"`
	Words      []Word  `json:"words"`
}

// TextSearchResult 文本搜索结果
type TextSearchResult struct {
	ImageID    uint    `json:"image_id"`
	ImagePath  string  `json:"image_path"`
	Text       string  `json:"text"`
	Matches    []Match `json:"matches"`
	Confidence float64 `json:"confidence"`
}

// Match 匹配项
type Match struct {
	Text       string `json:"text"`
	StartIndex int    `json:"start_index"`
	EndIndex   int    `json:"end_index"`
	X          int    `json:"x"`
	Y          int    `json:"y"`
	Width      int    `json:"width"`
	Height     int    `json:"height"`
}

// OCREngine OCR引擎
type OCREngine struct {
	textCache map[string]*OCRResult // 文本缓存
}

// NewOCREngine 创建OCR引擎
func NewOCREngine() *OCREngine {
	return &OCREngine{
		textCache: make(map[string]*OCRResult),
	}
}

// RecognizeText 识别图片中的文字
func (oe *OCREngine) RecognizeText(imagePath string) (*OCRResult, error) {
	// 检查缓存
	if result, exists := oe.textCache[imagePath]; exists {
		return result, nil
	}

	// 打开图片
	file, err := os.Open(imagePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open image: %v", err)
	}
	defer file.Close()

	// 解码图片
	img, _, err := image.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %v", err)
	}

	// 预处理图片
	processedImg := oe.preprocessImage(img)

	// 简化的OCR实现（实际项目中应使用Tesseract等专业OCR库）
	result := oe.performSimpleOCR(processedImg)

	// 缓存结果
	oe.textCache[imagePath] = result

	return result, nil
}

// preprocessImage 预处理图片
func (oe *OCREngine) preprocessImage(img image.Image) image.Image {
	bounds := img.Bounds()

	// 转换为灰度图
	grayImg := image.NewGray(bounds)
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			grayColor := color.GrayModel.Convert(img.At(x, y))
			grayImg.Set(x, y, grayColor)
		}
	}

	// 二值化处理
	binaryImg := oe.binarizeImage(grayImg)

	// 降噪
	denoisedImg := oe.denoiseImage(binaryImg)

	return denoisedImg
}

// binarizeImage 二值化图片
func (oe *OCREngine) binarizeImage(img *image.Gray) *image.Gray {
	bounds := img.Bounds()

	// 计算阈值（Otsu算法简化版）
	threshold := oe.calculateThreshold(img)

	binaryImg := image.NewGray(bounds)
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			gray := img.GrayAt(x, y).Y
			if gray > threshold {
				binaryImg.Set(x, y, color.Gray{255})
			} else {
				binaryImg.Set(x, y, color.Gray{0})
			}
		}
	}

	return binaryImg
}

// calculateThreshold 计算二值化阈值
func (oe *OCREngine) calculateThreshold(img *image.Gray) uint8 {
	bounds := img.Bounds()
	histogram := make([]int, 256)

	// 计算直方图
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			gray := img.GrayAt(x, y).Y
			histogram[gray]++
		}
	}

	// 简化的Otsu算法
	totalPixels := bounds.Dx() * bounds.Dy()
	sum := 0
	for i := 0; i < 256; i++ {
		sum += i * histogram[i]
	}

	sumB := 0
	wB := 0
	maximum := 0.0
	threshold := 0

	for i := 0; i < 256; i++ {
		wB += histogram[i]
		if wB == 0 {
			continue
		}

		wF := totalPixels - wB
		if wF == 0 {
			break
		}

		sumB += i * histogram[i]
		mB := float64(sumB) / float64(wB)
		mF := float64(sum-sumB) / float64(wF)

		between := float64(wB) * float64(wF) * (mB - mF) * (mB - mF)

		if between > maximum {
			maximum = between
			threshold = i
		}
	}

	return uint8(threshold)
}

// denoiseImage 降噪处理
func (oe *OCREngine) denoiseImage(img *image.Gray) *image.Gray {
	bounds := img.Bounds()
	denoisedImg := image.NewGray(bounds)

	// 3x3中值滤波
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var pixels []uint8
			for dy := -1; dy <= 1; dy++ {
				for dx := -1; dx <= 1; dx++ {
					pixels = append(pixels, img.GrayAt(x+dx, y+dy).Y)
				}
			}

			sort.Slice(pixels, func(i, j int) bool {
				return pixels[i] < pixels[j]
			})

			median := pixels[4] // 中位数
			denoisedImg.Set(x, y, color.Gray{median})
		}
	}

	return denoisedImg
}

// performSimpleOCR 执行简单OCR识别
func (oe *OCREngine) performSimpleOCR(img image.Image) *OCRResult {
	// 这是一个简化的OCR实现
	// 实际项目中应该使用Tesseract、PaddleOCR等专业OCR库

	result := &OCRResult{
		Text:       "",
		Confidence: 0.0,
		Language:   "auto",
		Regions:    []TextRegion{},
		Words:      []Word{},
		Lines:      []Line{},
	}

	// 检测文本区域
	regions := oe.detectTextRegions(img)

	var allText strings.Builder
	totalConfidence := 0.0

	for _, region := range regions {
		// 简单的字符识别（实际应该使用训练好的模型）
		text := oe.recognizeRegionText(img, region)

		if text != "" {
			if allText.Len() > 0 {
				allText.WriteString(" ")
			}
			allText.WriteString(text)

			textRegion := TextRegion{
				X:          region.X,
				Y:          region.Y,
				Width:      region.Width,
				Height:     region.Height,
				Text:       text,
				Confidence: 0.8, // 模拟置信度
			}
			result.Regions = append(result.Regions, textRegion)

			// 分割单词
			words := oe.splitIntoWords(text, region)
			result.Words = append(result.Words, words...)

			totalConfidence += 0.8
		}
	}

	result.Text = allText.String()
	if len(result.Regions) > 0 {
		result.Confidence = totalConfidence / float64(len(result.Regions))
	}

	// 检测语言
	result.Language = oe.detectLanguage(result.Text)

	return result
}

// detectTextRegions 检测文本区域
func (oe *OCREngine) detectTextRegions(img image.Image) []TextRegion {
	bounds := img.Bounds()
	var regions []TextRegion

	// 简化的文本区域检测
	// 实际应该使用EAST、CRAFT等文本检测算法

	// 扫描图片寻找可能的文本区域
	blockSize := 50
	for y := bounds.Min.Y; y < bounds.Max.Y-blockSize; y += blockSize / 2 {
		for x := bounds.Min.X; x < bounds.Max.X-blockSize; x += blockSize / 2 {
			if oe.isTextRegion(img, x, y, blockSize, blockSize) {
				regions = append(regions, TextRegion{
					X:      x,
					Y:      y,
					Width:  blockSize,
					Height: blockSize,
				})
			}
		}
	}

	// 合并重叠区域
	regions = oe.mergeOverlappingRegions(regions)

	return regions
}

// isTextRegion 判断是否为文本区域
func (oe *OCREngine) isTextRegion(img image.Image, x, y, width, height int) bool {
	bounds := img.Bounds()

	// 检查边界
	if x+width > bounds.Max.X || y+height > bounds.Max.Y {
		return false
	}

	// 计算区域内的边缘密度
	edgeCount := 0
	totalPixels := 0

	for dy := 0; dy < height; dy++ {
		for dx := 0; dx < width; dx++ {
			if x+dx < bounds.Max.X-1 && y+dy < bounds.Max.Y-1 {
				// 简单的边缘检测
				current := oe.getGrayValue(img.At(x+dx, y+dy))
				right := oe.getGrayValue(img.At(x+dx+1, y+dy))
				bottom := oe.getGrayValue(img.At(x+dx, y+dy+1))

				if abs(current-right) > 30 || abs(current-bottom) > 30 {
					edgeCount++
				}
				totalPixels++
			}
		}
	}

	// 文本区域通常有较高的边缘密度
	edgeDensity := float64(edgeCount) / float64(totalPixels)
	return edgeDensity > 0.1 && edgeDensity < 0.8
}

// recognizeRegionText 识别区域文本
func (oe *OCREngine) recognizeRegionText(img image.Image, region TextRegion) string {
	// 这里应该使用训练好的OCR模型
	// 现在只是返回模拟的文本

	// 简单的模式匹配
	patterns := []string{
		"Hello", "World", "Text", "Image", "Photo", "Picture",
		"Document", "Page", "Title", "Content", "Message",
		"你好", "世界", "文本", "图片", "照片", "文档",
	}

	// 随机返回一个模式（实际应该基于图像特征）
	if len(patterns) > 0 {
		hash := (region.X + region.Y) % len(patterns)
		return patterns[hash]
	}

	return ""
}

// splitIntoWords 分割单词
func (oe *OCREngine) splitIntoWords(text string, region TextRegion) []Word {
	words := strings.Fields(text)
	var result []Word

	wordWidth := region.Width / len(words)
	for i, word := range words {
		result = append(result, Word{
			Text:       word,
			X:          region.X + i*wordWidth,
			Y:          region.Y,
			Width:      wordWidth,
			Height:     region.Height,
			Confidence: 0.8,
		})
	}

	return result
}

// detectLanguage 检测语言
func (oe *OCREngine) detectLanguage(text string) string {
	if text == "" {
		return "unknown"
	}

	// 简单的语言检测
	chineseCount := 0
	englishCount := 0
	totalChars := 0

	for _, r := range text {
		if unicode.Is(unicode.Han, r) {
			chineseCount++
		} else if unicode.IsLetter(r) {
			englishCount++
		}
		totalChars++
	}

	if chineseCount > englishCount {
		return "zh"
	} else if englishCount > 0 {
		return "en"
	}

	return "auto"
}

// SearchTextInImages 在图片中搜索文本
func (oe *OCREngine) SearchTextInImages(query string, imagePaths []string) ([]TextSearchResult, error) {
	var results []TextSearchResult
	query = strings.ToLower(strings.TrimSpace(query))

	if query == "" {
		return results, nil
	}

	for i, imagePath := range imagePaths {
		ocrResult, err := oe.RecognizeText(imagePath)
		if err != nil {
			continue
		}

		text := strings.ToLower(ocrResult.Text)
		if strings.Contains(text, query) {
			matches := oe.findMatches(query, ocrResult)

			if len(matches) > 0 {
				results = append(results, TextSearchResult{
					ImageID:    uint(i + 1), // 临时ID
					ImagePath:  imagePath,
					Text:       ocrResult.Text,
					Matches:    matches,
					Confidence: ocrResult.Confidence,
				})
			}
		}
	}

	return results, nil
}

// findMatches 查找匹配项
func (oe *OCREngine) findMatches(query string, ocrResult *OCRResult) []Match {
	var matches []Match
	text := strings.ToLower(ocrResult.Text)
	query = strings.ToLower(query)

	// 使用正则表达式查找所有匹配
	re := regexp.MustCompile(regexp.QuoteMeta(query))
	indices := re.FindAllStringIndex(text, -1)

	for _, index := range indices {
		// 尝试找到对应的区域
		region := oe.findRegionForIndex(index[0], ocrResult)

		matches = append(matches, Match{
			Text:       query,
			StartIndex: index[0],
			EndIndex:   index[1],
			X:          region.X,
			Y:          region.Y,
			Width:      region.Width,
			Height:     region.Height,
		})
	}

	return matches
}

// findRegionForIndex 根据文本索引查找对应区域
func (oe *OCREngine) findRegionForIndex(index int, ocrResult *OCRResult) TextRegion {
	// 简化实现：返回第一个区域
	if len(ocrResult.Regions) > 0 {
		return ocrResult.Regions[0]
	}

	return TextRegion{X: 0, Y: 0, Width: 100, Height: 20}
}

// 辅助函数
func (oe *OCREngine) getGrayValue(c color.Color) int {
	r, g, b, _ := c.RGBA()
	// 转换为灰度值
	gray := (299*r + 587*g + 114*b) / 1000
	return int(gray >> 8)
}

func (oe *OCREngine) mergeOverlappingRegions(regions []TextRegion) []TextRegion {
	if len(regions) <= 1 {
		return regions
	}

	var merged []TextRegion
	used := make([]bool, len(regions))

	for i, region1 := range regions {
		if used[i] {
			continue
		}

		merged = append(merged, region1)
		used[i] = true

		// 查找重叠区域
		for j := i + 1; j < len(regions); j++ {
			if used[j] {
				continue
			}

			region2 := regions[j]
			if oe.regionsOverlap(region1, region2) {
				// 合并区域
				lastIndex := len(merged) - 1
				merged[lastIndex] = oe.mergeRegions(merged[lastIndex], region2)
				used[j] = true
			}
		}
	}

	return merged
}

func (oe *OCREngine) regionsOverlap(r1, r2 TextRegion) bool {
	return !(r1.X+r1.Width < r2.X || r2.X+r2.Width < r1.X ||
		r1.Y+r1.Height < r2.Y || r2.Y+r2.Height < r1.Y)
}

func (oe *OCREngine) mergeRegions(r1, r2 TextRegion) TextRegion {
	minX := min(r1.X, r2.X)
	minY := min(r1.Y, r2.Y)
	maxX := max(r1.X+r1.Width, r2.X+r2.Width)
	maxY := max(r1.Y+r1.Height, r2.Y+r2.Height)

	return TextRegion{
		X:      minX,
		Y:      minY,
		Width:  maxX - minX,
		Height: maxY - minY,
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func abs(a int) int {
	if a < 0 {
		return -a
	}
	return a
}
