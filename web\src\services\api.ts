import axios, { type AxiosInstance } from 'axios';
import type { LoginRequest, LoginResponse, RegisterRequest, RegisterResponse, User, UserSettings, UserSettingsRequest } from '../types/auth';
import type { Image, UploadResponse, Album, AlbumCreateRequest, AlbumUpdateRequest, MoveImagesRequest } from '../types/image';

// 创建 axios 实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从本地存储中获取 token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // token 过期或无效，清除本地存储并跳转到登录页
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// API 方法定义
export default {
  // Auth 相关API
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    const response = await api.post('/register', data);
    // 后端返回格式: { code: 200, message: "注册成功", data: { id: ..., username: ..., email: ... } }
    return response.data.data;
  },

  async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await api.post('/login', {
      email: data.email,
      password: data.password
    });
    // 后端返回格式: { code: 200, message: "操作成功", data: { user: ..., token: ... } }
    return response.data.data;
  },

  async logout(): Promise<void> {
    await api.post('/logout');
  },

  // 图片相关API
  async uploadImage(file: File, albumId?: number): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('image', file);
    if (albumId) {
      formData.append('album_id', albumId.toString());
    }

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };

    const response = await api.post('/upload', formData, config);
    return response.data.data;
  },

  async getImages(albumId?: number | 'uncategorized'): Promise<Image[]> {
    let url = '/images';
    if (albumId !== undefined) {
      url += `?album_id=${albumId}`;
    }
    const response = await api.get(url);
    return response.data.data;
  },

  async deleteImage(id: string | number): Promise<void> {
    await api.delete(`/images/${id}`);
  },

  async moveImageToAlbum(imageId: number, albumId?: number): Promise<void> {
    await api.put(`/images/${imageId}/move`, { album_id: albumId });
  },

  // 相册相关API
  async createAlbum(data: AlbumCreateRequest): Promise<Album> {
    const response = await api.post('/albums', data);
    return response.data.data.album;
  },

  async getAlbums(): Promise<Album[]> {
    const response = await api.get('/albums');
    return response.data.data.albums;
  },

  async getAlbum(id: number): Promise<Album> {
    const response = await api.get(`/albums/${id}`);
    return response.data.data.album;
  },

  async updateAlbum(id: number, data: AlbumUpdateRequest): Promise<Album> {
    const response = await api.put(`/albums/${id}`, data);
    return response.data.data.album;
  },

  async deleteAlbum(id: number): Promise<void> {
    await api.delete(`/albums/${id}`);
  },

  async getAlbumImages(id: number | 'uncategorized'): Promise<Image[]> {
    const response = await api.get<{ images: Image[] }>(`/albums/${id}/images`);
    return response.data.images;
  },

  async moveImagesToAlbum(data: MoveImagesRequest): Promise<void> {
    await api.post('/albums/move-images', data);
  },

  // 用户设置相关API
  async getUserProfile(): Promise<User> {
    const response = await api.get<{ user: User }>('/user/profile');
    return response.data.user;
  },

  async getUserSettings(): Promise<UserSettings> {
    const response = await api.get<{ user: UserSettings }>('/user/settings');
    return response.data.user;
  },

  async updateUserSettings(data: UserSettingsRequest): Promise<void> {
    await api.put('/user/settings', data);
  },

  // 健康检查API
  async healthCheck(): Promise<any> {
    const response = await api.get('/health');
    return response.data;
  },

  // 存储配额管理API
  async getUserStorageInfo(): Promise<any> {
    const response = await api.get('/user/storage');
    return response.data;
  },

  async getAllUsersStorageInfo(): Promise<any> {
    const response = await api.get('/admin/storage');
    return response.data;
  },

  async getUserStorageInfoById(userId: number): Promise<any> {
    const response = await api.get(`/admin/storage/${userId}`);
    return response.data;
  },

  async updateUserStorageQuota(userId: number, quota: number): Promise<any> {
    const response = await api.put(`/admin/storage/${userId}/quota`, {
      storage_quota: quota
    });
    return response.data;
  },

  async recalculateUserStorage(userId: number): Promise<any> {
    const response = await api.post(`/admin/storage/${userId}/recalculate`);
    return response.data;
  },

  async recalculateAllUsersStorage(): Promise<any> {
    const response = await api.post('/admin/storage/quota/recalculate-all');
    return response.data;
  },

  // 存储管理API
  async getStorageConfigs(): Promise<any> {
    const response = await api.get('/admin/storage/configs');
    return response.data;
  },

  async getStorageConfigById(id: number): Promise<any> {
    const response = await api.get(`/admin/storage/configs/${id}`);
    return response.data;
  },

  async createStorageConfig(config: any): Promise<any> {
    const response = await api.post('/admin/storage/configs', config);
    return response.data;
  },

  async updateStorageConfig(id: number, config: any): Promise<any> {
    const response = await api.put(`/admin/storage/configs/${id}`, config);
    return response.data;
  },

  async deleteStorageConfig(id: number): Promise<any> {
    const response = await api.delete(`/admin/storage/configs/${id}`);
    return response.data;
  },

  async setDefaultStorageConfig(id: number): Promise<any> {
    const response = await api.post(`/admin/storage/configs/${id}/default`);
    return response.data;
  },

  async toggleStorageConfigStatus(id: number): Promise<any> {
    const response = await api.post(`/admin/storage/configs/${id}/toggle`);
    return response.data;
  },

  async testStorageConfig(config: any): Promise<any> {
    const response = await api.post('/admin/storage/test', config);
    return response.data;
  },

  async getStorageStats(): Promise<any> {
    const response = await api.get('/admin/storage/stats');
    return response.data;
  },

  // 用户管理API
  async getAllUsersWithStats(): Promise<any> {
    const response = await api.get('/admin/users/stats');
    return response.data;
  },

  // 用户组管理API
  async getAllGroups(): Promise<any> {
    const response = await api.get('/admin/groups');
    return response.data;
  },

  async getGroup(id: number): Promise<any> {
    const response = await api.get(`/admin/groups/${id}`);
    return response.data;
  },

  async createGroup(data: any): Promise<any> {
    const response = await api.post('/admin/groups', data);
    return response.data;
  },

  async updateGroup(id: number, data: any): Promise<any> {
    const response = await api.put(`/admin/groups/${id}`, data);
    return response.data;
  },

  async deleteGroup(id: number): Promise<any> {
    const response = await api.delete(`/admin/groups/${id}`);
    return response.data;
  },

  async getGroupStats(): Promise<any> {
    const response = await api.get('/admin/groups/stats');
    return response.data;
  },

  // 用户组成员管理API
  async getGroupMembers(id: number): Promise<any> {
    const response = await api.get(`/admin/groups/${id}/members`);
    return response.data;
  },

  async addUsersToGroup(id: number, userIds: number[]): Promise<any> {
    const response = await api.post(`/admin/groups/${id}/members`, { user_ids: userIds });
    return response.data;
  },

  async removeUserFromGroup(groupId: number, userId: number): Promise<any> {
    const response = await api.delete(`/admin/groups/${groupId}/members/${userId}`);
    return response.data;
  },

  // 用户组权限管理API
  async getGroupPermissions(id: number): Promise<any> {
    const response = await api.get(`/admin/groups/${id}/permissions`);
    return response.data;
  },

  async assignGroupPermissions(id: number, permissions: number[]): Promise<any> {
    const response = await api.post(`/admin/groups/${id}/permissions`, { permission_ids: permissions });
    return response.data;
  },

  // 默认用户组管理API
  async getDefaultGroup(): Promise<any> {
    const response = await api.get('/admin/groups/default');
    return response.data;
  },

  async setDefaultGroup(id: number): Promise<any> {
    const response = await api.post(`/admin/groups/${id}/set-default`);
    return response.data;
  },

  // 管理员图片管理API
  async getAllImagesAdmin(params?: any): Promise<any> {
    const response = await api.get('/admin/images', { params });
    return response.data;
  },

  async getImageStatsAdmin(): Promise<any> {
    const response = await api.get('/admin/images/stats');
    return response.data;
  },

  async getImageDetailAdmin(id: number): Promise<any> {
    const response = await api.get(`/admin/images/${id}`);
    return response.data;
  },

  async deleteImageAdmin(id: number): Promise<any> {
    const response = await api.delete(`/admin/images/${id}`);
    return response.data;
  },

  async bulkDeleteImagesAdmin(imageIds: number[]): Promise<any> {
    const response = await api.post('/admin/images/bulk-delete', { image_ids: imageIds });
    return response.data;
  },

  async getUsersForFilter(): Promise<any> {
    const response = await api.get('/admin/users-filter');
    return response.data;
  },

  async getAlbumsForFilter(): Promise<any> {
    const response = await api.get('/admin/albums-filter');
    return response.data;
  }
};