package dao

import (
	"encoding/json"
	"errors"
	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
)

// GetAllStorageConfigs 获取所有存储配置
func GetAllStorageConfigs() ([]models.StorageConfig, error) {
	var configs []models.StorageConfig
	err := database.DB.Order("is_default DESC, created_at DESC").Find(&configs).Error
	return configs, err
}

// GetStorageConfigByID 根据ID获取存储配置
func GetStorageConfigByID(id uint) (*models.StorageConfig, error) {
	var config models.StorageConfig
	err := database.DB.Where("id = ?", id).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// GetDefaultStorageConfig 获取默认存储配置
func GetDefaultStorageConfig() (*models.StorageConfig, error) {
	var config models.StorageConfig
	err := database.DB.Where("is_default = ? AND is_enabled = ?", true, true).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// GetEnabledStorageConfigs 获取启用的存储配置
func GetEnabledStorageConfigs() ([]models.StorageConfig, error) {
	var configs []models.StorageConfig
	err := database.DB.Where("is_enabled = ?", true).Order("is_default DESC, created_at DESC").Find(&configs).Error
	return configs, err
}

// CreateStorageConfig 创建存储配置
func CreateStorageConfig(config *models.StorageConfig) error {
	// 如果设置为默认配置，先取消其他默认配置
	if config.IsDefault {
		err := database.DB.Model(&models.StorageConfig{}).
			Where("is_default = ?", true).
			Update("is_default", false).Error
		if err != nil {
			return err
		}
	}

	return database.DB.Create(config).Error
}

// UpdateStorageConfig 更新存储配置
func UpdateStorageConfig(id uint, config *models.StorageConfig) error {
	// 如果设置为默认配置，先取消其他默认配置
	if config.IsDefault {
		err := database.DB.Model(&models.StorageConfig{}).
			Where("is_default = ? AND id != ?", true, id).
			Update("is_default", false).Error
		if err != nil {
			return err
		}
	}

	return database.DB.Where("id = ?", id).Updates(config).Error
}

// DeleteStorageConfig 删除存储配置
func DeleteStorageConfig(id uint) error {
	// 检查是否为默认配置
	var config models.StorageConfig
	err := database.DB.Where("id = ?", id).First(&config).Error
	if err != nil {
		return err
	}

	if config.IsDefault {
		return errors.New("Cannot delete default storage config")
	}

	return database.DB.Delete(&models.StorageConfig{}, id).Error
}

// SetDefaultStorageConfig 设置默认存储配置
func SetDefaultStorageConfig(id uint) error {
	tx := database.DB.Begin()

	// 取消所有默认配置
	err := tx.Model(&models.StorageConfig{}).
		Where("is_default = ?", true).
		Update("is_default", false).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 设置新的默认配置
	err = tx.Model(&models.StorageConfig{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_default": true,
			"is_enabled": true,
		}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// ToggleStorageConfigStatus 切换存储配置状态
func ToggleStorageConfigStatus(id uint) error {
	var config models.StorageConfig
	err := database.DB.Where("id = ?", id).First(&config).Error
	if err != nil {
		return err
	}

	// 如果是默认配置，不能禁用
	if config.IsDefault && config.IsEnabled {
		return errors.New("Cannot disable default storage config")
	}

	return database.DB.Model(&config).Update("is_enabled", !config.IsEnabled).Error
}

// ParseStorageConfig 解析存储配置
func ParseStorageConfig(config *models.StorageConfig) (*models.StorageConfigDetail, error) {
	detail := &models.StorageConfigDetail{
		StorageConfig: *config,
	}

	if config.Config == "" {
		return detail, nil
	}

	var parsedConfig interface{}
	switch config.Provider {
	case models.StorageProviderLocal:
		var localConfig models.LocalStorageConfig
		err := json.Unmarshal([]byte(config.Config), &localConfig)
		if err != nil {
			return nil, err
		}
		parsedConfig = localConfig
	case models.StorageProviderAliOSS:
		var aliConfig models.AliOSSConfig
		err := json.Unmarshal([]byte(config.Config), &aliConfig)
		if err != nil {
			return nil, err
		}
		parsedConfig = aliConfig
	case models.StorageProviderTencentCOS:
		var tencentConfig models.TencentCOSConfig
		err := json.Unmarshal([]byte(config.Config), &tencentConfig)
		if err != nil {
			return nil, err
		}
		parsedConfig = tencentConfig
	case models.StorageProviderQiniuKodo:
		var qiniuConfig models.QiniuKodoConfig
		err := json.Unmarshal([]byte(config.Config), &qiniuConfig)
		if err != nil {
			return nil, err
		}
		parsedConfig = qiniuConfig
	case models.StorageProviderAWSS3:
		var awsConfig models.AWSS3Config
		err := json.Unmarshal([]byte(config.Config), &awsConfig)
		if err != nil {
			return nil, err
		}
		parsedConfig = awsConfig
	case models.StorageProviderFTP:
		var ftpConfig models.FTPConfig
		err := json.Unmarshal([]byte(config.Config), &ftpConfig)
		if err != nil {
			return nil, err
		}
		parsedConfig = ftpConfig
	case models.StorageProviderSFTP:
		var sftpConfig models.SFTPConfig
		err := json.Unmarshal([]byte(config.Config), &sftpConfig)
		if err != nil {
			return nil, err
		}
		parsedConfig = sftpConfig
	default:
		// 未知类型，保持原始JSON
		err := json.Unmarshal([]byte(config.Config), &parsedConfig)
		if err != nil {
			return nil, err
		}
	}

	detail.ParsedConfig = parsedConfig
	return detail, nil
}

// GetStorageStats 获取存储统计信息
func GetStorageStats() (*models.StorageStats, error) {
	var stats models.StorageStats

	// 统计文件数量
	err := database.DB.Model(&models.Image{}).Count(&stats.TotalFiles).Error
	if err != nil {
		return nil, err
	}

	// 统计总大小
	err = database.DB.Model(&models.Image{}).
		Select("COALESCE(SUM(size), 0)").Scan(&stats.TotalSize).Error
	if err != nil {
		return nil, err
	}

	// 统计已使用配额
	err = database.DB.Model(&models.User{}).
		Select("COALESCE(SUM(storage_used), 0)").Scan(&stats.UsedQuota).Error
	if err != nil {
		return nil, err
	}

	// 统计总配额
	err = database.DB.Model(&models.User{}).
		Select("COALESCE(SUM(storage_quota), 0)").Scan(&stats.TotalQuota).Error
	if err != nil {
		return nil, err
	}

	return &stats, nil
}
