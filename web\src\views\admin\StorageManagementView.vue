<template>
  <div class="storage-management">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2><i class="bi bi-hdd-stack"></i> 存储管理</h2>
          <p class="page-subtitle">管理存储配置、提供商和访问设置</p>
        </div>
        <div class="header-right">
          <button class="btn btn-secondary" @click="loadStorageConfigs">
            <i class="bi bi-arrow-clockwise"></i> 刷新
          </button>
          <button class="btn btn-primary" @click="showCreateModal">
            <i class="bi bi-plus"></i> 添加配置
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-if="stats">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-files"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total_files }}</div>
          <div class="stat-label">总文件数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-hdd"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatBytes(stats.total_size) }}</div>
          <div class="stat-label">总存储大小</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-speedometer2"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatBytes(stats.used_quota) }}</div>
          <div class="stat-label">已用配额</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-hdd-stack"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatBytes(stats.total_quota) }}</div>
          <div class="stat-label">总配额</div>
        </div>
      </div>
    </div>

    <!-- 存储配置列表 -->
    <div class="storage-configs-container">
      <div class="configs-header">
        <h3>存储配置</h3>
        <div class="configs-actions">
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索配置..." 
            class="search-input"
          />
        </div>
      </div>

      <div class="configs-grid">
        <div 
          v-for="config in filteredConfigs" 
          :key="config.id"
          class="config-card"
          :class="{ 
            'default': config.is_default, 
            'disabled': !config.is_enabled 
          }"
        >
          <div class="config-header">
            <div class="config-info">
              <div class="config-icon">
                <i class="bi" :class="getProviderInfo(config.provider).icon"></i>
              </div>
              <div class="config-details">
                <h4 class="config-name">{{ config.name }}</h4>
                <p class="config-provider">{{ getProviderInfo(config.provider).label }}</p>
              </div>
            </div>
            <div class="config-badges">
              <span v-if="config.is_default" class="badge badge-primary">默认</span>
              <span v-if="!config.is_enabled" class="badge badge-secondary">已禁用</span>
            </div>
          </div>

          <div class="config-body">
            <div class="config-item">
              <label>访问域名</label>
              <div class="config-value">{{ config.access_url || '未设置' }}</div>
            </div>
            <div class="config-item">
              <label>存储路径</label>
              <div class="config-value">{{ config.storage_path || '未设置' }}</div>
            </div>
            <div class="config-item">
              <label>创建时间</label>
              <div class="config-value">{{ formatDate(config.created_at) }}</div>
            </div>
          </div>

          <div class="config-actions">
            <button 
              class="btn btn-sm btn-outline-primary" 
              @click="testConfig(config)"
              :disabled="testing === config.id"
            >
              <i class="bi" :class="testing === config.id ? 'bi-arrow-clockwise spinning' : 'bi-check-circle'"></i>
              测试
            </button>
            <button 
              class="btn btn-sm btn-outline-secondary" 
              @click="editConfig(config)"
            >
              <i class="bi bi-pencil"></i>
              编辑
            </button>
            <button 
              v-if="!config.is_default"
              class="btn btn-sm btn-outline-success" 
              @click="setDefault(config.id)"
            >
              <i class="bi bi-star"></i>
              设为默认
            </button>
            <button 
              class="btn btn-sm" 
              :class="config.is_enabled ? 'btn-outline-warning' : 'btn-outline-success'"
              @click="toggleStatus(config.id)"
            >
              <i class="bi" :class="config.is_enabled ? 'bi-pause' : 'bi-play'"></i>
              {{ config.is_enabled ? '禁用' : '启用' }}
            </button>
            <button 
              v-if="!config.is_default"
              class="btn btn-sm btn-outline-danger" 
              @click="deleteConfig(config.id)"
            >
              <i class="bi bi-trash"></i>
              删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑配置模态框 -->
    <StorageConfigModal
      v-if="showModal"
      :config="editingConfig"
      @close="closeModal"
      @save="handleSave"
    />

    <!-- 通知组件 -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      <i class="bi" :class="notification.type === 'success' ? 'bi-check-circle' : 'bi-exclamation-circle'"></i>
      <span>{{ notification.message }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import api from '../../services/api';
import StorageConfigModal from '../../components/StorageConfigModal.vue';
import type { StorageConfig, StorageStats } from '../../types/storage-management';
import { getStorageProviderInfo } from '../../types/storage-management';
import { formatBytes } from '../../types/storage';
import { useDialog } from '../../utils/dialog';

// 响应式数据
const loading = ref(false);
const configs = ref<StorageConfig[]>([]);
const stats = ref<StorageStats | null>(null);
const { confirmDelete, notify } = useDialog();
const searchQuery = ref('');
const showModal = ref(false);
const editingConfig = ref<StorageConfig | null>(null);
const testing = ref<number | null>(null);

const notification = reactive({
  show: false,
  message: '',
  type: 'success' as 'success' | 'error'
});

// 计算属性
const filteredConfigs = computed(() => {
  if (!searchQuery.value) return configs.value;
  const query = searchQuery.value.toLowerCase();
  return configs.value.filter(config => 
    config.name.toLowerCase().includes(query) || 
    getProviderInfo(config.provider).label.toLowerCase().includes(query)
  );
});

// 生命周期
onMounted(() => {
  loadStorageConfigs();
  loadStorageStats();
});

// 方法
const loadStorageConfigs = async () => {
  loading.value = true;
  try {
    const response = await api.getStorageConfigs();
    configs.value = response.configs || [];
  } catch (error) {
    showNotification('加载存储配置失败', 'error');
  } finally {
    loading.value = false;
  }
};

const loadStorageStats = async () => {
  try {
    const response = await api.getStorageStats();
    stats.value = response;
  } catch (error) {
    console.error('Failed to load storage stats:', error);
  }
};

const showCreateModal = () => {
  editingConfig.value = null;
  showModal.value = true;
};

const editConfig = (config: StorageConfig) => {
  editingConfig.value = config;
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
  editingConfig.value = null;
};

const handleSave = async () => {
  await loadStorageConfigs();
  closeModal();
  showNotification('存储配置保存成功');
};

const testConfig = async (config: StorageConfig) => {
  testing.value = config.id;
  try {
    const response = await api.testStorageConfig({
      provider: config.provider,
      config: config.config
    });
    
    if (response.success) {
      showNotification('存储配置测试成功');
    } else {
      showNotification(`测试失败: ${response.message}`, 'error');
    }
  } catch (error) {
    showNotification('测试连接失败', 'error');
  } finally {
    testing.value = null;
  }
};

const setDefault = async (id: number) => {
  try {
    await api.setDefaultStorageConfig(id);
    showNotification('默认存储配置设置成功');
    loadStorageConfigs();
  } catch (error) {
    showNotification('设置默认配置失败', 'error');
  }
};

const toggleStatus = async (id: number) => {
  try {
    await api.toggleStorageConfigStatus(id);
    showNotification('存储配置状态切换成功');
    loadStorageConfigs();
  } catch (error) {
    showNotification('切换状态失败', 'error');
  }
};

const deleteConfig = async (id: number) => {
  const config = configs.value.find(c => c.id === id);
  const configName = config?.name || '存储配置';

  const confirmed = await confirmDelete(configName, {
    details: '删除后将无法恢复，请确认操作。'
  });

  if (!confirmed) return;

  try {
    await api.deleteStorageConfig(id);
    showNotification('存储配置删除成功');
    loadStorageConfigs();
  } catch (error) {
    showNotification('删除配置失败', 'error');
  }
};

const getProviderInfo = getStorageProviderInfo;

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN');
};



const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  notification.message = message;
  notification.type = type;
  notification.show = true;
  setTimeout(() => {
    notification.show = false;
  }, 3000);
};
</script>

<style scoped>
.storage-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.header-left {
  flex: 1;
}

.header-right {
  flex-shrink: 0;
  display: flex;
  gap: 10px;
}

.page-header h2 {
  color: var(--text-color);
  margin-bottom: 8px;
}

.page-subtitle {
  color: var(--text-secondary);
  margin: 0;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  border: 1px solid var(--border-color);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #007bff;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 配置容器 */
.storage-configs-container {
  background: var(--card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.configs-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.configs-header h3 {
  margin: 0;
  color: var(--text-color);
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 200px;
}

/* 配置网格 */
.configs-grid {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.config-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s;
  background: white;
}

.config-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.config-card.default {
  border-color: #007bff;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.config-card.disabled {
  opacity: 0.6;
  background: #f8f9fa;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.config-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #007bff;
}

.config-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.config-provider {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.config-badges {
  display: flex;
  gap: 5px;
}

.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge-primary {
  background: #007bff;
  color: white;
}

.badge-secondary {
  background: #6c757d;
  color: white;
}

.config-body {
  margin-bottom: 15px;
}

.config-item {
  margin-bottom: 10px;
}

.config-item label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  margin-bottom: 2px;
  text-transform: uppercase;
}

.config-value {
  font-size: 14px;
  color: #333;
  word-break: break-all;
}

.config-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-outline-primary {
  border: 1px solid #007bff;
  color: #007bff;
  background: transparent;
}

.btn-outline-primary:hover {
  background: #007bff;
  color: white;
}

.btn-outline-secondary {
  border: 1px solid #6c757d;
  color: #6c757d;
  background: transparent;
}

.btn-outline-secondary:hover {
  background: #6c757d;
  color: white;
}

.btn-outline-success {
  border: 1px solid #28a745;
  color: #28a745;
  background: transparent;
}

.btn-outline-success:hover {
  background: #28a745;
  color: white;
}

.btn-outline-warning {
  border: 1px solid #ffc107;
  color: #ffc107;
  background: transparent;
}

.btn-outline-warning:hover {
  background: #ffc107;
  color: #212529;
}

.btn-outline-danger {
  border: 1px solid #dc3545;
  color: #dc3545;
  background: transparent;
}

.btn-outline-danger:hover {
  background: #dc3545;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-weight: 500;
  z-index: 1001;
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification.success {
  background: #28a745;
}

.notification.error {
  background: #dc3545;
}
/* 在样式中增加CSS变量 */
:root {
  --card-bg: #fff;
  --text-color: #333;
  --border-color: #e9ecef;
}

[data-theme="dark"] {
  --card-bg: #2c2c2c;
  --text-color: #f0f0f0;
  --border-color: #444;
}


</style>
