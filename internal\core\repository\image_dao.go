package dao

import (
	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
	"cloudbed/pkg/pagination"
)

// CreateImage 创建新图片记录
func CreateImage(image *models.Image) error {
	return database.DB.Create(image).Error
}

// GetImagesByUserID 根据用户ID获取图片列表
func GetImagesByUserID(userID uint) ([]models.Image, error) {
	var images []models.Image
	err := database.DB.Preload("User").Preload("User.Role").Preload("Album").Where("user_id = ?", userID).Find(&images).Error
	return images, err
}

// GetImageByID 根据ID获取图片
func GetImageByID(id uint) (*models.Image, error) {
	var image models.Image
	err := database.DB.First(&image, id).Error
	return &image, err
}

// GetImageByName 根据名称获取图片
func GetImageByName(name string) (*models.Image, error) {
	var image models.Image
	err := database.DB.Where("name = ?", name).First(&image).Error
	return &image, err
}

// GetImagesByIDs 根据ID列表获取图片
func GetImagesByIDs(ids []uint) ([]models.Image, error) {
	var images []models.Image
	err := database.DB.Where("id IN ?", ids).Find(&images).Error
	return images, err
}

// DeleteImage 删除图片记录
func DeleteImage(id uint) error {
	return database.DB.Delete(&models.Image{}, id).Error
}

// DeleteImageByName 根据名称删除图片记录
func DeleteImageByName(name string) error {
	return database.DB.Where("name = ?", name).Delete(&models.Image{}).Error
}

// GetImagesByAlbumID 根据相册ID获取图片列表
func GetImagesByAlbumID(albumID uint) ([]models.Image, error) {
	var images []models.Image
	err := database.DB.Where("album_id = ?", albumID).Find(&images).Error
	return images, err
}

// GetImagesByUserIDAndAlbumID 根据用户ID和相册ID获取图片列表
func GetImagesByUserIDAndAlbumID(userID uint, albumID *uint) ([]models.Image, error) {
	var images []models.Image
	query := database.DB.Preload("User").Preload("User.Role").Preload("Album").Where("user_id = ?", userID)

	if albumID == nil {
		// 获取未分类的图片（没有相册的图片）
		query = query.Where("album_id IS NULL")
	} else {
		// 获取指定相册的图片
		query = query.Where("album_id = ?", *albumID)
	}

	err := query.Preload("Album").Find(&images).Error
	return images, err
}

// MoveImageToAlbum 将图片移动到指定相册
func MoveImageToAlbum(imageID uint, albumID *uint) error {
	return database.DB.Model(&models.Image{}).
		Where("id = ?", imageID).
		Update("album_id", albumID).Error
}

// MoveImagesByIDsToAlbum 批量将图片移动到指定相册
func MoveImagesByIDsToAlbum(imageIDs []uint, albumID *uint) error {
	return database.DB.Model(&models.Image{}).
		Where("id IN ?", imageIDs).
		Update("album_id", albumID).Error
}

// GetUncategorizedImagesByUserID 获取用户的未分类图片
func GetUncategorizedImagesByUserID(userID uint) ([]models.Image, error) {
	var images []models.Image
	err := database.DB.Preload("User").Preload("User.Role").Preload("Album").Where("user_id = ? AND album_id IS NULL", userID).Find(&images).Error
	return images, err
}

// GetImagesByUserIDPaginated 分页获取用户图片列表
func GetImagesByUserIDPaginated(userID uint, req pagination.ImagePaginationRequest) (pagination.Response, error) {
	var images []models.Image

	query := database.DB.Model(&models.Image{}).Where("user_id = ?", userID)

	// 应用相册过滤
	if req.AlbumID != nil {
		query = query.Where("album_id = ?", *req.AlbumID)
	}

	// 应用搜索过滤
	if req.Search != "" {
		query = query.Where("name LIKE ?", "%"+req.Search+"%")
	}

	// 使用分页工具进行查询
	return pagination.PaginateWithPreload(query, req.Request, &images, "created_at DESC", "User", "User.Role", "Album")
}

// GetUncategorizedImagesByUserIDPaginated 分页获取用户的未分类图片
func GetUncategorizedImagesByUserIDPaginated(userID uint, req pagination.Request) (pagination.Response, error) {
	var images []models.Image

	query := database.DB.Model(&models.Image{}).Where("user_id = ? AND album_id IS NULL", userID)

	// 使用分页工具进行查询
	return pagination.PaginateWithPreload(query, req, &images, "created_at DESC", "User", "User.Role", "Album")
}

// GetImageByIDAndUserID 根据ID和用户ID获取图片（确保用户权限）
func GetImageByIDAndUserID(id, userID uint) (*models.Image, error) {
	var image models.Image
	err := database.DB.Where("id = ? AND user_id = ?", id, userID).
		Preload("Album").
		First(&image).Error
	return &image, err
}
