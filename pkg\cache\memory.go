package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// MemoryCache 内存缓存实现
type MemoryCache struct {
	data   map[string]*cacheItem
	mutex  sync.RWMutex
	prefix string
}

// cacheItem 缓存项
type cacheItem struct {
	value      []byte
	expiration time.Time
	hasExpiry  bool
}

// NewMemoryCache 创建内存缓存实例
func NewMemoryCache(prefix string) *MemoryCache {
	cache := &MemoryCache{
		data:   make(map[string]*cacheItem),
		prefix: prefix,
	}

	// 启动清理协程
	go cache.cleanup()

	return cache
}

// buildKey 构建缓存键
func (m *MemoryCache) buildKey(key string) string {
	if m.prefix != "" {
		return fmt.Sprintf("%s:%s", m.prefix, key)
	}
	return key
}

// cleanup 定期清理过期项
func (m *MemoryCache) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		m.mutex.Lock()
		now := time.Now()
		for key, item := range m.data {
			if item.hasExpiry && now.After(item.expiration) {
				delete(m.data, key)
			}
		}
		m.mutex.Unlock()
	}
}

// isExpired 检查是否过期
func (item *cacheItem) isExpired() bool {
	if !item.hasExpiry {
		return false
	}
	return time.Now().After(item.expiration)
}

// Set 设置缓存
func (m *MemoryCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %v", err)
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	item := &cacheItem{
		value: data,
	}

	if expiration > 0 {
		item.expiration = time.Now().Add(expiration)
		item.hasExpiry = true
	}

	m.data[m.buildKey(key)] = item
	return nil
}

// Get 获取缓存
func (m *MemoryCache) Get(ctx context.Context, key string, dest interface{}) error {
	m.mutex.RLock()
	item, exists := m.data[m.buildKey(key)]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("key not found")
	}

	if item.isExpired() {
		m.mutex.Lock()
		delete(m.data, m.buildKey(key))
		m.mutex.Unlock()
		return fmt.Errorf("key expired")
	}

	return json.Unmarshal(item.value, dest)
}

// Delete 删除缓存
func (m *MemoryCache) Delete(ctx context.Context, keys ...string) error {
	if len(keys) == 0 {
		return nil
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	for _, key := range keys {
		delete(m.data, m.buildKey(key))
	}

	return nil
}

// Exists 检查键是否存在
func (m *MemoryCache) Exists(ctx context.Context, key string) (bool, error) {
	m.mutex.RLock()
	item, exists := m.data[m.buildKey(key)]
	m.mutex.RUnlock()

	if !exists {
		return false, nil
	}

	if item.isExpired() {
		m.mutex.Lock()
		delete(m.data, m.buildKey(key))
		m.mutex.Unlock()
		return false, nil
	}

	return true, nil
}

// Expire 设置过期时间
func (m *MemoryCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	item, exists := m.data[m.buildKey(key)]
	if !exists {
		return fmt.Errorf("key not found")
	}

	if expiration > 0 {
		item.expiration = time.Now().Add(expiration)
		item.hasExpiry = true
	} else {
		item.hasExpiry = false
	}

	return nil
}

// TTL 获取剩余过期时间
func (m *MemoryCache) TTL(ctx context.Context, key string) (time.Duration, error) {
	m.mutex.RLock()
	item, exists := m.data[m.buildKey(key)]
	m.mutex.RUnlock()

	if !exists {
		return -2 * time.Second, nil // Redis convention: -2 for non-existent key
	}

	if !item.hasExpiry {
		return -1 * time.Second, nil // Redis convention: -1 for no expiry
	}

	if item.isExpired() {
		m.mutex.Lock()
		delete(m.data, m.buildKey(key))
		m.mutex.Unlock()
		return -2 * time.Second, nil
	}

	return time.Until(item.expiration), nil
}

// Increment 递增（简单实现）
func (m *MemoryCache) Increment(ctx context.Context, key string) (int64, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	fullKey := m.buildKey(key)
	item, exists := m.data[fullKey]

	var currentValue int64 = 0
	if exists && !item.isExpired() {
		json.Unmarshal(item.value, &currentValue)
	}

	newValue := currentValue + 1
	data, _ := json.Marshal(newValue)

	m.data[fullKey] = &cacheItem{
		value: data,
	}

	return newValue, nil
}

// Decrement 递减（简单实现）
func (m *MemoryCache) Decrement(ctx context.Context, key string) (int64, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	fullKey := m.buildKey(key)
	item, exists := m.data[fullKey]

	var currentValue int64 = 0
	if exists && !item.isExpired() {
		json.Unmarshal(item.value, &currentValue)
	}

	newValue := currentValue - 1
	data, _ := json.Marshal(newValue)

	m.data[fullKey] = &cacheItem{
		value: data,
	}

	return newValue, nil
}

// FlushAll 清空所有缓存
func (m *MemoryCache) FlushAll(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.data = make(map[string]*cacheItem)
	return nil
}

// Size 获取缓存大小
func (m *MemoryCache) Size() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.data)
}

// Keys 获取所有键（用于调试）
func (m *MemoryCache) Keys() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	keys := make([]string, 0, len(m.data))
	now := time.Now()

	for key, item := range m.data {
		if !item.hasExpiry || now.Before(item.expiration) {
			keys = append(keys, key)
		}
	}

	return keys
}
