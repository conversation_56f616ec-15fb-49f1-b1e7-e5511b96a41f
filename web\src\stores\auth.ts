import { defineStore } from 'pinia';
import type { AuthState } from '../types/auth';
import api from '../services/api';

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: null,
    isLoggedIn: false,
  }),

  getters: {
    getUser: (state) => state.user,
    getToken: (state) => state.token,
    isAuthenticated: (state) => state.isLoggedIn,
  },

  actions: {
    // 初始化认证状态
    initAuth() {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      
      if (token && user) {
        this.token = token;
        this.user = JSON.parse(user);
        this.isLoggedIn = true;
      }
    },

    // 用户登录
    async login(email: string, password: string) {
      try {
        const response = await api.login({ email, password });
        
        this.user = response.user;
        this.token = response.token;
        this.isLoggedIn = true;
        
        // 保存到本地存储
        localStorage.setItem('token', response.token);
        localStorage.setItem('user', JSON.stringify(response.user));
        
        return { success: true };
      } catch (error: any) {
        return {
          success: false,
          message: error.response?.data?.message || error.response?.data?.error || '登录失败'
        };
      }
    },

    // 用户注册
    async register(username: string, email: string, password: string) {
      try {
        await api.register({ username, email, password });

        return {
          success: true,
          message: '注册成功，请登录'
        };
      } catch (error: any) {
        return {
          success: false,
          message: error.response?.data?.message || error.response?.data?.error || '注册失败'
        };
      }
    },

    // 用户登出
    async logout() {
      try {
        await api.logout();
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        // 清除状态
        this.user = null;
        this.token = null;
        this.isLoggedIn = false;

        // 清除本地存储
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('theme');
      }
    },

    // 获取用户资料
    async fetchUserProfile() {
      try {
        const user = await api.getUserProfile();

        // 如果默认相册不存在，清除默认相册ID
        if (user.default_album_id && !user.default_album) {
          user.default_album_id = undefined;
        }

        this.user = user;
        localStorage.setItem('user', JSON.stringify(user));
        return { success: true, user };
      } catch (error: any) {
        console.error('Fetch user profile error:', error);
        return {
          success: false,
          message: error.response?.data?.message || '获取用户资料失败'
        };
      }
    },

    // 更新用户设置
    async updateUserSettings(settings: { default_album_id?: number }) {
      try {
        await api.updateUserSettings(settings);

        // 更新本地用户信息
        if (this.user) {
          this.user.default_album_id = settings.default_album_id;
          localStorage.setItem('user', JSON.stringify(this.user));
        }

        return { success: true };
      } catch (error: any) {
        console.error('Update user settings error:', error);
        return {
          success: false,
          message: error.response?.data?.message || '更新设置失败'
        };
      }
    },
  },
});