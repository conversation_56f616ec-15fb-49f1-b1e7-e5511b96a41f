package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"cloudbed/internal/config"
	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
	imageprocessor "cloudbed/pkg/image"
)

func main() {
	var (
		configPath = flag.String("config", "./config/config.json", "配置文件路径")
		action     = flag.String("action", "generate", "操作类型: generate, cleanup, regenerate")
		imageID    = flag.Int("id", 0, "图片ID（可选，用于处理单个图片）")
		force      = flag.Bool("force", false, "强制重新生成已存在的缩略图")
	)
	flag.Parse()

	// 加载配置
	cfg := config.LoadConfig()
	if cfg == nil {
		log.Fatal("Failed to load config")
	}

	// 连接数据库
	database.ConnectDatabase()

	// 创建图片处理器
	processor := imageprocessor.NewProcessor(imageprocessor.DefaultConfig())

	switch *action {
	case "generate":
		if *imageID > 0 {
			generateSingleThumbnail(processor, uint(*imageID), *force)
		} else {
			generateAllThumbnails(processor, *force)
		}
	case "cleanup":
		if *imageID > 0 {
			cleanupSingleThumbnail(processor, uint(*imageID))
		} else {
			cleanupAllThumbnails(processor)
		}
	case "regenerate":
		if *imageID > 0 {
			regenerateSingleThumbnail(processor, uint(*imageID))
		} else {
			regenerateAllThumbnails(processor)
		}
	default:
		fmt.Printf("未知操作: %s\n", *action)
		flag.Usage()
		os.Exit(1)
	}
}

// generateAllThumbnails 为所有图片生成缩略图
func generateAllThumbnails(processor *imageprocessor.Processor, force bool) {
	log.Println("开始为所有图片生成缩略图...")

	var images []models.Image
	if err := database.DB.Find(&images).Error; err != nil {
		log.Fatalf("获取图片列表失败: %v", err)
	}

	total := len(images)
	success := 0
	failed := 0

	for i, image := range images {
		log.Printf("处理图片 %d/%d: %s", i+1, total, image.Name)

		// 检查是否已有缩略图且不强制重新生成
		if !force && image.HasThumbnails() {
			log.Printf("图片 %s 已有缩略图，跳过", image.Name)
			continue
		}

		if err := generateThumbnailForImage(processor, &image); err != nil {
			log.Printf("为图片 %s 生成缩略图失败: %v", image.Name, err)
			failed++
		} else {
			success++
		}
	}

	log.Printf("缩略图生成完成: 成功 %d, 失败 %d, 总计 %d", success, failed, total)
}

// generateSingleThumbnail 为单个图片生成缩略图
func generateSingleThumbnail(processor *imageprocessor.Processor, imageID uint, force bool) {
	log.Printf("为图片 ID %d 生成缩略图...", imageID)

	var image models.Image
	if err := database.DB.First(&image, imageID).Error; err != nil {
		log.Fatalf("获取图片失败: %v", err)
	}

	// 检查是否已有缩略图且不强制重新生成
	if !force && image.HasThumbnails() {
		log.Printf("图片 %s 已有缩略图", image.Name)
		return
	}

	if err := generateThumbnailForImage(processor, &image); err != nil {
		log.Fatalf("生成缩略图失败: %v", err)
	}

	log.Printf("图片 %s 缩略图生成成功", image.Name)
}

// generateThumbnailForImage 为指定图片生成缩略图
func generateThumbnailForImage(processor *imageprocessor.Processor, image *models.Image) error {
	// 构建文件路径
	filePath := "./uploads/" + image.Name

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在: %s", filePath)
	}

	// 检查是否为有效的图片格式
	if !imageprocessor.IsValidImageFormat(image.Name) {
		return fmt.Errorf("不支持的图片格式: %s", image.Name)
	}

	// 获取图片信息
	imageInfo, err := processor.GetImageInfo(filePath)
	if err != nil {
		return fmt.Errorf("获取图片信息失败: %v", err)
	}

	// 生成缩略图
	result, err := processor.GenerateThumbnails(filePath)
	if err != nil {
		return fmt.Errorf("生成缩略图失败: %v", err)
	}

	// 更新图片记录
	image.Width = imageInfo.Width
	image.Height = imageInfo.Height
	image.Format = imageInfo.Format

	// 设置缩略图信息
	thumbnailInfo := &models.ThumbnailInfo{}
	for sizeName, thumbnailPath := range result.Thumbnails {
		// 将本地路径转换为相对路径
		relativePath := strings.Replace(thumbnailPath, "./uploads/", "", 1)

		switch sizeName {
		case "small":
			thumbnailInfo.Small = relativePath
		case "medium":
			thumbnailInfo.Medium = relativePath
		case "large":
			thumbnailInfo.Large = relativePath
		case "xlarge":
			thumbnailInfo.XLarge = relativePath
		}
	}

	if err := image.SetThumbnails(thumbnailInfo); err != nil {
		return fmt.Errorf("设置缩略图信息失败: %v", err)
	}

	// 保存到数据库
	if err := database.DB.Save(image).Error; err != nil {
		return fmt.Errorf("保存图片记录失败: %v", err)
	}

	return nil
}

// cleanupAllThumbnails 清理所有缩略图
func cleanupAllThumbnails(processor *imageprocessor.Processor) {
	log.Println("开始清理所有缩略图...")

	var images []models.Image
	if err := database.DB.Find(&images).Error; err != nil {
		log.Fatalf("获取图片列表失败: %v", err)
	}

	total := len(images)
	success := 0
	failed := 0

	for i, image := range images {
		log.Printf("清理图片 %d/%d: %s", i+1, total, image.Name)

		if err := cleanupThumbnailForImage(processor, &image); err != nil {
			log.Printf("清理图片 %s 的缩略图失败: %v", image.Name, err)
			failed++
		} else {
			success++
		}
	}

	log.Printf("缩略图清理完成: 成功 %d, 失败 %d, 总计 %d", success, failed, total)
}

// cleanupSingleThumbnail 清理单个图片的缩略图
func cleanupSingleThumbnail(processor *imageprocessor.Processor, imageID uint) {
	log.Printf("清理图片 ID %d 的缩略图...", imageID)

	var image models.Image
	if err := database.DB.First(&image, imageID).Error; err != nil {
		log.Fatalf("获取图片失败: %v", err)
	}

	if err := cleanupThumbnailForImage(processor, &image); err != nil {
		log.Fatalf("清理缩略图失败: %v", err)
	}

	log.Printf("图片 %s 缩略图清理成功", image.Name)
}

// cleanupThumbnailForImage 清理指定图片的缩略图
func cleanupThumbnailForImage(processor *imageprocessor.Processor, image *models.Image) error {
	// 构建文件路径
	filePath := "./uploads/" + image.Name

	// 清理缩略图文件
	if err := processor.CleanupThumbnails(filePath); err != nil {
		return fmt.Errorf("清理缩略图文件失败: %v", err)
	}

	// 清空数据库中的缩略图信息
	image.Thumbnails = ""
	if err := database.DB.Save(image).Error; err != nil {
		return fmt.Errorf("更新图片记录失败: %v", err)
	}

	return nil
}

// regenerateAllThumbnails 重新生成所有缩略图
func regenerateAllThumbnails(processor *imageprocessor.Processor) {
	log.Println("开始重新生成所有缩略图...")
	cleanupAllThumbnails(processor)
	generateAllThumbnails(processor, true)
}

// regenerateSingleThumbnail 重新生成单个图片的缩略图
func regenerateSingleThumbnail(processor *imageprocessor.Processor, imageID uint) {
	log.Printf("重新生成图片 ID %d 的缩略图...", imageID)
	cleanupSingleThumbnail(processor, imageID)
	generateSingleThumbnail(processor, imageID, true)
}
