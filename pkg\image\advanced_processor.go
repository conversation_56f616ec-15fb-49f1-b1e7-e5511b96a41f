package image

import (
	"fmt"
	"image"
	"image/color"
	"image/gif"
	"image/jpeg"
	"image/png"
	"io"
	"math"
	"os"
	"path/filepath"
	"strings"

	"golang.org/x/image/draw"
	"golang.org/x/image/font"
	"golang.org/x/image/font/basicfont"
	"golang.org/x/image/math/f64"
	"golang.org/x/image/math/fixed"
)

// AdvancedProcessor 高级图片处理器
type AdvancedProcessor struct {
	config ProcessorConfig
}

// ProcessingOptions 图片处理选项
type ProcessingOptions struct {
	// 格式转换
	OutputFormat string `json:"output_format"` // jpeg, png, webp, gif
	Quality      int    `json:"quality"`       // 1-100

	// 尺寸调整
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Mode   string `json:"mode"` // fit, fill, crop, stretch

	// 旋转和翻转
	Rotation int  `json:"rotation"` // 0, 90, 180, 270
	FlipH    bool `json:"flip_h"`   // 水平翻转
	FlipV    bool `json:"flip_v"`   // 垂直翻转

	// 裁剪
	CropX      int `json:"crop_x"`
	CropY      int `json:"crop_y"`
	CropWidth  int `json:"crop_width"`
	CropHeight int `json:"crop_height"`

	// 水印
	WatermarkText     string  `json:"watermark_text"`
	WatermarkPosition string  `json:"watermark_position"` // top-left, top-right, bottom-left, bottom-right, center
	WatermarkOpacity  float64 `json:"watermark_opacity"`  // 0.0-1.0
	WatermarkSize     int     `json:"watermark_size"`     // 字体大小

	// 压缩
	Compress        bool `json:"compress"`
	MaxFileSize     int  `json:"max_file_size"`    // 最大文件大小（KB）
	ProgressiveJPEG bool `json:"progressive_jpeg"` // 渐进式JPEG
}

// NewAdvancedProcessor 创建高级图片处理器
func NewAdvancedProcessor(config ProcessorConfig) *AdvancedProcessor {
	return &AdvancedProcessor{config: config}
}

// ProcessingResult 处理结果
type ProcessingResult struct {
	OutputPath       string            `json:"output_path"`
	Format           string            `json:"format"`
	Width            int               `json:"width"`
	Height           int               `json:"height"`
	FileSize         int64             `json:"file_size"`
	ProcessedSize    int64             `json:"processed_size"`
	OriginalSize     int64             `json:"original_size"`
	Operations       []string          `json:"operations"`
	CompressionRatio float64           `json:"compression_ratio"`
	Metadata         map[string]string `json:"metadata"`
}

// ProcessImage 处理图片
func (ap *AdvancedProcessor) ProcessImage(inputPath, outputPath string, options ProcessingOptions) (*ProcessingResult, error) {
	// 获取原始文件信息
	originalInfo, err := os.Stat(inputPath)
	if err != nil {
		return nil, fmt.Errorf("无法获取原始文件信息: %v", err)
	}
	originalSize := originalInfo.Size()

	// 创建临时文件路径
	tempPath := outputPath + ".tmp"
	currentPath := inputPath
	operations := []string{}

	// 裁剪
	if options.CropWidth > 0 && options.CropHeight > 0 {
		cropOpts := CropOptions{
			InputPath:  currentPath,
			OutputPath: tempPath,
			X:          options.CropX,
			Y:          options.CropY,
			Width:      options.CropWidth,
			Height:     options.CropHeight,
		}
		if err := ap.CropImage(cropOpts); err != nil {
			return nil, fmt.Errorf("裁剪失败: %v", err)
		}
		currentPath = tempPath
		operations = append(operations, "crop")
	}

	// 调整尺寸
	if options.Width > 0 || options.Height > 0 {
		resizeOpts := ResizeOptions{
			InputPath:  currentPath,
			OutputPath: tempPath + "2",
			Width:      options.Width,
			Height:     options.Height,
		}
		if err := ap.ResizeImage(resizeOpts); err != nil {
			return nil, fmt.Errorf("调整尺寸失败: %v", err)
		}
		if currentPath != inputPath {
			os.Remove(currentPath)
		}
		currentPath = tempPath + "2"
		operations = append(operations, "resize")
	}

	// 旋转
	if options.Rotation != 0 {
		rotateOpts := RotateOptions{
			InputPath:  currentPath,
			OutputPath: tempPath + "3",
			Angle:      float64(options.Rotation),
		}
		if err := ap.RotateImage(rotateOpts); err != nil {
			return nil, fmt.Errorf("旋转失败: %v", err)
		}
		if currentPath != inputPath {
			os.Remove(currentPath)
		}
		currentPath = tempPath + "3"
		operations = append(operations, "rotate")
	}

	// 添加水印
	if options.WatermarkText != "" {
		watermarkOpts := WatermarkOptions{
			InputPath:     currentPath,
			OutputPath:    outputPath,
			WatermarkText: options.WatermarkText,
			Position:      options.WatermarkPosition,
			Opacity:       options.WatermarkOpacity,
			FontSize:      options.WatermarkSize,
		}
		if err := ap.AddWatermark(watermarkOpts); err != nil {
			return nil, fmt.Errorf("添加水印失败: %v", err)
		}
		if currentPath != inputPath {
			os.Remove(currentPath)
		}
		operations = append(operations, "watermark")
	} else {
		// 如果没有水印，直接复制到输出路径
		if currentPath != outputPath {
			if err := copyFile(currentPath, outputPath); err != nil {
				return nil, fmt.Errorf("复制文件失败: %v", err)
			}
			if currentPath != inputPath {
				os.Remove(currentPath)
			}
		}
	}

	// 获取处理后的文件信息
	processedInfo, err := os.Stat(outputPath)
	if err != nil {
		return nil, fmt.Errorf("无法获取处理后文件信息: %v", err)
	}

	// 获取图片尺寸
	file, err := os.Open(outputPath)
	if err != nil {
		return nil, fmt.Errorf("无法打开处理后文件: %v", err)
	}
	defer file.Close()

	img, format, err := image.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("无法解码处理后图片: %v", err)
	}

	bounds := img.Bounds()
	processedSize := processedInfo.Size()

	result := &ProcessingResult{
		OutputPath:       outputPath,
		Format:           format,
		Width:            bounds.Dx(),
		Height:           bounds.Dy(),
		FileSize:         processedSize,
		ProcessedSize:    processedSize,
		OriginalSize:     originalSize,
		Operations:       operations,
		CompressionRatio: float64(originalSize) / float64(processedSize),
		Metadata:         make(map[string]string),
	}

	result.Metadata["processed_format"] = format
	result.Metadata["operations"] = strings.Join(operations, ",")

	return result, nil
}

// copyFile 复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}

// ConvertFormat 图片格式转换
type ConvertFormatOptions struct {
	InputPath    string `json:"input_path"`
	OutputPath   string `json:"output_path"`
	OutputFormat string `json:"output_format"` // jpeg, png, gif, webp
	Quality      int    `json:"quality"`       // 1-100, 仅对JPEG有效
}

// CompressOptions 图片压缩选项
type CompressOptions struct {
	InputPath   string `json:"input_path"`
	OutputPath  string `json:"output_path"`
	Quality     int    `json:"quality"`       // 1-100
	MaxFileSize int64  `json:"max_file_size"` // 最大文件大小（字节）
}

// ResizeOptions 图片调整大小选项
type ResizeOptions struct {
	InputPath    string `json:"input_path"`
	OutputPath   string `json:"output_path"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	KeepRatio    bool   `json:"keep_ratio"`    // 保持宽高比
	ResizeMethod string `json:"resize_method"` // fit, fill, stretch
}

// RotateOptions 图片旋转选项
type RotateOptions struct {
	InputPath  string  `json:"input_path"`
	OutputPath string  `json:"output_path"`
	Angle      float64 `json:"angle"` // 旋转角度（度）
}

// CropOptions 图片裁剪选项
type CropOptions struct {
	InputPath  string `json:"input_path"`
	OutputPath string `json:"output_path"`
	X          int    `json:"x"`      // 裁剪起始X坐标
	Y          int    `json:"y"`      // 裁剪起始Y坐标
	Width      int    `json:"width"`  // 裁剪宽度
	Height     int    `json:"height"` // 裁剪高度
}

// WatermarkOptions 水印选项
type WatermarkOptions struct {
	InputPath     string  `json:"input_path"`
	OutputPath    string  `json:"output_path"`
	WatermarkText string  `json:"watermark_text"`
	Position      string  `json:"position"`  // top-left, top-right, bottom-left, bottom-right, center
	Opacity       float64 `json:"opacity"`   // 0.0-1.0
	FontSize      int     `json:"font_size"` // 字体大小
	Color         string  `json:"color"`     // 颜色 #RRGGBB
}

// ConvertFormat 转换图片格式
func (ap *AdvancedProcessor) ConvertFormat(options ConvertFormatOptions) error {
	// 打开原始图片
	inputFile, err := os.Open(options.InputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// 解码图片
	img, _, err := image.Decode(inputFile)
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 确保输出目录存在
	outputDir := filepath.Dir(options.OutputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 创建输出文件
	outputFile, err := os.Create(options.OutputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	// 根据格式编码图片
	return ap.encodeImageWithFormat(outputFile, img, options.OutputFormat, options.Quality)
}

// CompressImage 压缩图片
func (ap *AdvancedProcessor) CompressImage(options CompressOptions) error {
	// 打开原始图片
	inputFile, err := os.Open(options.InputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// 解码图片
	img, format, err := image.Decode(inputFile)
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 确保输出目录存在
	outputDir := filepath.Dir(options.OutputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 创建输出文件
	outputFile, err := os.Create(options.OutputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	// 压缩图片
	return ap.encodeImageWithFormat(outputFile, img, format, options.Quality)
}

// ResizeImage 调整图片大小
func (ap *AdvancedProcessor) ResizeImage(options ResizeOptions) error {
	// 打开原始图片
	inputFile, err := os.Open(options.InputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// 解码图片
	img, format, err := image.Decode(inputFile)
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 计算新尺寸
	bounds := img.Bounds()
	originalWidth := bounds.Dx()
	originalHeight := bounds.Dy()

	newWidth, newHeight := ap.calculateNewSize(originalWidth, originalHeight, options)

	// 创建调整后的图片
	resized := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// 使用高质量缩放算法
	draw.CatmullRom.Scale(resized, resized.Bounds(), img, bounds, draw.Over, nil)

	// 确保输出目录存在
	outputDir := filepath.Dir(options.OutputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 保存调整后的图片
	outputFile, err := os.Create(options.OutputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	return ap.encodeImageWithFormat(outputFile, resized, format, ap.config.Quality)
}

// RotateImage 旋转图片
func (ap *AdvancedProcessor) RotateImage(options RotateOptions) error {
	// 打开原始图片
	inputFile, err := os.Open(options.InputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// 解码图片
	img, format, err := image.Decode(inputFile)
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 旋转图片
	rotated := ap.rotateImage(img, options.Angle)

	// 确保输出目录存在
	outputDir := filepath.Dir(options.OutputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 保存旋转后的图片
	outputFile, err := os.Create(options.OutputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	return ap.encodeImageWithFormat(outputFile, rotated, format, ap.config.Quality)
}

// CropImage 裁剪图片
func (ap *AdvancedProcessor) CropImage(options CropOptions) error {
	// 打开原始图片
	inputFile, err := os.Open(options.InputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// 解码图片
	img, format, err := image.Decode(inputFile)
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 验证裁剪区域
	bounds := img.Bounds()
	if options.X < 0 || options.Y < 0 ||
		options.X+options.Width > bounds.Dx() ||
		options.Y+options.Height > bounds.Dy() {
		return fmt.Errorf("crop area is out of image bounds")
	}

	// 裁剪图片
	cropRect := image.Rect(options.X, options.Y, options.X+options.Width, options.Y+options.Height)
	cropped := image.NewRGBA(image.Rect(0, 0, options.Width, options.Height))
	draw.Draw(cropped, cropped.Bounds(), img, cropRect.Min, draw.Src)

	// 确保输出目录存在
	outputDir := filepath.Dir(options.OutputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 保存裁剪后的图片
	outputFile, err := os.Create(options.OutputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	return ap.encodeImageWithFormat(outputFile, cropped, format, ap.config.Quality)
}

// AddWatermark 添加水印
func (ap *AdvancedProcessor) AddWatermark(options WatermarkOptions) error {
	// 打开原始图片
	inputFile, err := os.Open(options.InputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// 解码图片
	img, format, err := image.Decode(inputFile)
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 添加水印
	watermarked := ap.addTextWatermark(img, options)

	// 确保输出目录存在
	outputDir := filepath.Dir(options.OutputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 保存带水印的图片
	outputFile, err := os.Create(options.OutputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	return ap.encodeImageWithFormat(outputFile, watermarked, format, ap.config.Quality)
}

// encodeImageWithFormat 根据格式编码图片
func (ap *AdvancedProcessor) encodeImageWithFormat(w io.Writer, img image.Image, format string, quality int) error {
	switch strings.ToLower(format) {
	case "jpeg", "jpg":
		return jpeg.Encode(w, img, &jpeg.Options{Quality: quality})
	case "png":
		return png.Encode(w, img)
	case "gif":
		return gif.Encode(w, img, nil)
	default:
		return jpeg.Encode(w, img, &jpeg.Options{Quality: quality})
	}
}

// calculateNewSize 计算新尺寸
func (ap *AdvancedProcessor) calculateNewSize(originalWidth, originalHeight int, options ResizeOptions) (int, int) {
	if !options.KeepRatio {
		return options.Width, options.Height
	}

	// 保持宽高比
	scaleX := float64(options.Width) / float64(originalWidth)
	scaleY := float64(options.Height) / float64(originalHeight)

	var scale float64
	switch options.ResizeMethod {
	case "fit":
		// 适应：选择较小的缩放比例，确保图片完全适应目标尺寸
		scale = math.Min(scaleX, scaleY)
	case "fill":
		// 填充：选择较大的缩放比例，确保目标区域被完全填充
		scale = math.Max(scaleX, scaleY)
	default:
		// 默认使用fit模式
		scale = math.Min(scaleX, scaleY)
	}

	newWidth := int(float64(originalWidth) * scale)
	newHeight := int(float64(originalHeight) * scale)

	return newWidth, newHeight
}

// rotateImage 旋转图片
func (ap *AdvancedProcessor) rotateImage(img image.Image, angle float64) image.Image {
	bounds := img.Bounds()
	w, h := bounds.Dx(), bounds.Dy()

	// 将角度转换为弧度
	rad := angle * math.Pi / 180

	// 计算旋转后的边界
	cos := math.Abs(math.Cos(rad))
	sin := math.Abs(math.Sin(rad))
	newW := int(float64(w)*cos + float64(h)*sin)
	newH := int(float64(h)*cos + float64(w)*sin)

	// 创建新图片
	rotated := image.NewRGBA(image.Rect(0, 0, newW, newH))

	// 计算旋转中心
	centerX := float64(newW) / 2
	centerY := float64(newH) / 2
	origCenterX := float64(w) / 2
	origCenterY := float64(h) / 2

	// 应用旋转变换
	transform := f64.Aff3{
		math.Cos(rad), -math.Sin(rad), centerX - origCenterX*math.Cos(rad) + origCenterY*math.Sin(rad),
		math.Sin(rad), math.Cos(rad), centerY - origCenterX*math.Sin(rad) - origCenterY*math.Cos(rad),
	}

	draw.CatmullRom.Transform(rotated, transform, img, bounds, draw.Over, nil)

	return rotated
}

// addTextWatermark 添加文字水印
func (ap *AdvancedProcessor) addTextWatermark(img image.Image, options WatermarkOptions) image.Image {
	bounds := img.Bounds()
	watermarked := image.NewRGBA(bounds)
	draw.Draw(watermarked, bounds, img, bounds.Min, draw.Src)

	// 解析颜色
	watermarkColor := ap.parseColor(options.Color, options.Opacity)

	// 计算文字位置
	x, y := ap.calculateWatermarkPosition(bounds, options.Position, options.FontSize)

	// 添加文字（简化实现，实际应用中可以使用更复杂的字体渲染）
	ap.drawText(watermarked, x, y, options.WatermarkText, watermarkColor)

	return watermarked
}

// parseColor 解析颜色
func (ap *AdvancedProcessor) parseColor(colorStr string, opacity float64) color.RGBA {
	// 简化实现，默认返回白色
	alpha := uint8(255 * opacity)
	return color.RGBA{255, 255, 255, alpha}
}

// calculateWatermarkPosition 计算水印位置
func (ap *AdvancedProcessor) calculateWatermarkPosition(bounds image.Rectangle, position string, fontSize int) (int, int) {
	w, h := bounds.Dx(), bounds.Dy()
	margin := 20

	switch position {
	case "top-left":
		return margin, margin + fontSize
	case "top-right":
		return w - margin - fontSize*6, margin + fontSize // 估算文字宽度
	case "bottom-left":
		return margin, h - margin
	case "bottom-right":
		return w - margin - fontSize*6, h - margin
	case "center":
		return w/2 - fontSize*3, h / 2
	default:
		return w - margin - fontSize*6, h - margin // 默认右下角
	}
}

// drawText 绘制文字（简化实现）
func (ap *AdvancedProcessor) drawText(img *image.RGBA, x, y int, text string, c color.RGBA) {
	// 这是一个简化的文字绘制实现
	// 实际应用中应该使用更复杂的字体渲染库
	face := basicfont.Face7x13
	drawer := &font.Drawer{
		Dst:  img,
		Src:  image.NewUniform(c),
		Face: face,
		Dot:  fixed.Point26_6{X: fixed.I(x), Y: fixed.I(y)},
	}
	drawer.DrawString(text)
}
