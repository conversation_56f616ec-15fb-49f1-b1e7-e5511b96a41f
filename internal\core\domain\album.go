package models

import (
	"time"
)

// Album 表示相册模型
type Album struct {
	ID          uint      `json:"id" gorm:"primary_key"`
	Name        string    `json:"name" gorm:"type:varchar(100);not null"` // 相册名称
	Description string    `json:"description" gorm:"type:varchar(500)"`   // 相册描述
	CoverImage  string    `json:"cover_image" gorm:"type:varchar(500)"`   // 封面图片URL
	UserID      uint      `json:"user_id" gorm:"not null"`                // 所属用户ID
	User        User      `json:"user" gorm:"foreignKey:UserID"`          // 关联的用户
	Images      []Image   `json:"images" gorm:"foreignKey:AlbumID"`       // 相册中的图片
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// AlbumCreateRequest 创建相册请求
type AlbumCreateRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Description string `json:"description" binding:"max=500"`
}

// AlbumUpdateRequest 更新相册请求
type AlbumUpdateRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Description string `json:"description" binding:"max=500"`
	CoverImage  string `json:"cover_image"`
}

// AlbumResponse 相册响应
type AlbumResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	CoverImage  string    `json:"cover_image"`
	ImageCount  int64     `json:"image_count"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
