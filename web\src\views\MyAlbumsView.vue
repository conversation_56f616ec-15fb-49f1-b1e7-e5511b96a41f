<template>
  <div class="my-albums-container">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2><i class="bi bi-folder"></i> 我的相册</h2>
          <p class="page-subtitle">管理和组织您的图片相册</p>
        </div>
        <div class="header-right">
          <button class="btn btn-primary" @click="showCreateModal = true">
            <i class="bi bi-plus-circle"></i> 创建相册
          </button>
        </div>
      </div>
    </div>

    <!-- 相册网格视图 -->
    <div class="albums-view">
      <div v-if="loading" class="loading-state">
        <i class="bi bi-arrow-repeat spin"></i>
        <p>加载中...</p>
      </div>
      
      <div v-else class="albums-grid">
        <!-- 用户相册 -->
        <div
          class="album-card"
          v-for="album in albums"
          :key="album.id"
          @click="viewAlbum(album.id)"
        >
          <div class="album-cover">
            <img v-if="album.cover_image" :src="album.cover_image" :alt="album.name" />
            <i v-else class="bi bi-collection"></i>
          </div>
          <div class="album-info">
            <h3>{{ album.name }}</h3>
            <p>{{ album.description || '暂无描述' }}</p>
            <div class="album-meta">
              <span class="image-count">{{ album.image_count || 0 }} 张图片</span>
              <span class="created-date">{{ formatDate(album.created_at) }}</span>
            </div>
          </div>
          <div class="album-actions">
            <button
              class="action-btn edit-btn"
              @click.stop="editAlbum(album)"
              title="编辑相册"
            >
              <i class="bi bi-pencil"></i>
            </button>
            <button 
              class="action-btn delete-btn" 
              @click.stop="deleteAlbum(album)"
              title="删除相册"
            >
              <i class="bi bi-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && albums.length === 0" class="empty-state">
        <i class="bi bi-collection"></i>
        <h3>还没有相册</h3>
        <p>创建您的第一个相册来组织图片</p>
      </div>
    </div>

    <!-- 创建相册模态框 -->
    <div v-if="showCreateModal" class="modal-overlay" @click="showCreateModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>创建相册</h3>
          <button class="close-btn" @click="showCreateModal = false">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="createAlbum">
            <div class="form-group">
              <label for="albumName">相册名称</label>
              <input
                id="albumName"
                v-model="formData.name"
                type="text"
                class="form-control"
                placeholder="请输入相册名称"
                required
              />
            </div>
            <div class="form-group">
              <label for="albumDescription">相册描述</label>
              <textarea
                id="albumDescription"
                v-model="formData.description"
                class="form-control"
                placeholder="请输入相册描述（可选）"
                rows="3"
              ></textarea>
            </div>
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" @click="showCreateModal = false">
                取消
              </button>
              <button type="submit" class="btn btn-primary">
                创建
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 编辑相册模态框 -->
    <div v-if="showEditModal" class="modal-overlay" @click="showEditModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>编辑相册</h3>
          <button class="close-btn" @click="showEditModal = false">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="updateAlbum">
            <div class="form-group">
              <label for="editAlbumName">相册名称</label>
              <input
                id="editAlbumName"
                v-model="formData.name"
                type="text"
                class="form-control"
                placeholder="请输入相册名称"
                required
              />
            </div>
            <div class="form-group">
              <label for="editAlbumDescription">相册描述</label>
              <textarea
                id="editAlbumDescription"
                v-model="formData.description"
                class="form-control"
                placeholder="请输入相册描述（可选）"
                rows="3"
              ></textarea>
            </div>
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" @click="showEditModal = false">
                取消
              </button>
              <button type="submit" class="btn btn-primary">
                保存
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 通知 -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onActivated } from 'vue';
import { useRouter } from 'vue-router';
import api from '../services/api';
import { useDialog } from '../utils/dialog';

const router = useRouter();
const { confirmDeleteAsync, notify } = useDialog();

// 响应式数据
const loading = ref(false);
const albums = ref<any[]>([]);
const showCreateModal = ref(false);
const showEditModal = ref(false);
const editingAlbum = ref<any | null>(null);

const formData = reactive({
  name: '',
  description: ''
});

const notification = reactive({
  show: false,
  message: '',
  type: 'success' as 'success' | 'error'
});

// 生命周期
onMounted(() => {
  loadAlbums();
});

// 当组件被激活时（从其他页面返回时）也重新加载
onActivated(() => {
  loadAlbums();
});

// 方法
const loadAlbums = async () => {
  loading.value = true;
  try {
    console.log('开始加载相册...');
    const response = await api.getAlbums();
    console.log('相册API响应:', response);

    albums.value = response || [];

    console.log('加载完成，相册数量:', albums.value.length);
  } catch (error) {
    console.error('加载相册失败:', error);
    showNotification('加载相册失败', 'error');
  } finally {
    loading.value = false;
  }
};

const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  notification.message = message;
  notification.type = type;
  notification.show = true;
  setTimeout(() => {
    notification.show = false;
  }, 3000);
};

const createAlbum = async () => {
  if (!formData.name.trim()) {
    showNotification('请输入相册名称', 'error');
    return;
  }

  try {
    console.log('创建相册:', formData);
    const newAlbum = await api.createAlbum({
      name: formData.name,
      description: formData.description
    });
    console.log('创建成功:', newAlbum);

    // 直接添加到本地数组，避免重新加载
    albums.value.push(newAlbum);

    showNotification('相册创建成功');
    showCreateModal.value = false;
    formData.name = '';
    formData.description = '';
  } catch (error) {
    console.error('创建相册失败:', error);
    showNotification('创建失败', 'error');
  }
};

const editAlbum = (album: any) => {
  editingAlbum.value = album;
  formData.name = album.name;
  formData.description = album.description || '';
  showEditModal.value = true;
};

const updateAlbum = async () => {
  if (!editingAlbum.value || !formData.name.trim()) {
    showNotification('请输入相册名称', 'error');
    return;
  }

  try {
    console.log('更新相册:', editingAlbum.value.id, formData);
    const updatedAlbum = await api.updateAlbum(editingAlbum.value.id, {
      name: formData.name,
      description: formData.description
    });
    console.log('更新成功:', updatedAlbum);

    // 直接更新本地数组中的相册
    const index = albums.value.findIndex(album => album.id === editingAlbum.value!.id);
    if (index !== -1) {
      albums.value[index] = updatedAlbum;
    }

    showNotification('相册更新成功');
    showEditModal.value = false;
    editingAlbum.value = null;
    formData.name = '';
    formData.description = '';
  } catch (error) {
    console.error('更新相册失败:', error);
    showNotification('更新失败', 'error');
  }
};

const deleteAlbum = async (album: any) => {
  confirmDeleteAsync(album.name, async () => {
    try {
      console.log('删除相册:', album.id);
      await api.deleteAlbum(album.id);
      console.log('删除成功');

      // 直接从本地数组中移除相册
      const index = albums.value.findIndex(a => a.id === album.id);
      if (index !== -1) {
        albums.value.splice(index, 1);
      }

      notify.success('相册删除成功');
    } catch (error) {
      console.error('删除相册失败:', error);
      notify.error('删除失败');
    }
  });
};

const viewAlbum = (_albumId: number | string) => {
  // 暂时跳转到我的图片页面，后续可以实现相册详情页
  router.push('/user/photos');
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};
</script>

<style scoped>
/* 复用之前的样式，但针对相册进行调整 */
.my-albums-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.header-left {
  flex: 1;
}

.header-right {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
  padding-top: 4px; /* 微调对齐 */
}

.page-header h2 {
  color: #333;
  margin-bottom: 8px;
}

.page-subtitle {
  color: #666;
  margin: 0;
}

.action-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.loading-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #007bff;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.albums-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.album-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
}

.album-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.album-cover {
  width: 100%;
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.album-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.album-cover i {
  font-size: 48px;
  color: #6c757d;
}

.album-info {
  padding: 16px;
}

.album-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.album-info p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.album-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.album-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  gap: 4px;
}

.album-card:hover .album-actions {
  opacity: 1;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn {
  background-color: rgba(40, 167, 69, 0.9);
  color: white;
}

.edit-btn:hover {
  background-color: #28a745;
}

.delete-btn {
  background-color: rgba(220, 53, 69, 0.9);
  color: white;
}

.delete-btn:hover {
  background-color: #dc3545;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #666;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 20px;
  color: #ddd;
}

.empty-state h3 {
  margin-bottom: 12px;
  color: #333;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  z-index: 1001;
}

.notification.success {
  background-color: #28a745;
}

.notification.error {
  background-color: #dc3545;
}
</style>
