package errors

import (
	"fmt"
)

// AppError 应用错误类型
type AppError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
	Err     error  `json:"-"`
}

func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Err.Error())
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// 预定义错误码
const (
	// 认证相关
	ErrCodeUnauthorized     = "UNAUTHORIZED"
	ErrCodeInvalidToken     = "INVALID_TOKEN"
	ErrCodeTokenExpired     = "TOKEN_EXPIRED"
	ErrCodeInvalidPassword  = "INVALID_PASSWORD"
	ErrCodeAccountDisabled  = "ACCOUNT_DISABLED"

	// 权限相关
	ErrCodeForbidden           = "FORBIDDEN"
	ErrCodeInsufficientPermission = "INSUFFICIENT_PERMISSION"
	ErrCodeResourceNotOwned    = "RESOURCE_NOT_OWNED"

	// 资源相关
	ErrCodeNotFound        = "NOT_FOUND"
	ErrCodeAlreadyExists   = "ALREADY_EXISTS"
	ErrCodeResourceInUse   = "RESOURCE_IN_USE"

	// 验证相关
	ErrCodeValidationFailed = "VALIDATION_FAILED"
	ErrCodeInvalidFormat    = "INVALID_FORMAT"
	ErrCodeMissingParameter = "MISSING_PARAMETER"

	// 文件相关
	ErrCodeFileNotFound     = "FILE_NOT_FOUND"
	ErrCodeFileTypeNotAllowed = "FILE_TYPE_NOT_ALLOWED"
	ErrCodeFileTooLarge     = "FILE_TOO_LARGE"
	ErrCodeUploadFailed     = "UPLOAD_FAILED"

	// 存储相关
	ErrCodeStorageQuotaExceeded = "STORAGE_QUOTA_EXCEEDED"
	ErrCodeStorageNotAvailable  = "STORAGE_NOT_AVAILABLE"

	// 系统相关
	ErrCodeInternalError    = "INTERNAL_ERROR"
	ErrCodeDatabaseError    = "DATABASE_ERROR"
	ErrCodeServiceUnavailable = "SERVICE_UNAVAILABLE"
)

// 预定义错误消息
var errorMessages = map[string]string{
	ErrCodeUnauthorized:           "未授权访问",
	ErrCodeInvalidToken:           "无效的访问令牌",
	ErrCodeTokenExpired:           "访问令牌已过期",
	ErrCodeInvalidPassword:        "密码错误",
	ErrCodeAccountDisabled:        "账户已被禁用",
	ErrCodeForbidden:              "权限不足",
	ErrCodeInsufficientPermission: "权限不足",
	ErrCodeResourceNotOwned:       "无权访问此资源",
	ErrCodeNotFound:               "资源不存在",
	ErrCodeAlreadyExists:          "资源已存在",
	ErrCodeResourceInUse:          "资源正在使用中",
	ErrCodeValidationFailed:       "数据验证失败",
	ErrCodeInvalidFormat:          "数据格式错误",
	ErrCodeMissingParameter:       "缺少必要参数",
	ErrCodeFileNotFound:           "文件不存在",
	ErrCodeFileTypeNotAllowed:     "不支持的文件类型",
	ErrCodeFileTooLarge:           "文件过大",
	ErrCodeUploadFailed:           "文件上传失败",
	ErrCodeStorageQuotaExceeded:   "存储配额已满",
	ErrCodeStorageNotAvailable:    "存储服务不可用",
	ErrCodeInternalError:          "服务器内部错误",
	ErrCodeDatabaseError:          "数据库错误",
	ErrCodeServiceUnavailable:     "服务暂时不可用",
}

// New 创建新的应用错误
func New(code string, details string) *AppError {
	message, exists := errorMessages[code]
	if !exists {
		message = "未知错误"
	}

	return &AppError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// NewWithError 创建带原始错误的应用错误
func NewWithError(code string, details string, err error) *AppError {
	message, exists := errorMessages[code]
	if !exists {
		message = "未知错误"
	}

	return &AppError{
		Code:    code,
		Message: message,
		Details: details,
		Err:     err,
	}
}

// Wrap 包装现有错误
func Wrap(err error, code string, details string) *AppError {
	if err == nil {
		return nil
	}

	// 如果已经是AppError，直接返回
	if appErr, ok := err.(*AppError); ok {
		return appErr
	}

	return NewWithError(code, details, err)
}

// Is 检查错误是否为指定类型
func Is(err error, code string) bool {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Code == code
	}
	return false
}

// 预定义的常用错误
var (
	ErrUnauthorized           = New(ErrCodeUnauthorized, "")
	ErrInvalidToken           = New(ErrCodeInvalidToken, "")
	ErrTokenExpired           = New(ErrCodeTokenExpired, "")
	ErrInvalidPassword        = New(ErrCodeInvalidPassword, "")
	ErrAccountDisabled        = New(ErrCodeAccountDisabled, "")
	ErrForbidden              = New(ErrCodeForbidden, "")
	ErrInsufficientPermission = New(ErrCodeInsufficientPermission, "")
	ErrResourceNotOwned       = New(ErrCodeResourceNotOwned, "")
	ErrNotFound               = New(ErrCodeNotFound, "")
	ErrAlreadyExists          = New(ErrCodeAlreadyExists, "")
	ErrResourceInUse          = New(ErrCodeResourceInUse, "")
	ErrValidationFailed       = New(ErrCodeValidationFailed, "")
	ErrInvalidFormat          = New(ErrCodeInvalidFormat, "")
	ErrMissingParameter       = New(ErrCodeMissingParameter, "")
	ErrFileNotFound           = New(ErrCodeFileNotFound, "")
	ErrFileTypeNotAllowed     = New(ErrCodeFileTypeNotAllowed, "")
	ErrFileTooLarge           = New(ErrCodeFileTooLarge, "")
	ErrUploadFailed           = New(ErrCodeUploadFailed, "")
	ErrStorageQuotaExceeded   = New(ErrCodeStorageQuotaExceeded, "")
	ErrStorageNotAvailable    = New(ErrCodeStorageNotAvailable, "")
	ErrInternalError          = New(ErrCodeInternalError, "")
	ErrDatabaseError          = New(ErrCodeDatabaseError, "")
	ErrServiceUnavailable     = New(ErrCodeServiceUnavailable, "")
)

// FromStdError 从标准错误创建应用错误
func FromStdError(err error) *AppError {
	if err == nil {
		return nil
	}

	if appErr, ok := err.(*AppError); ok {
		return appErr
	}

	return NewWithError(ErrCodeInternalError, err.Error(), err)
}
