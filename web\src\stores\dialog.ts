import { ref, reactive } from 'vue'
import { defineStore } from 'pinia'

interface DialogOptions {
  title?: string
  message: string
  details?: string
  type?: 'warning' | 'danger' | 'info' | 'success'
  confirmText?: string
  cancelText?: string
  loadingText?: string
  closeOnOverlay?: boolean
}

interface NotificationOptions {
  title: string
  message?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
}

export const useDialogStore = defineStore('dialog', () => {
  // 确认弹窗状态
  const confirmDialog = reactive({
    show: false,
    title: '',
    message: '',
    details: '',
    type: 'warning' as 'warning' | 'danger' | 'info' | 'success',
    confirmText: '确认',
    cancelText: '取消',
    loadingText: '处理中...',
    loading: false,
    closeOnOverlay: true,
    onConfirm: () => {},
    onCancel: () => {}
  })

  // 通知列表
  const notifications = ref<Array<{
    id: string
    title: string
    message?: string
    type: 'success' | 'error' | 'warning' | 'info'
    duration: number
  }>>([])

  // 显示确认弹窗
  const showConfirm = (options: DialogOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      confirmDialog.title = options.title || '确认操作'
      confirmDialog.message = options.message
      confirmDialog.details = options.details || ''
      confirmDialog.type = options.type || 'warning'
      confirmDialog.confirmText = options.confirmText || '确认'
      confirmDialog.cancelText = options.cancelText || '取消'
      confirmDialog.loadingText = options.loadingText || '处理中...'
      confirmDialog.closeOnOverlay = options.closeOnOverlay !== false
      confirmDialog.loading = false
      confirmDialog.show = true

      confirmDialog.onConfirm = () => {
        confirmDialog.show = false
        resolve(true)
      }

      confirmDialog.onCancel = () => {
        confirmDialog.show = false
        resolve(false)
      }
    })
  }

  // 显示异步确认弹窗
  const showAsyncConfirm = (options: DialogOptions & { onConfirm: () => Promise<void> }): void => {
    confirmDialog.title = options.title || '确认操作'
    confirmDialog.message = options.message
    confirmDialog.details = options.details || ''
    confirmDialog.type = options.type || 'warning'
    confirmDialog.confirmText = options.confirmText || '确认'
    confirmDialog.cancelText = options.cancelText || '取消'
    confirmDialog.loadingText = options.loadingText || '处理中...'
    confirmDialog.closeOnOverlay = options.closeOnOverlay !== false
    confirmDialog.loading = false
    confirmDialog.show = true

    confirmDialog.onConfirm = async () => {
      confirmDialog.loading = true
      try {
        await options.onConfirm()
        confirmDialog.show = false
      } catch (error) {
        confirmDialog.loading = false
        // 错误处理由具体的操作函数处理
      }
    }

    confirmDialog.onCancel = () => {
      confirmDialog.show = false
    }
  }

  // 关闭确认弹窗
  const closeConfirm = () => {
    confirmDialog.show = false
    confirmDialog.loading = false
  }

  // 添加通知
  const addNotification = (options: NotificationOptions) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
    const notification = {
      id,
      title: options.title,
      message: options.message,
      type: options.type || 'success',
      duration: options.duration || 4000
    }

    notifications.value.push(notification)

    // 自动移除通知
    setTimeout(() => {
      removeNotification(id)
    }, notification.duration)
  }

  // 移除通知
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 便捷方法
  const showSuccess = (title: string, message?: string) => {
    addNotification({ title, message, type: 'success' })
  }

  const showError = (title: string, message?: string) => {
    addNotification({ title, message, type: 'error' })
  }

  const showWarning = (title: string, message?: string) => {
    addNotification({ title, message, type: 'warning' })
  }

  const showInfo = (title: string, message?: string) => {
    addNotification({ title, message, type: 'info' })
  }

  return {
    // 状态
    confirmDialog,
    notifications,
    
    // 方法
    showConfirm,
    showAsyncConfirm,
    closeConfirm,
    addNotification,
    removeNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
})
