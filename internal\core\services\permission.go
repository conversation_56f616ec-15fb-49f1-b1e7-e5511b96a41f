package services

import (
	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
)

// PermissionService 权限服务
type PermissionService struct{}

// NewPermissionService 创建权限服务实例
func NewPermissionService() *PermissionService {
	return &PermissionService{}
}

// GetUserEffectivePermissions 获取用户的有效权限（包括组权限）
func (ps *PermissionService) GetUserEffectivePermissions(userID uint) ([]models.Permission, error) {
	var permissions []models.Permission
	permissionMap := make(map[uint]models.Permission)

	// 1. 获取用户直接权限
	var userPermissions []models.UserPermission
	err := database.DB.Preload("Permission").Where("user_id = ?", userID).Find(&userPermissions).Error
	if err != nil {
		return nil, err
	}

	for _, up := range userPermissions {
		permissionMap[up.Permission.ID] = up.Permission
	}

	// 2. 获取用户所属组的权限
	var userGroups []models.UserGroup
	err = database.DB.Preload("Group").Preload("Group.Permissions").
		Where("user_id = ?", userID).Find(&userGroups).Error
	if err != nil {
		return nil, err
	}

	for _, ug := range userGroups {
		// 只处理激活的组
		if ug.Group.IsActive {
			for _, permission := range ug.Group.Permissions {
				permissionMap[permission.ID] = permission
			}
		}
	}

	// 3. 获取用户角色权限
	var user models.User
	err = database.DB.Preload("Role").Preload("Role.Permissions").First(&user, userID).Error
	if err != nil {
		return nil, err
	}

	for _, permission := range user.Role.Permissions {
		permissionMap[permission.ID] = permission
	}

	// 转换为切片
	for _, permission := range permissionMap {
		permissions = append(permissions, permission)
	}

	return permissions, nil
}

// HasPermission 检查用户是否有指定权限
func (ps *PermissionService) HasPermission(userID uint, permissionName string) (bool, error) {
	permissions, err := ps.GetUserEffectivePermissions(userID)
	if err != nil {
		return false, err
	}

	for _, permission := range permissions {
		if permission.Name == permissionName {
			return true, nil
		}
	}

	return false, nil
}

// GetGroupPermissions 获取组的所有权限
func (ps *PermissionService) GetGroupPermissions(groupID uint) ([]models.Permission, error) {
	var group models.Group
	err := database.DB.Preload("Permissions").First(&group, groupID).Error
	if err != nil {
		return nil, err
	}

	return group.Permissions, nil
}

// AssignPermissionToGroup 为组分配权限
func (ps *PermissionService) AssignPermissionToGroup(groupID uint, permissionIDs []uint) error {
	// 开始事务
	tx := database.DB.Begin()

	// 删除现有权限
	if err := tx.Where("group_id = ?", groupID).Delete(&models.GroupPermission{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 添加新权限
	for _, permissionID := range permissionIDs {
		groupPermission := models.GroupPermission{
			GroupID:      groupID,
			PermissionID: permissionID,
		}

		if err := tx.Create(&groupPermission).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// RemovePermissionFromGroup 从组中移除权限
func (ps *PermissionService) RemovePermissionFromGroup(groupID uint, permissionID uint) error {
	return database.DB.Where("group_id = ? AND permission_id = ?", groupID, permissionID).
		Delete(&models.GroupPermission{}).Error
}

// GetUsersInGroup 获取组中的所有用户
func (ps *PermissionService) GetUsersInGroup(groupID uint) ([]models.User, error) {
	var users []models.User
	
	err := database.DB.Joins("JOIN user_groups ON users.id = user_groups.user_id").
		Where("user_groups.group_id = ?", groupID).
		Find(&users).Error
	
	return users, err
}

// AddUserToGroup 将用户添加到组
func (ps *PermissionService) AddUserToGroup(userID uint, groupID uint, addedBy uint) error {
	// 检查用户是否已在组中
	var existingRelation models.UserGroup
	if database.DB.Where("user_id = ? AND group_id = ?", userID, groupID).
		First(&existingRelation).Error == nil {
		return nil // 用户已在组中
	}

	userGroup := models.UserGroup{
		UserID:  userID,
		GroupID: groupID,
		AddedBy: addedBy,
	}

	return database.DB.Create(&userGroup).Error
}

// RemoveUserFromGroup 从组中移除用户
func (ps *PermissionService) RemoveUserFromGroup(userID uint, groupID uint) error {
	return database.DB.Where("user_id = ? AND group_id = ?", userID, groupID).
		Delete(&models.UserGroup{}).Error
}

// GetUserGroups 获取用户所属的所有组
func (ps *PermissionService) GetUserGroups(userID uint) ([]models.Group, error) {
	var groups []models.Group
	
	err := database.DB.Joins("JOIN user_groups ON groups.id = user_groups.group_id").
		Where("user_groups.user_id = ? AND groups.is_active = ?", userID, true).
		Find(&groups).Error
	
	return groups, err
}

// GetPermissionHierarchy 获取权限层次结构（用户 -> 组 -> 角色）
func (ps *PermissionService) GetPermissionHierarchy(userID uint) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 用户直接权限
	var userPermissions []models.UserPermission
	err := database.DB.Preload("Permission").Where("user_id = ?", userID).Find(&userPermissions).Error
	if err != nil {
		return nil, err
	}

	var directPermissions []models.Permission
	for _, up := range userPermissions {
		directPermissions = append(directPermissions, up.Permission)
	}
	result["direct_permissions"] = directPermissions

	// 组权限
	userGroups, err := ps.GetUserGroups(userID)
	if err != nil {
		return nil, err
	}

	var groupPermissions []models.Permission
	groupDetails := make([]map[string]interface{}, 0)
	
	for _, group := range userGroups {
		permissions, err := ps.GetGroupPermissions(group.ID)
		if err != nil {
			continue
		}
		
		groupPermissions = append(groupPermissions, permissions...)
		groupDetails = append(groupDetails, map[string]interface{}{
			"group":       group,
			"permissions": permissions,
		})
	}
	result["group_permissions"] = groupPermissions
	result["groups"] = groupDetails

	// 角色权限
	var user models.User
	err = database.DB.Preload("Role").Preload("Role.Permissions").First(&user, userID).Error
	if err != nil {
		return nil, err
	}

	result["role_permissions"] = user.Role.Permissions
	result["role"] = user.Role

	// 有效权限（去重后的所有权限）
	effectivePermissions, err := ps.GetUserEffectivePermissions(userID)
	if err != nil {
		return nil, err
	}
	result["effective_permissions"] = effectivePermissions

	return result, nil
}
