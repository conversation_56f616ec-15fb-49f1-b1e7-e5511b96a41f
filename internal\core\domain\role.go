package models

import "time"

// Role 表示角色模型
type Role struct {
	ID          uint         `json:"id" gorm:"primary_key"`
	Name        string       `json:"name" gorm:"type:varchar(50);unique;not null"`        // 角色名称（英文）
	DisplayName string       `json:"display_name" gorm:"type:varchar(100);not null"`      // 显示名称（中文）
	Description string       `json:"description" gorm:"type:varchar(500)"`                // 角色描述
	IsSystem    bool         `json:"is_system" gorm:"default:false"`                      // 是否为系统角色
	Permissions []Permission `json:"permissions" gorm:"many2many:role_permissions"`       // 角色权限
	Users       []User       `json:"users" gorm:"foreignKey:RoleID"`                      // 拥有此角色的用户
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
}

// Permission 表示权限模型
type Permission struct {
	ID          uint   `json:"id" gorm:"primary_key"`
	Name        string `json:"name" gorm:"type:varchar(50);unique;not null"`   // 权限名称（英文）
	DisplayName string `json:"display_name" gorm:"type:varchar(100);not null"` // 显示名称（中文）
	Description string `json:"description" gorm:"type:varchar(500)"`           // 权限描述
	Resource    string `json:"resource" gorm:"type:varchar(50);not null"`      // 资源类型
	Action      string `json:"action" gorm:"type:varchar(50);not null"`        // 操作类型
	Roles       []Role `json:"roles" gorm:"many2many:role_permissions"`        // 拥有此权限的角色
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// RolePermission 角色权限关联表
type RolePermission struct {
	ID           uint      `json:"id" gorm:"primary_key"`
	RoleID       uint      `json:"role_id" gorm:"not null"`
	PermissionID uint      `json:"permission_id" gorm:"not null"`
	Role         Role      `json:"role" gorm:"foreignKey:RoleID"`
	Permission   Permission `json:"permission" gorm:"foreignKey:PermissionID"`
	CreatedAt    time.Time `json:"created_at"`
}

// UserPermission 用户特殊权限表
type UserPermission struct {
	ID           uint       `json:"id" gorm:"primary_key"`
	UserID       uint       `json:"user_id" gorm:"not null"`
	PermissionID uint       `json:"permission_id" gorm:"not null"`
	Granted      bool       `json:"granted" gorm:"default:true"`              // true=授予，false=撤销
	User         User       `json:"user" gorm:"foreignKey:UserID"`
	Permission   Permission `json:"permission" gorm:"foreignKey:PermissionID"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// 角色常量
const (
	RoleSuperAdmin = "super_admin"
	RoleAdmin      = "admin"
	RoleUser       = "user"
	RoleGuest      = "guest"
)

// 权限常量
const (
	// 用户管理权限
	PermUserView       = "user.view"
	PermUserCreate     = "user.create"
	PermUserUpdate     = "user.update"
	PermUserDelete     = "user.delete"
	PermUserManageRole = "user.manage_role"

	// 角色管理权限
	PermRoleView   = "role.view"
	PermRoleCreate = "role.create"
	PermRoleUpdate = "role.update"
	PermRoleDelete = "role.delete"

	// 权限管理权限
	PermPermissionView   = "permission.view"
	PermPermissionAssign = "permission.assign"

	// 图片管理权限
	PermImageViewOwn   = "image.view_own"
	PermImageViewAll   = "image.view_all"
	PermImageUpload    = "image.upload"
	PermImageUpdateOwn = "image.update_own"
	PermImageUpdateAll = "image.update_all"
	PermImageDeleteOwn = "image.delete_own"
	PermImageDeleteAll = "image.delete_all"

	// 相册管理权限
	PermAlbumViewOwn   = "album.view_own"
	PermAlbumViewAll   = "album.view_all"
	PermAlbumCreate    = "album.create"
	PermAlbumUpdateOwn = "album.update_own"
	PermAlbumUpdateAll = "album.update_all"
	PermAlbumDeleteOwn = "album.delete_own"
	PermAlbumDeleteAll = "album.delete_all"

	// 系统管理权限
	PermSystemViewLogs      = "system.view_logs"
	PermSystemManageSettings = "system.manage_settings"
	PermSystemBackup        = "system.backup"
)

// HasPermission 检查角色是否拥有指定权限
func (r *Role) HasPermission(permissionName string) bool {
	for _, perm := range r.Permissions {
		if perm.Name == permissionName {
			return true
		}
	}
	return false
}

// IsSystemRole 检查是否为系统角色
func (r *Role) IsSystemRole() bool {
	return r.IsSystem
}

// CanBeDeleted 检查角色是否可以被删除
func (r *Role) CanBeDeleted() bool {
	return !r.IsSystem
}

// GetPermissionsByResource 根据资源类型获取权限
func (r *Role) GetPermissionsByResource(resource string) []Permission {
	var permissions []Permission
	for _, perm := range r.Permissions {
		if perm.Resource == resource {
			permissions = append(permissions, perm)
		}
	}
	return permissions
}

// 角色响应结构
type RoleResponse struct {
	ID          uint                 `json:"id"`
	Name        string               `json:"name"`
	DisplayName string               `json:"display_name"`
	Description string               `json:"description"`
	IsSystem    bool                 `json:"is_system"`
	Permissions []PermissionResponse `json:"permissions"`
	UserCount   int64                `json:"user_count"`
	CreatedAt   time.Time            `json:"created_at"`
	UpdatedAt   time.Time            `json:"updated_at"`
}

// 权限响应结构
type PermissionResponse struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Description string `json:"description"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
}

// 角色创建请求
type RoleCreateRequest struct {
	Name         string `json:"name" binding:"required,min=2,max=50"`
	DisplayName  string `json:"display_name" binding:"required,min=2,max=100"`
	Description  string `json:"description" binding:"max=500"`
	PermissionIDs []uint `json:"permission_ids"`
}

// 角色更新请求
type RoleUpdateRequest struct {
	DisplayName   string `json:"display_name" binding:"required,min=2,max=100"`
	Description   string `json:"description" binding:"max=500"`
	PermissionIDs []uint `json:"permission_ids"`
}

// 用户角色分配请求
type UserRoleAssignRequest struct {
	UserID uint `json:"user_id" binding:"required"`
	RoleID uint `json:"role_id" binding:"required"`
}

// 用户权限分配请求
type UserPermissionAssignRequest struct {
	UserID       uint `json:"user_id" binding:"required"`
	PermissionID uint `json:"permission_id" binding:"required"`
	Granted      bool `json:"granted"`
}
