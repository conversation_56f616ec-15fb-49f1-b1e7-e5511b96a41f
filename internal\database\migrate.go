package database

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"gorm.io/gorm"
)

// MigrationManager 数据库迁移管理器（简化版）
type MigrationManager struct {
	db *gorm.DB
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *gorm.DB) *MigrationManager {
	return &MigrationManager{db: db}
}

// ApplyMigration 应用单个迁移文件
func (m *MigrationManager) ApplyMigration(filename string) error {
	log.Printf("Applying migration: %s", filename)

	migrationPath := filepath.Join("database", "migrations", filename)
	if _, err := os.Stat(migrationPath); os.IsNotExist(err) {
		return fmt.Errorf("migration file not found: %s", migrationPath)
	}

	content, err := os.ReadFile(migrationPath)
	if err != nil {
		return fmt.Errorf("failed to read migration file: %v", err)
	}

	// 分割并执行SQL语句
	statements := strings.Split(string(content), ";")
	for i, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") {
			continue
		}

		if err := m.db.Exec(stmt).Error; err != nil {
			log.Printf("Warning: Failed to execute statement %d in %s: %v", i+1, filename, err)
			// 继续执行，不中断
		}
	}

	log.Printf("Migration %s applied successfully", filename)
	return nil
}

// ApplyAllMigrations 应用所有迁移文件
func (m *MigrationManager) ApplyAllMigrations() error {
	log.Println("Applying all migrations...")

	migrationFiles := []string{
		"add_image_metadata.sql",
		"add_performance_indexes.sql",
		"create_tag_tables.sql",
		"create_sharing_tables.sql",
	}

	for _, file := range migrationFiles {
		if err := m.ApplyMigration(file); err != nil {
			log.Printf("Warning: Failed to apply migration %s: %v", file, err)
			// 继续执行其他迁移，不中断整个过程
		}
	}

	log.Println("All migrations processing completed")
	return nil
}

// CheckTables 检查表是否存在
func (m *MigrationManager) CheckTables() error {
	log.Println("Checking database tables...")

	tables := []string{
		"users", "roles", "permissions", "role_permissions",
		"albums", "images", "storage_configs",
		"tags", "image_tags", "tag_categories",
		"shares", "share_access", "comments",
	}

	for _, table := range tables {
		var count int64
		err := m.db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?", table).Scan(&count).Error
		if err != nil {
			log.Printf("Failed to check table %s: %v", table, err)
			continue
		}

		if count > 0 {
			log.Printf("✓ Table %s exists", table)
		} else {
			log.Printf("✗ Table %s missing", table)
		}
	}

	return nil
}

// AnalyzeTables 分析表以更新统计信息
func (m *MigrationManager) AnalyzeTables() error {
	log.Println("Analyzing tables to update statistics...")

	tables := []string{
		"users", "images", "albums", "roles", "permissions",
		"role_permissions", "storage_configs", "tags", "shares",
	}

	for _, table := range tables {
		err := m.db.Exec(fmt.Sprintf("ANALYZE TABLE %s", table)).Error
		if err != nil {
			log.Printf("Failed to analyze table %s: %v", table, err)
			continue
		}
		log.Printf("✓ Table %s analyzed", table)
	}

	log.Println("Table analysis completed")
	return nil
}

// QuickSetup 快速设置（用于新安装）
func (m *MigrationManager) QuickSetup() error {
	log.Println("Running quick database setup...")

	// 1. 应用所有迁移
	if err := m.ApplyAllMigrations(); err != nil {
		log.Printf("Warning: Migration issues: %v", err)
	}

	// 2. 分析表
	if err := m.AnalyzeTables(); err != nil {
		log.Printf("Warning: Table analysis issues: %v", err)
	}

	// 3. 检查表
	if err := m.CheckTables(); err != nil {
		log.Printf("Warning: Table check issues: %v", err)
	}

	log.Println("Quick setup completed!")
	return nil
}
