package controllers

import (
	"archive/zip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
	imageprocessor "cloudbed/pkg/image"
	"cloudbed/pkg/response"

	"github.com/gin-gonic/gin"
)

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	ImageIDs []uint `json:"image_ids" binding:"required"`
}

// BatchMoveRequest 批量移动请求
type BatchMoveRequest struct {
	ImageIDs []uint `json:"image_ids" binding:"required"`
	AlbumID  *uint  `json:"album_id"` // nil表示移动到未分类
}

// BatchDownloadRequest 批量下载请求
type BatchDownloadRequest struct {
	ImageIDs []uint `json:"image_ids" binding:"required"`
}

// BatchOperationResult 批量操作结果
type BatchOperationResult struct {
	Total     int      `json:"total"`
	Success   int      `json:"success"`
	Failed    int      `json:"failed"`
	Errors    []string `json:"errors,omitempty"`
	FailedIDs []uint   `json:"failed_ids,omitempty"`
}

// BatchDeleteImages 批量删除图片
func BatchDeleteImages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req BatchDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	if len(req.ImageIDs) == 0 {
		response.BadRequest(c, "请选择要删除的图片")
		return
	}

	if len(req.ImageIDs) > 100 {
		response.BadRequest(c, "单次最多删除100张图片")
		return
	}

	result := &BatchOperationResult{
		Total:   len(req.ImageIDs),
		Success: 0,
		Failed:  0,
		Errors:  []string{},
	}

	// 获取用户的图片
	var images []models.Image
	err := database.DB.Where("id IN ? AND user_id = ?", req.ImageIDs, userID.(uint)).Find(&images).Error
	if err != nil {
		response.InternalServerErrorWithDetail(c, "查询图片失败", err.Error())
		return
	}

	if len(images) == 0 {
		response.NotFound(c, "没有找到可删除的图片")
		return
	}

	processor := imageprocessor.NewProcessor(imageprocessor.DefaultConfig())
	var totalSize int64

	// 逐个删除图片
	for _, image := range images {
		if err := deleteImageWithCleanup(&image, processor); err != nil {
			result.Failed++
			result.Errors = append(result.Errors, fmt.Sprintf("删除图片 %s 失败: %v", image.Name, err))
			result.FailedIDs = append(result.FailedIDs, image.ID)
		} else {
			result.Success++
			totalSize += image.Size
		}
	}

	// 更新用户存储使用量
	if totalSize > 0 {
		if err := UpdateStorageUsage(userID.(uint), -totalSize); err != nil {
			// 记录错误但不影响删除结果
			result.Errors = append(result.Errors, fmt.Sprintf("更新存储使用量失败: %v", err))
		}
	}

	response.SuccessWithMessage(c, "批量删除完成", result)
}

// BatchMoveImages 批量移动图片到相册
func BatchMoveImages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req BatchMoveRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	if len(req.ImageIDs) == 0 {
		response.BadRequest(c, "请选择要移动的图片")
		return
	}

	if len(req.ImageIDs) > 200 {
		response.BadRequest(c, "单次最多移动200张图片")
		return
	}

	// 如果指定了相册ID，验证相册是否存在且属于当前用户
	if req.AlbumID != nil {
		var album models.Album
		err := database.DB.Where("id = ? AND user_id = ?", *req.AlbumID, userID.(uint)).First(&album).Error
		if err != nil {
			response.NotFound(c, "相册不存在")
			return
		}
	}

	result := &BatchOperationResult{
		Total:   len(req.ImageIDs),
		Success: 0,
		Failed:  0,
		Errors:  []string{},
	}

	// 批量更新图片的相册ID
	updateResult := database.DB.Model(&models.Image{}).
		Where("id IN ? AND user_id = ?", req.ImageIDs, userID.(uint)).
		Update("album_id", req.AlbumID)

	if updateResult.Error != nil {
		response.InternalServerErrorWithDetail(c, "移动图片失败", updateResult.Error.Error())
		return
	}

	result.Success = int(updateResult.RowsAffected)
	result.Failed = result.Total - result.Success

	if result.Failed > 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("有 %d 张图片移动失败，可能不存在或不属于您", result.Failed))
	}

	albumName := "未分类"
	if req.AlbumID != nil {
		var album models.Album
		if err := database.DB.First(&album, *req.AlbumID).Error; err == nil {
			albumName = album.Name
		}
	}

	response.SuccessWithMessage(c, fmt.Sprintf("成功将 %d 张图片移动到 %s", result.Success, albumName), result)
}

// BatchDownloadImages 批量下载图片
func BatchDownloadImages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req BatchDownloadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	if len(req.ImageIDs) == 0 {
		response.BadRequest(c, "请选择要下载的图片")
		return
	}

	if len(req.ImageIDs) > 50 {
		response.BadRequest(c, "单次最多下载50张图片")
		return
	}

	// 获取用户的图片
	var images []models.Image
	err := database.DB.Where("id IN ? AND user_id = ?", req.ImageIDs, userID.(uint)).Find(&images).Error
	if err != nil {
		response.InternalServerErrorWithDetail(c, "查询图片失败", err.Error())
		return
	}

	if len(images) == 0 {
		response.NotFound(c, "没有找到可下载的图片")
		return
	}

	// 创建临时ZIP文件
	tempDir := "./temp"
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		response.InternalServerError(c, "创建临时目录失败")
		return
	}

	timestamp := time.Now().Format("20060102_150405")
	zipFileName := fmt.Sprintf("images_%s.zip", timestamp)
	zipFilePath := filepath.Join(tempDir, zipFileName)

	zipFile, err := os.Create(zipFilePath)
	if err != nil {
		response.InternalServerError(c, "创建ZIP文件失败")
		return
	}
	defer func() {
		zipFile.Close()
		// 清理临时文件
		go func() {
			time.Sleep(10 * time.Minute) // 10分钟后删除
			os.Remove(zipFilePath)
		}()
	}()

	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	successCount := 0
	for _, image := range images {
		imagePath := "./uploads/" + image.Name

		// 检查文件是否存在
		if _, err := os.Stat(imagePath); os.IsNotExist(err) {
			continue
		}

		// 添加文件到ZIP
		if err := addFileToZip(zipWriter, imagePath, image.Name); err != nil {
			continue
		}
		successCount++
	}

	if successCount == 0 {
		response.InternalServerError(c, "没有可用的图片文件")
		return
	}

	// 关闭ZIP写入器以完成文件
	zipWriter.Close()
	zipFile.Close()

	// 设置响应头
	c.Header("Content-Type", "application/zip")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", zipFileName))
	c.Header("Content-Length", strconv.FormatInt(getFileSize(zipFilePath), 10))

	// 发送文件
	c.File(zipFilePath)
}

// GetBatchOperationStatus 获取批量操作状态
func GetBatchOperationStatus(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 获取用户的图片统计信息
	var stats struct {
		TotalImages         int64 `json:"total_images"`
		UncategorizedImages int64 `json:"uncategorized_images"`
		TotalAlbums         int64 `json:"total_albums"`
		TotalSize           int64 `json:"total_size"`
	}

	database.DB.Model(&models.Image{}).Where("user_id = ?", userID.(uint)).Count(&stats.TotalImages)
	database.DB.Model(&models.Image{}).Where("user_id = ? AND album_id IS NULL", userID.(uint)).Count(&stats.UncategorizedImages)
	database.DB.Model(&models.Album{}).Where("user_id = ?", userID.(uint)).Count(&stats.TotalAlbums)
	database.DB.Model(&models.Image{}).Where("user_id = ?", userID.(uint)).Select("COALESCE(SUM(size), 0)").Scan(&stats.TotalSize)

	response.Success(c, stats)
}

// deleteImageWithCleanup 删除图片并清理相关文件
func deleteImageWithCleanup(image *models.Image, processor *imageprocessor.Processor) error {
	// 删除数据库记录
	if err := database.DB.Delete(image).Error; err != nil {
		return fmt.Errorf("删除数据库记录失败: %v", err)
	}

	// 删除原始文件
	filePath := "./uploads/" + image.Name
	if err := os.Remove(filePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("删除文件失败: %v", err)
	}

	// 清理缩略图
	if err := processor.CleanupThumbnails(filePath); err != nil {
		// 缩略图清理失败不影响主要删除操作
		fmt.Printf("清理缩略图失败: %v\n", err)
	}

	return nil
}

// addFileToZip 将文件添加到ZIP压缩包
func addFileToZip(zipWriter *zip.Writer, filePath, fileName string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 创建ZIP文件条目
	writer, err := zipWriter.Create(fileName)
	if err != nil {
		return err
	}

	// 复制文件内容
	_, err = io.Copy(writer, file)
	return err
}

// getFileSize 获取文件大小
func getFileSize(filePath string) int64 {
	if info, err := os.Stat(filePath); err == nil {
		return info.Size()
	}
	return 0
}
