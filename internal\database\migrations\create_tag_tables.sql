-- 创建标签相关表
-- 标签系统的数据库表结构

-- 创建标签表
CREATE TABLE IF NOT EXISTS `tags` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL COMMENT '标签名称',
    `color` varchar(7) NOT NULL DEFAULT '#007bff' COMMENT '标签颜色',
    `description` varchar(255) DEFAULT '' COMMENT '标签描述',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
    `usage_count` int NOT NULL DEFAULT 0 COMMENT '使用次数',
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_user_tag_name` (`user_id`, `name`),
    KEY `idx_tags_user_id` (`user_id`),
    KEY `idx_tags_usage_count` (`usage_count`),
    KEY `idx_tags_name` (`name`),
    CONSTRAINT `fk_tags_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';

-- 创建图片标签关联表
CREATE TABLE IF NOT EXISTS `image_tags` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `image_id` bigint unsigned NOT NULL COMMENT '图片ID',
    `tag_id` bigint unsigned NOT NULL COMMENT '标签ID',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID（冗余字段）',
    `created_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_image_tag` (`image_id`, `tag_id`),
    KEY `idx_image_tags_image_id` (`image_id`),
    KEY `idx_image_tags_tag_id` (`tag_id`),
    KEY `idx_image_tags_user_id` (`user_id`),
    CONSTRAINT `fk_image_tags_image` FOREIGN KEY (`image_id`) REFERENCES `images` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_image_tags_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_image_tags_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片标签关联表';

-- 创建标签分类表
CREATE TABLE IF NOT EXISTS `tag_categories` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL COMMENT '分类名称',
    `color` varchar(7) NOT NULL DEFAULT '#6c757d' COMMENT '分类颜色',
    `description` varchar(255) DEFAULT '' COMMENT '分类描述',
    `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
    `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_tag_categories_user_id` (`user_id`),
    KEY `idx_tag_categories_sort_order` (`sort_order`),
    CONSTRAINT `fk_tag_categories_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签分类表';

-- 创建标签分类关联表
CREATE TABLE IF NOT EXISTS `tag_category_relations` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `tag_id` bigint unsigned NOT NULL COMMENT '标签ID',
    `category_id` bigint unsigned NOT NULL COMMENT '分类ID',
    `created_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tag_category` (`tag_id`, `category_id`),
    KEY `idx_tag_category_relations_tag_id` (`tag_id`),
    KEY `idx_tag_category_relations_category_id` (`category_id`),
    CONSTRAINT `fk_tag_category_relations_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_tag_category_relations_category` FOREIGN KEY (`category_id`) REFERENCES `tag_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签分类关联表';

-- 创建性能优化索引
CREATE INDEX `idx_tags_created_at` ON `tags` (`created_at`);
CREATE INDEX `idx_tags_updated_at` ON `tags` (`updated_at`);
CREATE INDEX `idx_image_tags_created_at` ON `image_tags` (`created_at`);
CREATE INDEX `idx_tag_categories_created_at` ON `tag_categories` (`created_at`);
CREATE INDEX `idx_tag_categories_updated_at` ON `tag_categories` (`updated_at`);

-- 创建复合索引用于常见查询
CREATE INDEX `idx_tags_user_usage` ON `tags` (`user_id`, `usage_count` DESC);
CREATE INDEX `idx_tags_user_name` ON `tags` (`user_id`, `name`);
CREATE INDEX `idx_image_tags_user_image` ON `image_tags` (`user_id`, `image_id`);
CREATE INDEX `idx_image_tags_user_tag` ON `image_tags` (`user_id`, `tag_id`);

-- 插入一些默认标签分类（可选）
INSERT IGNORE INTO `tag_categories` (`name`, `color`, `description`, `user_id`, `sort_order`) VALUES
('通用', '#007bff', '通用标签分类', 1, 1),
('主题', '#28a745', '主题相关标签', 1, 2),
('地点', '#17a2b8', '地点相关标签', 1, 3),
('人物', '#ffc107', '人物相关标签', 1, 4),
('事件', '#dc3545', '事件相关标签', 1, 5),
('情感', '#6f42c1', '情感相关标签', 1, 6);

-- 分析表以更新统计信息
ANALYZE TABLE `tags`;
ANALYZE TABLE `image_tags`;
ANALYZE TABLE `tag_categories`;
ANALYZE TABLE `tag_category_relations`;
