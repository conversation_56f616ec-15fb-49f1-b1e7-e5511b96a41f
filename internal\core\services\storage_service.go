package services

import (
	"encoding/json"
	"fmt"
	"cloudbed/internal/core/domain"
	"net"
	"time"
)

// TestStorageConnection 测试存储连接
func TestStorageConnection(provider models.StorageProvider, config interface{}) models.StorageTestResponse {
	switch provider {
	case models.StorageProviderLocal:
		return testLocalStorage(config)
	case models.StorageProviderAliOSS:
		return testAliOSSStorage(config)
	case models.StorageProviderTencentCOS:
		return testTencentCOSStorage(config)
	case models.StorageProviderQiniuKodo:
		return testQiniuKodoStorage(config)
	case models.StorageProviderAWSS3:
		return testAWSS3Storage(config)
	case models.StorageProviderFTP:
		return testFTPStorage(config)
	case models.StorageProviderSFTP:
		return testSFTPStorage(config)
	default:
		return models.StorageTestResponse{
			Success: false,
			Message: "Unsupported storage provider",
		}
	}
}

// testLocalStorage 测试本地存储
func testLocalStorage(config interface{}) models.StorageTestResponse {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid configuration format",
			Details: err.Error(),
		}
	}

	var localConfig models.LocalStorageConfig
	if err := json.Unmarshal(configBytes, &localConfig); err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid local storage configuration",
			Details: err.Error(),
		}
	}

	// 检查路径是否存在或可创建
	// 这里可以添加实际的路径检查逻辑
	return models.StorageTestResponse{
		Success: true,
		Message: "Local storage configuration is valid",
		Details: fmt.Sprintf("Upload path: %s", localConfig.UploadPath),
	}
}

// testAliOSSStorage 测试阿里云OSS存储
func testAliOSSStorage(config interface{}) models.StorageTestResponse {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid configuration format",
			Details: err.Error(),
		}
	}

	var aliConfig models.AliOSSConfig
	if err := json.Unmarshal(configBytes, &aliConfig); err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid Ali OSS configuration",
			Details: err.Error(),
		}
	}

	// 验证必填字段
	if aliConfig.AccessKeyID == "" || aliConfig.AccessKeySecret == "" || 
	   aliConfig.Endpoint == "" || aliConfig.BucketName == "" {
		return models.StorageTestResponse{
			Success: false,
			Message: "Missing required Ali OSS configuration fields",
			Details: "AccessKeyID, AccessKeySecret, Endpoint, and BucketName are required",
		}
	}

	// 这里可以添加实际的OSS连接测试
	return models.StorageTestResponse{
		Success: true,
		Message: "Ali OSS configuration is valid",
		Details: fmt.Sprintf("Endpoint: %s, Bucket: %s", aliConfig.Endpoint, aliConfig.BucketName),
	}
}

// testTencentCOSStorage 测试腾讯云COS存储
func testTencentCOSStorage(config interface{}) models.StorageTestResponse {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid configuration format",
			Details: err.Error(),
		}
	}

	var tencentConfig models.TencentCOSConfig
	if err := json.Unmarshal(configBytes, &tencentConfig); err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid Tencent COS configuration",
			Details: err.Error(),
		}
	}

	// 验证必填字段
	if tencentConfig.SecretID == "" || tencentConfig.SecretKey == "" || 
	   tencentConfig.Region == "" || tencentConfig.BucketName == "" {
		return models.StorageTestResponse{
			Success: false,
			Message: "Missing required Tencent COS configuration fields",
			Details: "SecretID, SecretKey, Region, and BucketName are required",
		}
	}

	return models.StorageTestResponse{
		Success: true,
		Message: "Tencent COS configuration is valid",
		Details: fmt.Sprintf("Region: %s, Bucket: %s", tencentConfig.Region, tencentConfig.BucketName),
	}
}

// testQiniuKodoStorage 测试七牛云Kodo存储
func testQiniuKodoStorage(config interface{}) models.StorageTestResponse {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid configuration format",
			Details: err.Error(),
		}
	}

	var qiniuConfig models.QiniuKodoConfig
	if err := json.Unmarshal(configBytes, &qiniuConfig); err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid Qiniu Kodo configuration",
			Details: err.Error(),
		}
	}

	// 验证必填字段
	if qiniuConfig.AccessKey == "" || qiniuConfig.SecretKey == "" || 
	   qiniuConfig.BucketName == "" || qiniuConfig.Domain == "" {
		return models.StorageTestResponse{
			Success: false,
			Message: "Missing required Qiniu Kodo configuration fields",
			Details: "AccessKey, SecretKey, BucketName, and Domain are required",
		}
	}

	return models.StorageTestResponse{
		Success: true,
		Message: "Qiniu Kodo configuration is valid",
		Details: fmt.Sprintf("Domain: %s, Bucket: %s", qiniuConfig.Domain, qiniuConfig.BucketName),
	}
}

// testAWSS3Storage 测试AWS S3存储
func testAWSS3Storage(config interface{}) models.StorageTestResponse {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid configuration format",
			Details: err.Error(),
		}
	}

	var awsConfig models.AWSS3Config
	if err := json.Unmarshal(configBytes, &awsConfig); err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid AWS S3 configuration",
			Details: err.Error(),
		}
	}

	// 验证必填字段
	if awsConfig.AccessKeyID == "" || awsConfig.SecretAccessKey == "" || 
	   awsConfig.Region == "" || awsConfig.BucketName == "" {
		return models.StorageTestResponse{
			Success: false,
			Message: "Missing required AWS S3 configuration fields",
			Details: "AccessKeyID, SecretAccessKey, Region, and BucketName are required",
		}
	}

	return models.StorageTestResponse{
		Success: true,
		Message: "AWS S3 configuration is valid",
		Details: fmt.Sprintf("Region: %s, Bucket: %s", awsConfig.Region, awsConfig.BucketName),
	}
}

// testFTPStorage 测试FTP存储
func testFTPStorage(config interface{}) models.StorageTestResponse {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid configuration format",
			Details: err.Error(),
		}
	}

	var ftpConfig models.FTPConfig
	if err := json.Unmarshal(configBytes, &ftpConfig); err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid FTP configuration",
			Details: err.Error(),
		}
	}

	// 验证必填字段
	if ftpConfig.Host == "" || ftpConfig.Username == "" {
		return models.StorageTestResponse{
			Success: false,
			Message: "Missing required FTP configuration fields",
			Details: "Host and Username are required",
		}
	}

	// 测试连接
	address := fmt.Sprintf("%s:%d", ftpConfig.Host, ftpConfig.Port)
	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Failed to connect to FTP server",
			Details: err.Error(),
		}
	}
	conn.Close()

	return models.StorageTestResponse{
		Success: true,
		Message: "FTP configuration is valid and server is reachable",
		Details: fmt.Sprintf("Host: %s:%d", ftpConfig.Host, ftpConfig.Port),
	}
}

// testSFTPStorage 测试SFTP存储
func testSFTPStorage(config interface{}) models.StorageTestResponse {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid configuration format",
			Details: err.Error(),
		}
	}

	var sftpConfig models.SFTPConfig
	if err := json.Unmarshal(configBytes, &sftpConfig); err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Invalid SFTP configuration",
			Details: err.Error(),
		}
	}

	// 验证必填字段
	if sftpConfig.Host == "" || sftpConfig.Username == "" {
		return models.StorageTestResponse{
			Success: false,
			Message: "Missing required SFTP configuration fields",
			Details: "Host and Username are required",
		}
	}

	// 验证认证方式
	if sftpConfig.Password == "" && sftpConfig.PrivateKey == "" {
		return models.StorageTestResponse{
			Success: false,
			Message: "Either Password or PrivateKey must be provided",
		}
	}

	// 测试连接
	address := fmt.Sprintf("%s:%d", sftpConfig.Host, sftpConfig.Port)
	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		return models.StorageTestResponse{
			Success: false,
			Message: "Failed to connect to SFTP server",
			Details: err.Error(),
		}
	}
	conn.Close()

	return models.StorageTestResponse{
		Success: true,
		Message: "SFTP configuration is valid and server is reachable",
		Details: fmt.Sprintf("Host: %s:%d", sftpConfig.Host, sftpConfig.Port),
	}
}
