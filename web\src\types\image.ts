export interface Album {
  id: number;
  name: string;
  description: string;
  cover_image: string;
  image_count: number;
  created_at: string;
  updated_at: string;
}

export interface Image {
  id: number;
  name: string;
  size: number;
  url: string;
  user_id: number;
  album_id?: number;
  album?: Album;
  created_at: string;
  updated_at: string;
  // 兼容旧版本
  modified?: string;
}

export interface UploadResponse {
  success: boolean;
  message: string;
  url: string;
  name: string;
  image: Image;
}

export interface AlbumCreateRequest {
  name: string;
  description?: string;
}

export interface AlbumUpdateRequest {
  name: string;
  description?: string;
  cover_image?: string;
}

export interface MoveImagesRequest {
  image_ids: number[];
  album_id?: number;
}