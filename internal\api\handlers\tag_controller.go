package controllers

import (
	"strconv"

	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"
	"cloudbed/pkg/pagination"
	"cloudbed/pkg/response"

	"github.com/gin-gonic/gin"
)

// CreateTagRequest 创建标签请求
type CreateTagRequest struct {
	Name        string `json:"name" binding:"required"`
	Color       string `json:"color"`
	Description string `json:"description"`
}

// UpdateTagRequest 更新标签请求
type UpdateTagRequest struct {
	Name        string `json:"name" binding:"required"`
	Color       string `json:"color"`
	Description string `json:"description"`
}

// AddTagToImageRequest 为图片添加标签请求
type AddTagToImageRequest struct {
	ImageID uint `json:"image_id" binding:"required"`
	TagID   uint `json:"tag_id" binding:"required"`
}

// BatchTagRequest 批量标签操作请求
type BatchTagRequest struct {
	ImageIDs []uint `json:"image_ids" binding:"required"`
	TagIDs   []uint `json:"tag_ids" binding:"required"`
}

// CreateTag 创建标签
func CreateTag(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req CreateTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 检查标签是否已存在
	if existingTag, _ := dao.GetTagByName(userID.(uint), req.Name); existingTag != nil {
		response.BadRequest(c, "标签已存在")
		return
	}

	tag := &models.Tag{
		Name:        req.Name,
		Color:       req.Color,
		Description: req.Description,
		UserID:      userID.(uint),
	}

	if err := dao.CreateTag(tag); err != nil {
		response.InternalServerErrorWithDetail(c, "创建标签失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "标签创建成功", tag)
}

// GetTags 获取用户的所有标签
func GetTags(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	// 解析分页参数
	req := pagination.ParseTagRequest(c)

	// 获取标签列表
	result, err := dao.GetTagsByUserIDPaginated(userID.(uint), req)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取标签失败", err.Error())
		return
	}

	response.Success(c, result)
}

// GetTag 获取单个标签详情
func GetTag(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	tagID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "标签ID格式错误")
		return
	}

	tag, err := dao.GetTagByID(uint(tagID))
	if err != nil {
		response.NotFound(c, "标签不存在")
		return
	}

	// 检查权限
	if tag.UserID != userID.(uint) {
		response.Forbidden(c, "无权访问此标签")
		return
	}

	response.Success(c, tag)
}

// UpdateTag 更新标签
func UpdateTag(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	tagID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "标签ID格式错误")
		return
	}

	var req UpdateTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	tag, err := dao.GetTagByID(uint(tagID))
	if err != nil {
		response.NotFound(c, "标签不存在")
		return
	}

	// 检查权限
	if tag.UserID != userID.(uint) {
		response.Forbidden(c, "无权修改此标签")
		return
	}

	// 检查新名称是否与其他标签冲突
	if req.Name != tag.Name {
		if existingTag, _ := dao.GetTagByName(userID.(uint), req.Name); existingTag != nil {
			response.BadRequest(c, "标签名称已存在")
			return
		}
	}

	// 更新标签信息
	tag.Name = req.Name
	tag.Color = req.Color
	tag.Description = req.Description

	if err := dao.UpdateTag(tag); err != nil {
		response.InternalServerErrorWithDetail(c, "更新标签失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "标签更新成功", tag)
}

// DeleteTag 删除标签
func DeleteTag(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	tagID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "标签ID格式错误")
		return
	}

	tag, err := dao.GetTagByID(uint(tagID))
	if err != nil {
		response.NotFound(c, "标签不存在")
		return
	}

	// 检查权限
	if tag.UserID != userID.(uint) {
		response.Forbidden(c, "无权删除此标签")
		return
	}

	if err := dao.DeleteTag(uint(tagID)); err != nil {
		response.InternalServerErrorWithDetail(c, "删除标签失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "标签删除成功", nil)
}

// GetTagsWithStats 获取带统计信息的标签
func GetTagsWithStats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	tags, err := dao.GetTagsWithStats(userID.(uint))
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取标签统计失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"tags": tags,
	})
}

// GetPopularTags 获取热门标签
func GetPopularTags(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50
	}

	tags, err := dao.GetPopularTags(userID.(uint), limit)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取热门标签失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"tags": tags,
	})
}

// AddTagToImage 为图片添加标签
func AddTagToImage(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req AddTagToImageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 验证图片权限
	image, err := dao.GetImageByIDAndUserID(req.ImageID, userID.(uint))
	if err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 验证标签权限
	tag, err := dao.GetTagByID(req.TagID)
	if err != nil {
		response.NotFound(c, "标签不存在")
		return
	}

	if tag.UserID != userID.(uint) {
		response.Forbidden(c, "无权使用此标签")
		return
	}

	if err := dao.AddTagToImage(req.ImageID, req.TagID, userID.(uint)); err != nil {
		response.InternalServerErrorWithDetail(c, "添加标签失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "标签添加成功", gin.H{
		"image": image,
		"tag":   tag,
	})
}

// RemoveTagFromImage 从图片移除标签
func RemoveTagFromImage(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	imageID, err := strconv.ParseUint(c.Param("image_id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "图片ID格式错误")
		return
	}

	tagID, err := strconv.ParseUint(c.Param("tag_id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "标签ID格式错误")
		return
	}

	// 验证图片权限
	if _, err := dao.GetImageByIDAndUserID(uint(imageID), userID.(uint)); err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 验证标签权限
	tag, err := dao.GetTagByID(uint(tagID))
	if err != nil {
		response.NotFound(c, "标签不存在")
		return
	}

	if tag.UserID != userID.(uint) {
		response.Forbidden(c, "无权操作此标签")
		return
	}

	if err := dao.RemoveTagFromImage(uint(imageID), uint(tagID)); err != nil {
		response.InternalServerErrorWithDetail(c, "移除标签失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "标签移除成功", nil)
}

// GetImageTags 获取图片的标签
func GetImageTags(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	imageID, err := strconv.ParseUint(c.Param("image_id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "图片ID格式错误")
		return
	}

	// 验证图片权限
	if _, err := dao.GetImageByIDAndUserID(uint(imageID), userID.(uint)); err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	tags, err := dao.GetImageTags(uint(imageID))
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取图片标签失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"tags": tags,
	})
}

// GetTagImages 获取标签下的图片
func GetTagImages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	tagID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "标签ID格式错误")
		return
	}

	// 验证标签权限
	tag, err := dao.GetTagByID(uint(tagID))
	if err != nil {
		response.NotFound(c, "标签不存在")
		return
	}

	if tag.UserID != userID.(uint) {
		response.Forbidden(c, "无权访问此标签")
		return
	}

	// 解析分页参数
	req := pagination.ParseRequest(c)

	// 获取标签下的图片
	result, err := dao.GetTagImages(uint(tagID), req)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取标签图片失败", err.Error())
		return
	}

	response.Success(c, result)
}

// BatchAddTags 批量添加标签
func BatchAddTags(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req BatchTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	if len(req.ImageIDs) == 0 || len(req.TagIDs) == 0 {
		response.BadRequest(c, "图片ID或标签ID不能为空")
		return
	}

	if len(req.ImageIDs) > 100 {
		response.BadRequest(c, "单次最多处理100张图片")
		return
	}

	if err := dao.BatchAddTagsToImages(req.ImageIDs, req.TagIDs, userID.(uint)); err != nil {
		response.InternalServerErrorWithDetail(c, "批量添加标签失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "批量添加标签成功", gin.H{
		"image_count": len(req.ImageIDs),
		"tag_count":   len(req.TagIDs),
	})
}

// BatchRemoveTags 批量移除标签
func BatchRemoveTags(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req BatchTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	if len(req.ImageIDs) == 0 || len(req.TagIDs) == 0 {
		response.BadRequest(c, "图片ID或标签ID不能为空")
		return
	}

	if len(req.ImageIDs) > 100 {
		response.BadRequest(c, "单次最多处理100张图片")
		return
	}

	// 验证图片和标签权限（这里简化处理，实际应用中应该验证所有图片和标签都属于当前用户）
	_ = userID // 标记userID已使用，避免编译警告

	if err := dao.BatchRemoveTagsFromImages(req.ImageIDs, req.TagIDs); err != nil {
		response.InternalServerErrorWithDetail(c, "批量移除标签失败", err.Error())
		return
	}

	response.SuccessWithMessage(c, "批量移除标签成功", gin.H{
		"image_count": len(req.ImageIDs),
		"tag_count":   len(req.TagIDs),
	})
}

// SearchTags 搜索标签
func SearchTags(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	query := c.Query("q")
	if query == "" {
		response.BadRequest(c, "搜索关键词不能为空")
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50
	}

	tags, err := dao.SearchTags(userID.(uint), query, limit)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "搜索标签失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"tags":  tags,
		"query": query,
	})
}
