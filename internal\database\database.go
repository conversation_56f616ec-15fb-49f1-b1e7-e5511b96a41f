package database

import (
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"path/filepath"
	"strings"

	"cloudbed/internal/config"
	"cloudbed/internal/core/domain"

	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 自定义错误
var (
	ErrSystemRoleCannotBeDeleted = errors.New("system role cannot be deleted")
	ErrRoleInUse                 = errors.New("role is in use by users")
	ErrPermissionNotFound        = errors.New("permission not found")
	ErrUserNotFound              = errors.New("user not found")
	ErrInsufficientPermissions   = errors.New("insufficient permissions")
)

var DB *gorm.DB

// InitDatabase 初始化数据库
func InitDatabase() error {
	cfg := config.LoadConfig()

	var err error
	var dsn string

	// 根据配置选择数据库类型
	switch cfg.Database.Type {
	case "mysql":
		dsn = cfg.Database.GetMySQLDSN()
		DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
		if err != nil {
			return fmt.Errorf("failed to connect to MySQL: %v", err)
		}
		log.Println("Connected to MySQL database successfully")

		// 对于MySQL，执行完整的schema.sql
		return executeSchemaSQL()

	case "sqlite":
		fallthrough
	default:
		dsn = cfg.Database.SQLite.Path
		DB, err = gorm.Open(sqlite.Open(dsn), &gorm.Config{})
		if err != nil {
			return fmt.Errorf("failed to connect to SQLite: %v", err)
		}
		log.Println("Connected to SQLite database successfully")

		// 对于SQLite，使用简化的初始化
		return initSQLiteSchema()
	}
}

// InitDatabaseSimple 简化的数据库初始化（推荐使用）
func InitDatabaseSimple() error {
	return InitDatabase()
}

// ConnectDatabase 连接到数据库（保持向后兼容）
func ConnectDatabase() {
	// 加载配置
	cfg := config.LoadConfig()

	var err error

	// 根据配置选择数据库类型
	switch cfg.Database.Type {
	case "mysql":
		// 连接到MySQL数据库
		DB, err = gorm.Open(mysql.Open(cfg.Database.GetMySQLDSN()), &gorm.Config{})
		if err != nil {
			log.Fatal("Failed to connect to MySQL database:", err)
		}
		log.Println("Connected to MySQL database successfully")
	case "sqlite":
		fallthrough
	default:
		// 连接到SQLite数据库
		DB, err = gorm.Open(sqlite.Open(cfg.Database.SQLite.Path), &gorm.Config{})
		if err != nil {
			log.Fatal("Failed to connect to SQLite database:", err)
		}
		log.Println("Connected to SQLite database successfully")
	}

	// 分步迁移数据库模型以避免外键约束问题
	// 1. 先创建角色和权限表
	err = DB.AutoMigrate(&models.Role{}, &models.Permission{})
	if err != nil {
		log.Fatal("Failed to migrate roles and permissions tables:", err)
	}

	// 2. 创建角色权限关联表
	err = DB.AutoMigrate(&models.RolePermission{})
	if err != nil {
		log.Fatal("Failed to migrate role permissions table:", err)
	}

	// 3. 创建用户组表
	err = DB.AutoMigrate(&models.Group{})
	if err != nil {
		log.Fatal("Failed to migrate groups table:", err)
	}

	// 4. 创建用户组权限关联表
	err = DB.AutoMigrate(&models.GroupPermission{})
	if err != nil {
		log.Fatal("Failed to migrate group permissions table:", err)
	}

	// 5. 创建用户表
	err = DB.AutoMigrate(&models.User{})
	if err != nil {
		log.Fatal("Failed to migrate users table:", err)
	}

	// 6. 创建用户权限关联表
	err = DB.AutoMigrate(&models.UserPermission{})
	if err != nil {
		log.Fatal("Failed to migrate user permissions table:", err)
	}

	// 7. 创建用户组关联表
	err = DB.AutoMigrate(&models.UserGroup{})
	if err != nil {
		log.Fatal("Failed to migrate user groups table:", err)
	}

	// 8. 创建相册表
	err = DB.AutoMigrate(&models.Album{})
	if err != nil {
		log.Fatal("Failed to migrate albums table:", err)
	}

	// 9. 创建图片表
	err = DB.AutoMigrate(&models.Image{})
	if err != nil {
		log.Fatal("Failed to migrate images table:", err)
	}

	// 10. 创建存储配置表
	err = DB.AutoMigrate(&models.StorageConfig{})
	if err != nil {
		log.Fatal("Failed to migrate storage configs table:", err)
	}

	// 11. 创建默认存储配置（如果不存在）
	createDefaultStorageConfig()

	// 12. 手动添加用户表的默认相册外键约束（如果使用MySQL）
	if cfg.Database.Type == "mysql" {
		// 检查外键是否已存在
		var count int64
		DB.Raw("SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' AND CONSTRAINT_NAME = 'fk_users_default_album'", cfg.Database.MySQL.Name).Scan(&count)

		if count == 0 {
			// 添加外键约束
			err = DB.Exec("ALTER TABLE users ADD CONSTRAINT fk_users_default_album FOREIGN KEY (default_album_id) REFERENCES albums(id) ON DELETE SET NULL ON UPDATE CASCADE").Error
			if err != nil {
				log.Printf("Warning: Failed to add foreign key constraint for default_album_id: %v", err)
			}
		}
	}

	log.Println("Database migrated successfully")
}

// createDefaultStorageConfig 创建默认存储配置
func createDefaultStorageConfig() {
	var count int64
	DB.Model(&models.StorageConfig{}).Count(&count)

	// 如果没有存储配置，创建默认的本地存储配置
	if count == 0 {
		// 从配置文件获取基础URL，如果没有配置则使用localhost
		cfg := config.LoadConfig()
		defaultAccessURL := config.GetBaseURL(fmt.Sprintf("localhost:%s", cfg.Server.Port), false, cfg)

		defaultConfig := &models.StorageConfig{
			Name:        "默认本地存储",
			Provider:    models.StorageProviderLocal,
			IsDefault:   true,
			IsEnabled:   true,
			AccessURL:   defaultAccessURL,
			StoragePath: "./uploads",
			Config:      `{"upload_path":"./uploads","max_size":104857600}`,
		}

		if err := DB.Create(defaultConfig).Error; err != nil {
			log.Printf("Failed to create default storage config: %v", err)
		} else {
			log.Println("Default local storage config created successfully")
		}
	}
}

// executeSchemaSQL 执行完整的schema.sql文件
func executeSchemaSQL() error {
	log.Println("Executing schema.sql...")

	// 读取schema.sql文件
	schemaPath := filepath.Join("database", "schema.sql")
	content, err := ioutil.ReadFile(schemaPath)
	if err != nil {
		return fmt.Errorf("failed to read schema.sql: %v", err)
	}

	// 分割SQL语句（以分号分隔）
	sqlStatements := strings.Split(string(content), ";")

	// 执行每个SQL语句
	for i, stmt := range sqlStatements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") {
			continue
		}

		// 跳过一些MySQL特定的语句
		if strings.Contains(stmt, "CREATE DATABASE") ||
		   strings.Contains(stmt, "USE `image_backup`") ||
		   strings.Contains(stmt, "DELIMITER") ||
		   strings.Contains(stmt, "CREATE PROCEDURE") ||
		   strings.Contains(stmt, "CREATE TRIGGER") {
			continue
		}

		err := DB.Exec(stmt).Error
		if err != nil {
			log.Printf("Warning: Failed to execute SQL statement %d: %v", i+1, err)
			log.Printf("Statement: %s", stmt)
			// 继续执行其他语句，不中断整个过程
		}
	}

	log.Println("Schema initialization completed")
	return nil
}

// initSQLiteSchema 初始化SQLite数据库结构（简化版本）
func initSQLiteSchema() error {
	log.Println("Initializing SQLite schema...")

	// 基本表结构的SQL语句
	sqlStatements := []string{
		// 角色表
		`CREATE TABLE IF NOT EXISTS roles (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name VARCHAR(50) NOT NULL UNIQUE,
			display_name VARCHAR(100) NOT NULL,
			description VARCHAR(500),
			is_system BOOLEAN NOT NULL DEFAULT 0,
			created_at DATETIME,
			updated_at DATETIME
		)`,

		// 权限表
		`CREATE TABLE IF NOT EXISTS permissions (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name VARCHAR(50) NOT NULL UNIQUE,
			display_name VARCHAR(100) NOT NULL,
			description VARCHAR(500),
			resource VARCHAR(50) NOT NULL,
			action VARCHAR(50) NOT NULL,
			created_at DATETIME,
			updated_at DATETIME
		)`,

		// 角色权限关联表
		`CREATE TABLE IF NOT EXISTS role_permissions (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			role_id INTEGER NOT NULL,
			permission_id INTEGER NOT NULL,
			created_at DATETIME,
			FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
			FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
			UNIQUE(role_id, permission_id)
		)`,

		// 用户表
		`CREATE TABLE IF NOT EXISTS users (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			username VARCHAR(50) NOT NULL UNIQUE,
			email VARCHAR(191) NOT NULL UNIQUE,
			password VARCHAR(191) NOT NULL,
			role_id INTEGER NOT NULL DEFAULT 3,
			status VARCHAR(20) NOT NULL DEFAULT 'active',
			last_login_at DATETIME,
			storage_quota INTEGER NOT NULL DEFAULT 1073741824,
			storage_used INTEGER NOT NULL DEFAULT 0,
			default_album_id INTEGER,
			created_at DATETIME,
			updated_at DATETIME,
			FOREIGN KEY (role_id) REFERENCES roles(id)
		)`,

		// 相册表
		`CREATE TABLE IF NOT EXISTS albums (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name VARCHAR(100) NOT NULL,
			description VARCHAR(500),
			cover_image VARCHAR(500),
			user_id INTEGER NOT NULL,
			created_at DATETIME,
			updated_at DATETIME,
			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
		)`,

		// 图片表
		`CREATE TABLE IF NOT EXISTS images (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name VARCHAR(191) NOT NULL,
			url VARCHAR(500) NOT NULL,
			size INTEGER,
			width INTEGER DEFAULT 0,
			height INTEGER DEFAULT 0,
			format VARCHAR(10) DEFAULT '',
			thumbnails TEXT,
			user_id INTEGER NOT NULL,
			album_id INTEGER,
			created_at DATETIME,
			updated_at DATETIME,
			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
			FOREIGN KEY (album_id) REFERENCES albums(id) ON DELETE SET NULL
		)`,

		// 存储配置表
		`CREATE TABLE IF NOT EXISTS storage_configs (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name VARCHAR(100) NOT NULL,
			provider VARCHAR(50) NOT NULL,
			is_default BOOLEAN NOT NULL DEFAULT 0,
			is_enabled BOOLEAN NOT NULL DEFAULT 1,
			access_url VARCHAR(500),
			storage_path VARCHAR(500),
			config TEXT,
			created_at DATETIME,
			updated_at DATETIME
		)`,
	}

	// 执行SQL语句
	for i, stmt := range sqlStatements {
		err := DB.Exec(stmt).Error
		if err != nil {
			return fmt.Errorf("failed to execute SQL statement %d: %v", i+1, err)
		}
	}

	// 插入基础数据
	return insertBasicData()
}

// insertBasicData 插入基础数据
func insertBasicData() error {
	log.Println("Inserting basic data...")

	// 插入系统角色
	roles := []string{
		"INSERT OR IGNORE INTO roles (id, name, display_name, description, is_system, created_at, updated_at) VALUES (1, 'super_admin', '超级管理员', '拥有系统所有权限的超级管理员', 1, datetime('now'), datetime('now'))",
		"INSERT OR IGNORE INTO roles (id, name, display_name, description, is_system, created_at, updated_at) VALUES (2, 'admin', '管理员', '拥有大部分管理权限的管理员', 1, datetime('now'), datetime('now'))",
		"INSERT OR IGNORE INTO roles (id, name, display_name, description, is_system, created_at, updated_at) VALUES (3, 'user', '普通用户', '普通用户，只能管理自己的内容', 1, datetime('now'), datetime('now'))",
		"INSERT OR IGNORE INTO roles (id, name, display_name, description, is_system, created_at, updated_at) VALUES (4, 'guest', '访客', '只读权限的访客用户', 1, datetime('now'), datetime('now'))",
	}

	for _, sql := range roles {
		if err := DB.Exec(sql).Error; err != nil {
			log.Printf("Warning: Failed to insert role: %v", err)
		}
	}

	// 插入基本权限
	permissions := []string{
		"INSERT OR IGNORE INTO permissions (name, display_name, description, resource, action, created_at, updated_at) VALUES ('image.view_own', '查看自己的图片', '查看自己上传的图片', 'image', 'view_own', datetime('now'), datetime('now'))",
		"INSERT OR IGNORE INTO permissions (name, display_name, description, resource, action, created_at, updated_at) VALUES ('image.upload', '上传图片', '上传新图片', 'image', 'upload', datetime('now'), datetime('now'))",
		"INSERT OR IGNORE INTO permissions (name, display_name, description, resource, action, created_at, updated_at) VALUES ('image.update_own', '编辑自己的图片', '编辑自己的图片信息', 'image', 'update_own', datetime('now'), datetime('now'))",
		"INSERT OR IGNORE INTO permissions (name, display_name, description, resource, action, created_at, updated_at) VALUES ('image.delete_own', '删除自己的图片', '删除自己的图片', 'image', 'delete_own', datetime('now'), datetime('now'))",
		"INSERT OR IGNORE INTO permissions (name, display_name, description, resource, action, created_at, updated_at) VALUES ('album.view_own', '查看自己的相册', '查看自己创建的相册', 'album', 'view_own', datetime('now'), datetime('now'))",
		"INSERT OR IGNORE INTO permissions (name, display_name, description, resource, action, created_at, updated_at) VALUES ('album.create', '创建相册', '创建新相册', 'album', 'create', datetime('now'), datetime('now'))",
		"INSERT OR IGNORE INTO permissions (name, display_name, description, resource, action, created_at, updated_at) VALUES ('album.update_own', '编辑自己的相册', '编辑自己的相册', 'album', 'update_own', datetime('now'), datetime('now'))",
		"INSERT OR IGNORE INTO permissions (name, display_name, description, resource, action, created_at, updated_at) VALUES ('album.delete_own', '删除自己的相册', '删除自己的相册', 'album', 'delete_own', datetime('now'), datetime('now'))",
	}

	for _, sql := range permissions {
		if err := DB.Exec(sql).Error; err != nil {
			log.Printf("Warning: Failed to insert permission: %v", err)
		}
	}

	// 为普通用户角色分配基本权限
	rolePermissions := `
		INSERT OR IGNORE INTO role_permissions (role_id, permission_id, created_at)
		SELECT 3, p.id, datetime('now') FROM permissions p
		WHERE p.name IN ('image.view_own', 'image.upload', 'image.update_own', 'image.delete_own', 'album.view_own', 'album.create', 'album.update_own', 'album.delete_own')
	`
	if err := DB.Exec(rolePermissions).Error; err != nil {
		log.Printf("Warning: Failed to assign permissions to user role: %v", err)
	}

	// 创建默认管理员用户
	adminUser := `INSERT OR IGNORE INTO users (username, email, password, role_id, status, created_at, updated_at) VALUES ('admin', '<EMAIL>', '$2a$08$RDPgNLCMZiBiOGQOhhKzguqtziCs5o.gPs6nkgmfMpCKzZZ0qgSZi', 1, 'active', datetime('now'), datetime('now'))`
	if err := DB.Exec(adminUser).Error; err != nil {
		log.Printf("Warning: Failed to create admin user: %v", err)
	}

	// 创建默认存储配置
	storageConfig := `INSERT OR IGNORE INTO storage_configs (name, provider, is_default, is_enabled, access_url, storage_path, config, created_at, updated_at) VALUES ('默认本地存储', 'local', 1, 1, 'http://localhost:18080', './uploads', '{"upload_path":"./uploads","max_size":104857600}', datetime('now'), datetime('now'))`
	if err := DB.Exec(storageConfig).Error; err != nil {
		log.Printf("Warning: Failed to create storage config: %v", err)
	}

	log.Println("Basic data insertion completed")
	return nil
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return DB
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}
