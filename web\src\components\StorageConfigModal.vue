<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>{{ config ? '编辑存储配置' : '添加存储配置' }}</h3>
        <button class="modal-close" @click="$emit('close')">
          <i class="bi bi-x"></i>
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <!-- 基本信息 -->
          <div class="form-section">
            <h4>基本信息</h4>
            
            <div class="form-group">
              <label for="name">配置名称 *</label>
              <input 
                id="name"
                v-model="form.name" 
                type="text" 
                required 
                placeholder="输入配置名称"
                class="form-control"
              />
            </div>

            <div class="form-group">
              <label for="provider">存储提供商 *</label>
              <select 
                id="provider"
                v-model="form.provider" 
                required 
                class="form-control"
                @change="onProviderChange"
              >
                <option value="">请选择存储提供商</option>
                <option 
                  v-for="provider in STORAGE_PROVIDERS" 
                  :key="provider.value"
                  :value="provider.value"
                >
                  {{ provider.label }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label for="access_url">访问域名</label>
              <input
                id="access_url"
                v-model="form.access_url"
                type="url"
                :placeholder="getAccessUrlPlaceholder()"
                class="form-control"
              />
              <small class="form-text">
                图片访问的基础URL。部署到服务器时请设置为您的域名（如：https://your-domain.com）。
                <br>
                留空则自动使用当前访问地址。
              </small>
            </div>

            <div class="form-group">
              <label for="storage_path">存储路径</label>
              <input 
                id="storage_path"
                v-model="form.storage_path" 
                type="text" 
                placeholder="/uploads"
                class="form-control"
              />
              <small class="form-text">文件存储的路径</small>
            </div>

            <div class="form-group">
              <div class="form-check">
                <input 
                  id="is_default"
                  v-model="form.is_default" 
                  type="checkbox" 
                  class="form-check-input"
                />
                <label for="is_default" class="form-check-label">设为默认配置</label>
              </div>
            </div>

            <div class="form-group">
              <div class="form-check">
                <input 
                  id="is_enabled"
                  v-model="form.is_enabled" 
                  type="checkbox" 
                  class="form-check-input"
                />
                <label for="is_enabled" class="form-check-label">启用此配置</label>
              </div>
            </div>
          </div>

          <!-- 存储配置 -->
          <div class="form-section" v-if="form.provider">
            <h4>{{ getProviderInfo(form.provider).label }}配置</h4>
            <p class="section-description">{{ getProviderInfo(form.provider).description }}</p>

            <!-- 本地存储配置 -->
            <template v-if="form.provider === 'local'">
              <div class="form-group">
                <label for="upload_path">上传路径 *</label>
                <input 
                  id="upload_path"
                  v-model="form.config.upload_path" 
                  type="text" 
                  required 
                  placeholder="./uploads"
                  class="form-control"
                />
              </div>
              <div class="form-group">
                <label for="max_size">最大文件大小 (字节)</label>
                <input 
                  id="max_size"
                  v-model.number="form.config.max_size" 
                  type="number" 
                  min="1"
                  placeholder="104857600"
                  class="form-control"
                />
                <small class="form-text">默认100MB (104857600字节)</small>
              </div>
            </template>

            <!-- 阿里云OSS配置 -->
            <template v-else-if="form.provider === 'ali_oss'">
              <div class="form-row">
                <div class="form-group">
                  <label for="access_key_id">Access Key ID *</label>
                  <input 
                    id="access_key_id"
                    v-model="form.config.access_key_id" 
                    type="text" 
                    required 
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label for="access_key_secret">Access Key Secret *</label>
                  <input 
                    id="access_key_secret"
                    v-model="form.config.access_key_secret" 
                    type="password" 
                    required 
                    class="form-control"
                  />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label for="endpoint">Endpoint *</label>
                  <input 
                    id="endpoint"
                    v-model="form.config.endpoint" 
                    type="text" 
                    required 
                    placeholder="oss-cn-hangzhou.aliyuncs.com"
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label for="bucket_name">Bucket名称 *</label>
                  <input 
                    id="bucket_name"
                    v-model="form.config.bucket_name" 
                    type="text" 
                    required 
                    class="form-control"
                  />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label for="region">区域</label>
                  <input 
                    id="region"
                    v-model="form.config.region" 
                    type="text" 
                    placeholder="cn-hangzhou"
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label for="path_prefix">路径前缀</label>
                  <input 
                    id="path_prefix"
                    v-model="form.config.path_prefix" 
                    type="text" 
                    placeholder="images/"
                    class="form-control"
                  />
                </div>
              </div>
            </template>

            <!-- 腾讯云COS配置 -->
            <template v-else-if="form.provider === 'tencent_cos'">
              <div class="form-row">
                <div class="form-group">
                  <label for="secret_id">Secret ID *</label>
                  <input 
                    id="secret_id"
                    v-model="form.config.secret_id" 
                    type="text" 
                    required 
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label for="secret_key">Secret Key *</label>
                  <input 
                    id="secret_key"
                    v-model="form.config.secret_key" 
                    type="password" 
                    required 
                    class="form-control"
                  />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label for="cos_region">区域 *</label>
                  <input 
                    id="cos_region"
                    v-model="form.config.region" 
                    type="text" 
                    required 
                    placeholder="ap-beijing"
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label for="cos_bucket_name">Bucket名称 *</label>
                  <input 
                    id="cos_bucket_name"
                    v-model="form.config.bucket_name" 
                    type="text" 
                    required 
                    class="form-control"
                  />
                </div>
              </div>
              <div class="form-group">
                <label for="cos_path_prefix">路径前缀</label>
                <input 
                  id="cos_path_prefix"
                  v-model="form.config.path_prefix" 
                  type="text" 
                  placeholder="images/"
                  class="form-control"
                />
              </div>
            </template>

            <!-- FTP配置 -->
            <template v-else-if="form.provider === 'ftp'">
              <div class="form-row">
                <div class="form-group">
                  <label for="ftp_host">主机地址 *</label>
                  <input 
                    id="ftp_host"
                    v-model="form.config.host" 
                    type="text" 
                    required 
                    placeholder="ftp.example.com"
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label for="ftp_port">端口</label>
                  <input 
                    id="ftp_port"
                    v-model.number="form.config.port" 
                    type="number" 
                    min="1"
                    max="65535"
                    placeholder="21"
                    class="form-control"
                  />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label for="ftp_username">用户名 *</label>
                  <input 
                    id="ftp_username"
                    v-model="form.config.username" 
                    type="text" 
                    required 
                    class="form-control"
                  />
                </div>
                <div class="form-group">
                  <label for="ftp_password">密码</label>
                  <input 
                    id="ftp_password"
                    v-model="form.config.password" 
                    type="password" 
                    class="form-control"
                  />
                </div>
              </div>
              <div class="form-group">
                <label for="ftp_remote_path">远程路径</label>
                <input 
                  id="ftp_remote_path"
                  v-model="form.config.remote_path" 
                  type="text" 
                  placeholder="/uploads"
                  class="form-control"
                />
              </div>
              <div class="form-group">
                <div class="form-check">
                  <input 
                    id="ftp_passive_mode"
                    v-model="form.config.passive_mode" 
                    type="checkbox" 
                    class="form-check-input"
                  />
                  <label for="ftp_passive_mode" class="form-check-label">被动模式</label>
                </div>
              </div>
            </template>

            <!-- 其他配置类型的占位符 -->
            <template v-else>
              <div class="form-group">
                <label>配置信息</label>
                <textarea 
                  v-model="configJson" 
                  rows="10" 
                  placeholder="请输入JSON格式的配置信息"
                  class="form-control"
                ></textarea>
                <small class="form-text">请输入有效的JSON格式配置</small>
              </div>
            </template>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" @click="$emit('close')">
          取消
        </button>
        <button 
          type="button" 
          class="btn btn-outline-primary" 
          @click="testConnection"
          :disabled="!canTest || testing"
        >
          <i class="bi" :class="testing ? 'bi-arrow-clockwise spinning' : 'bi-check-circle'"></i>
          {{ testing ? '测试中...' : '测试连接' }}
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          @click="handleSubmit"
          :disabled="!canSave || saving"
        >
          <i class="bi" :class="saving ? 'bi-arrow-clockwise spinning' : 'bi-check'"></i>
          {{ saving ? '保存中...' : '保存' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import api from '../services/api';
import type { StorageConfig, StorageConfigRequest } from '../types/storage-management';
import { STORAGE_PROVIDERS, getStorageProviderInfo, getDefaultConfig } from '../types/storage-management';
import { useDialog } from '../utils/dialog';

// Props
const props = defineProps<{
  config?: StorageConfig | null;
}>();

// Emits
const emit = defineEmits<{
  close: [];
  save: [];
}>();

// 响应式数据
const saving = ref(false);
const testing = ref(false);
const configJson = ref('');
const { notify } = useDialog();

const form = reactive<StorageConfigRequest>({
  name: '',
  provider: 'local' as any,
  is_default: false,
  is_enabled: true,
  access_url: '',
  storage_path: '',
  config: {}
});

// 计算属性
const canTest = computed(() => {
  return form.provider && form.name;
});

const canSave = computed(() => {
  return form.provider && form.name;
});

// 监听器
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    Object.assign(form, {
      name: newConfig.name,
      provider: newConfig.provider,
      is_default: newConfig.is_default,
      is_enabled: newConfig.is_enabled,
      access_url: newConfig.access_url,
      storage_path: newConfig.storage_path,
      config: newConfig.config || {}
    });
  } else {
    resetForm();
  }
}, { immediate: true });

// 生命周期
onMounted(() => {
  if (!props.config) {
    resetForm();
  }
});

// 方法
const resetForm = () => {
  Object.assign(form, {
    name: '',
    provider: 'local' as any,
    is_default: false,
    is_enabled: true,
    access_url: '',
    storage_path: '',
    config: getDefaultConfig('local')
  });
};

// 获取访问URL的占位符
const getAccessUrlPlaceholder = () => {
  const currentUrl = window.location.origin;
  return currentUrl;
};

const onProviderChange = () => {
  form.config = getDefaultConfig(form.provider);
};

const getProviderInfo = getStorageProviderInfo;

const testConnection = async () => {
  testing.value = true;
  try {
    const response = await api.testStorageConfig({
      provider: form.provider,
      config: form.config
    });
    
    if (response.success) {
      notify.success('连接测试成功！');
    } else {
      notify.error(`连接测试失败：${response.message}`);
    }
  } catch (error) {
    notify.error('连接测试失败，请检查配置');
  } finally {
    testing.value = false;
  }
};

const handleSubmit = async () => {
  saving.value = true;
  try {
    if (props.config) {
      await api.updateStorageConfig(props.config.id, form);
    } else {
      await api.createStorageConfig(form);
    }
    emit('save');
  } catch (error) {
    notify.error('保存失败，请检查配置');
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.modal-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.section-description {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 14px;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-text {
  display: block;
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

.form-check {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-check-input {
  margin: 0;
}

.form-check-label {
  margin: 0;
  cursor: pointer;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.btn-outline-primary {
  border: 1px solid #007bff;
  color: #007bff;
  background: transparent;
}

.btn-outline-primary:hover:not(:disabled) {
  background: #007bff;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 10px;
  }
}
</style>
