package controllers

import (
	"log"
	"net/http"
	"strconv"

	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"

	"github.com/gin-gonic/gin"
)

// GetAllImages 获取所有图片（管理员）
func GetAllImages(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "20"))
	search := c.Query("search")
	userID := c.Query("user_id")
	albumID := c.Query("album_id")
	sortBy := c.<PERSON>fault<PERSON>("sort_by", "created_at")
	sortDesc := c.<PERSON>("sort_desc", "true") == "true"

	// 构建请求参数
	req := models.ImageListRequest{
		Page:     page,
		PageSize: pageSize,
		Search:   search,
		SortBy:   sortBy,
		SortDesc: sortDesc,
	}

	if userID != "" {
		if uid, err := strconv.ParseUint(userID, 10, 32); err == nil {
			req.UserID = uint(uid)
		}
	}

	if albumID != "" {
		if aid, err := strconv.ParseUint(albumID, 10, 32); err == nil {
			req.AlbumID = uint(aid)
		}
	}

	// 获取图片列表
	response, err := dao.GetAllImagesPaginated(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch images"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetImageStats 获取图片统计信息
func GetImageStats(c *gin.Context) {
	stats, err := dao.GetImageStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch image stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// DeleteImageByAdmin 管理员删除图片
func DeleteImageByAdmin(c *gin.Context) {
	imageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid image ID"})
		return
	}

	// 获取图片信息
	image, err := dao.GetImageByID(uint(imageID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Image not found"})
		return
	}

	// 删除物理文件
	if err := removeFile("./uploads/" + image.Name); err != nil {
		log.Printf("Failed to delete file %s: %v", image.Name, err)
		// 继续删除数据库记录，即使文件删除失败
	}

	// 删除数据库记录
	if err := dao.DeleteImage(uint(imageID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete image record"})
		return
	}

	// 更新用户存储使用量
	if err := UpdateStorageUsage(image.UserID, -image.Size); err != nil {
		log.Printf("Failed to update storage usage for user %d: %v", image.UserID, err)
		// 不返回错误，因为图片已经删除成功
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Image deleted successfully",
		"image":   image,
	})
}

// BulkDeleteImages 批量删除图片
func BulkDeleteImages(c *gin.Context) {
	var req struct {
		ImageIDs []uint `json:"image_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if len(req.ImageIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No image IDs provided"})
		return
	}

	// 获取所有要删除的图片信息
	images, err := dao.GetImagesByIDs(req.ImageIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get images"})
		return
	}

	// 删除物理文件
	for _, image := range images {
		if err := removeFile("./uploads/" + image.Name); err != nil {
			log.Printf("Failed to delete file %s: %v", image.Name, err)
			// 继续删除其他文件，即使某个文件删除失败
		}
	}

	// 批量删除数据库记录
	deletedCount, err := dao.BulkDeleteImages(req.ImageIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete images"})
		return
	}

	// 更新用户存储使用量
	for _, image := range images {
		if err := UpdateStorageUsage(image.UserID, -image.Size); err != nil {
			log.Printf("Failed to update storage usage for user %d: %v", image.UserID, err)
			// 不返回错误，因为图片已经删除成功
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Images deleted successfully",
		"deleted_count": deletedCount,
	})
}

// GetImageDetail 获取图片详细信息
func GetImageDetail(c *gin.Context) {
	imageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid image ID"})
		return
	}

	// 获取图片详细信息
	image, err := dao.GetImageDetailByID(uint(imageID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Image not found"})
		return
	}

	c.JSON(http.StatusOK, image)
}

// GetUsersForFilter 获取用户列表用于过滤
func GetUsersForFilter(c *gin.Context) {
	users, err := dao.GetUsersForFilter()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch users"})
		return
	}

	c.JSON(http.StatusOK, users)
}

// GetAlbumsForFilter 获取相册列表用于过滤
func GetAlbumsForFilter(c *gin.Context) {
	albums, err := dao.GetAlbumsForFilter()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch albums"})
		return
	}

	c.JSON(http.StatusOK, albums)
}
