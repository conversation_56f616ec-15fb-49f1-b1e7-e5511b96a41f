<template>
  <div class="groups-view">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2><i class="bi bi-people-fill"></i> 角色组管理</h2>
          <p class="page-subtitle">管理角色组和权限分配</p>
        </div>
        <div class="header-right">
          <button class="btn btn-secondary" @click="fetchGroups">
            <i class="bi bi-arrow-clockwise"></i> 刷新
          </button>
          <button class="btn btn-primary" @click="showCreateModal = true">
            <i class="bi bi-plus"></i> 创建角色组
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-people-fill"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ groups.length }}</div>
          <div class="stat-label">总角色组</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-check-circle-fill"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ activeGroups }}</div>
          <div class="stat-label">活跃角色组</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="bi bi-person-check-fill"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ totalMembers }}</div>
          <div class="stat-label">总成员数</div>
        </div>
      </div>
    </div>

    <!-- 用户组列表 -->
    <div class="groups-table-container">
      <table class="groups-table">
        <thead>
          <tr>
            <th>角色组</th>
            <th>成员数量</th>
            <th>状态</th>
            <th>创建者</th>
            <th>创建时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <!-- 加载状态 -->
          <tr v-if="loading">
            <td colspan="6" class="text-center">
              <div class="loading-spinner">
                <i class="bi bi-arrow-clockwise spinning"></i>
                <span>加载中...</span>
              </div>
            </td>
          </tr>
          
          <!-- 错误状态 -->
          <tr v-else-if="error">
            <td colspan="6" class="text-center">
              <div class="error-state">
                <i class="bi bi-exclamation-triangle"></i>
                <span>{{ error }}</span>
                <button class="btn btn-sm btn-primary" @click="fetchGroups">重试</button>
              </div>
            </td>
          </tr>
          
          <!-- 无数据状态 -->
          <tr v-else-if="groups.length === 0">
            <td colspan="6" class="text-center">
              <div class="no-data">
                <i class="bi bi-inbox"></i>
                <span>暂无角色组</span>
              </div>
            </td>
          </tr>
          
          <!-- 用户组数据 -->
          <tr v-else v-for="group in groups" :key="group.id">
            <td>
              <div class="group-info">
                <div class="group-icon">
                  <i class="bi bi-people-fill"></i>
                </div>
                <div class="group-details">
                  <div class="group-name">{{ group.display_name }}</div>
                  <div class="group-description" v-if="group.description">{{ group.description }}</div>
                </div>
              </div>
            </td>
            <td>
              <span class="member-count">{{ group.member_count || 0 }}</span>
            </td>
            <td>
              <div class="status-badges">
                <span class="status-badge" :class="getStatusClass(group)">
                  <i class="bi" :class="group.is_active ? 'bi-check-circle' : 'bi-x-circle'"></i>
                  {{ group.is_active ? '活跃' : '禁用' }}
                </span>
                <span v-if="group.is_default" class="status-badge status-default">
                  <i class="bi bi-star-fill"></i>
                  默认组
                </span>
                <span v-if="group.is_system" class="status-badge status-system">
                  <i class="bi bi-shield-fill"></i>
                  系统组
                </span>
              </div>
            </td>
            <td>
              <div class="creator-info" v-if="group.creator">
                <span class="creator-name">{{ group.creator.email }}</span>
              </div>
              <span v-else class="text-muted">系统</span>
            </td>
            <td>
              <span class="create-time">{{ formatDate(group.created_at) }}</span>
            </td>
            <td>
              <div class="action-buttons">
                <button class="btn btn-sm btn-outline-primary" @click="viewGroup(group)" title="查看详情">
                  <i class="bi bi-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-info" @click="manageMembers(group)" title="管理成员">
                  <i class="bi bi-people"></i>
                </button>
                <button class="btn btn-sm btn-outline-warning" @click="managePermissions(group)" title="管理权限">
                  <i class="bi bi-shield-check"></i>
                </button>
                <button
                  v-if="!group.is_default && group.is_active"
                  class="btn btn-sm btn-outline-success"
                  @click="setDefaultGroup(group)"
                  title="设为默认组"
                >
                  <i class="bi bi-star"></i>
                </button>
                <button
                  class="btn btn-sm btn-outline-secondary"
                  @click="editGroup(group)"
                  :title="group.is_system ? '配置系统组' : '编辑'"
                >
                  <i class="bi bi-pencil"></i>
                </button>
                <button
                  v-if="!group.is_system && !group.is_default"
                  class="btn btn-sm btn-outline-danger"
                  @click="deleteGroup(group)"
                  title="删除"
                >
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 创建/编辑用户组模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModals">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>
            {{ showCreateModal ? '创建角色组' : (currentGroup?.is_system ? '配置系统角色组' : '编辑角色组') }}
          </h3>
          <button class="close-btn" @click="closeModals">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitGroup">
            <div class="form-group">
              <label for="groupName">组名称（英文）</label>
              <input
                id="groupName"
                v-model="groupForm.name"
                type="text"
                class="form-control"
                :disabled="showEditModal"
                placeholder="例如：developers"
                required
              >
            </div>
            <div class="form-group">
              <label for="groupDisplayName">显示名称</label>
              <input
                id="groupDisplayName"
                v-model="groupForm.display_name"
                type="text"
                class="form-control"
                :disabled="currentGroup?.is_system"
                placeholder="例如：开发人员"
                required
              >
              <div v-if="currentGroup?.is_system" class="form-text">系统组的显示名称不可修改</div>
            </div>
            <div class="form-group">
              <label for="groupDescription">描述</label>
              <textarea
                id="groupDescription"
                v-model="groupForm.description"
                class="form-control"
                rows="3"
                placeholder="角色组描述（可选）"
              ></textarea>
            </div>

            <!-- 上传限制配置 -->
            <div class="form-section">
              <h4>上传限制配置</h4>
              <div class="form-row">
                <div class="form-group">
                  <label for="maxFileSize">最大文件大小 (KB)</label>
                  <input
                    id="maxFileSize"
                    v-model.number="groupForm.max_file_size"
                    type="number"
                    class="form-control"
                    min="1"
                    max="1048576"
                    placeholder="10240"
                  >
                </div>
                <div class="form-group">
                  <label for="concurrentUploads">并发上传限制</label>
                  <input
                    id="concurrentUploads"
                    v-model.number="groupForm.concurrent_uploads"
                    type="number"
                    class="form-control"
                    min="1"
                    max="10"
                    placeholder="3"
                  >
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label for="uploadLimitMinute">每分钟上传限制</label>
                  <input
                    id="uploadLimitMinute"
                    v-model.number="groupForm.upload_limit_minute"
                    type="number"
                    class="form-control"
                    min="0"
                    max="1000"
                    placeholder="10"
                  >
                </div>
                <div class="form-group">
                  <label for="uploadLimitHour">每小时上传限制</label>
                  <input
                    id="uploadLimitHour"
                    v-model.number="groupForm.upload_limit_hour"
                    type="number"
                    class="form-control"
                    min="0"
                    max="10000"
                    placeholder="100"
                  >
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label for="uploadLimitDay">每天上传限制</label>
                  <input
                    id="uploadLimitDay"
                    v-model.number="groupForm.upload_limit_day"
                    type="number"
                    class="form-control"
                    min="0"
                    max="50000"
                    placeholder="500"
                  >
                </div>
                <div class="form-group">
                  <label for="uploadLimitWeek">每周上传限制</label>
                  <input
                    id="uploadLimitWeek"
                    v-model.number="groupForm.upload_limit_week"
                    type="number"
                    class="form-control"
                    min="0"
                    max="200000"
                    placeholder="2000"
                  >
                </div>
              </div>
              <div class="form-group">
                <label for="uploadLimitMonth">每月上传限制</label>
                <input
                  id="uploadLimitMonth"
                  v-model.number="groupForm.upload_limit_month"
                  type="number"
                  class="form-control"
                  min="0"
                  max="500000"
                  placeholder="5000"
                >
              </div>
            </div>

            <!-- 文件命名和路径配置 -->
            <div class="form-section">
              <h4>文件命名和路径配置</h4>
              <div class="form-group">
                <label for="pathNamingRule">路径命名规则</label>
                <input
                  id="pathNamingRule"
                  v-model="groupForm.path_naming_rule"
                  type="text"
                  class="form-control"
                  placeholder="{Y}/{m}/{d}"
                >
                <div class="form-text">支持变量：{Y}年份，{m}月份，{d}日期</div>
              </div>
              <div class="form-group">
                <label for="fileNamingRule">文件命名规则</label>
                <input
                  id="fileNamingRule"
                  v-model="groupForm.file_naming_rule"
                  type="text"
                  class="form-control"
                  placeholder="{uniqid}"
                >
                <div class="form-text">支持变量：{uniqid}唯一ID，{timestamp}时间戳，{original}原文件名</div>
              </div>
            </div>

            <!-- 图片处理配置 -->
            <div class="form-section">
              <h4>图片处理配置</h4>
              <div class="form-row">
                <div class="form-group">
                  <label for="imageQuality">图片保存质量 (1-100)</label>
                  <input
                    id="imageQuality"
                    v-model.number="groupForm.image_quality"
                    type="number"
                    class="form-control"
                    min="1"
                    max="100"
                    placeholder="85"
                  >
                </div>
                <div class="form-group">
                  <label for="imageFormat">图片转换格式</label>
                  <select
                    id="imageFormat"
                    v-model="groupForm.image_format"
                    class="form-control"
                  >
                    <option value="original">保持原格式</option>
                    <option value="jpg">转换为 JPG</option>
                    <option value="jpeg">转换为 JPEG</option>
                    <option value="png">转换为 PNG</option>
                    <option value="webp">转换为 WebP</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label for="allowedImageTypes">允许上传的图片类型</label>
                <input
                  id="allowedImageTypes"
                  v-model="groupForm.allowed_image_types"
                  type="text"
                  class="form-control"
                  placeholder="jpg,jpeg,png,gif,webp"
                >
                <div class="form-text">多个类型用逗号分隔</div>
              </div>
            </div>

            <div class="form-group" v-if="!currentGroup?.is_system">
              <label class="checkbox-label">
                <input
                  v-model="groupForm.is_active"
                  type="checkbox"
                >
                <span class="checkmark"></span>
                激活角色组
              </label>
            </div>
            <div class="form-group" v-if="!currentGroup?.is_system">
              <label class="checkbox-label">
                <input
                  v-model="groupForm.is_default"
                  type="checkbox"
                >
                <span class="checkmark"></span>
                设为默认角色组（新用户自动加入）
              </label>
            </div>
            <div v-if="currentGroup?.is_system" class="system-group-notice">
              <i class="bi bi-info-circle"></i>
              <span>系统角色组的基本属性不可修改，但可以调整配置参数</span>
            </div>
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" @click="closeModals">取消</button>
              <button type="submit" class="btn btn-primary" :disabled="submitting">
                <i v-if="submitting" class="bi bi-arrow-clockwise spinning"></i>
                {{ submitting ? '提交中...' : (showCreateModal ? '创建' : '更新') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 旧的通知系统已移除，使用新的NotificationToast组件 -->
  </div>

  <!-- 弹窗和通知现在使用全局组件 -->
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import api from '../../services/api';
import { useDialog } from '../../utils/dialog';

// 响应式数据
const loading = ref(false);
const error = ref('');
const groups = ref<any[]>([]);
const showCreateModal = ref(false);
const showEditModal = ref(false);
const submitting = ref(false);
const currentGroup = ref<any>(null);

// 使用全局弹窗
const { confirmDeleteAsync, confirmAsync, notify } = useDialog();

const groupForm = reactive({
  name: '',
  display_name: '',
  description: '',
  is_active: true,
  is_default: false,

  // 上传限制配置
  max_file_size: 10240,
  concurrent_uploads: 3,
  upload_limit_minute: 10,
  upload_limit_hour: 100,
  upload_limit_day: 500,
  upload_limit_week: 2000,
  upload_limit_month: 5000,

  // 文件命名和路径配置
  path_naming_rule: '{Y}/{m}/{d}',
  file_naming_rule: '{uniqid}',

  // 图片处理配置
  image_quality: 85,
  image_format: 'original',
  allowed_image_types: 'jpg,jpeg,png,gif,webp'
});

// 移除旧的通知系统，使用新的NotificationToast组件

// 计算属性
const activeGroups = computed(() => {
  return groups.value.filter(group => group.is_active).length;
});

const totalMembers = computed(() => {
  return groups.value.reduce((total, group) => total + (group.member_count || 0), 0);
});

// 获取用户组列表
const fetchGroups = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    const response = await api.getAllGroups();
    groups.value = response.groups || [];
  } catch (err: any) {
    error.value = err.response?.data?.error || err.message || '获取角色组列表失败';
    showNotification('加载角色组列表失败', 'error');
  } finally {
    loading.value = false;
  }
};

// 工具函数
const getStatusClass = (group: any) => {
  return group.is_active ? 'status-active' : 'status-inactive';
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

// 操作函数
const viewGroup = (group: any) => {
  // TODO: 实现查看详情功能
  console.log('查看用户组:', group);
};

const manageMembers = (group: any) => {
  // TODO: 实现成员管理功能
  console.log('管理成员:', group);
};

const managePermissions = (group: any) => {
  // TODO: 实现权限管理功能
  console.log('管理权限:', group);
};

const editGroup = (group: any) => {
  currentGroup.value = group;
  groupForm.name = group.name;
  groupForm.display_name = group.display_name;
  groupForm.description = group.description || '';
  groupForm.is_active = group.is_active;
  groupForm.is_default = group.is_default;

  // 填充配置字段
  groupForm.max_file_size = group.max_file_size || 10240;
  groupForm.concurrent_uploads = group.concurrent_uploads || 3;
  groupForm.upload_limit_minute = group.upload_limit_minute || 10;
  groupForm.upload_limit_hour = group.upload_limit_hour || 100;
  groupForm.upload_limit_day = group.upload_limit_day || 500;
  groupForm.upload_limit_week = group.upload_limit_week || 2000;
  groupForm.upload_limit_month = group.upload_limit_month || 5000;
  groupForm.path_naming_rule = group.path_naming_rule || '{Y}/{m}/{d}';
  groupForm.file_naming_rule = group.file_naming_rule || '{uniqid}';
  groupForm.image_quality = group.image_quality || 85;
  groupForm.image_format = group.image_format || 'original';
  groupForm.allowed_image_types = group.allowed_image_types || 'jpg,jpeg,png,gif,webp';

  showEditModal.value = true;
};

const deleteGroup = async (group: any) => {
  if (group.is_default) {
    showNotification('不能删除默认角色组', 'error');
    return;
  }

  confirmDeleteAsync(group.display_name, async () => {
    await api.deleteGroup(group.id);
    showNotification('角色组删除成功');
    fetchGroups();
  }, {
    details: '此操作不可恢复，该组的所有成员将失去组权限。'
  });
};

const setDefaultGroup = async (group: any) => {
  confirmAsync(`确定要将"${group.display_name}"设为默认角色组吗？`, async () => {
    await api.setDefaultGroup(group.id);
    showNotification('默认角色组设置成功');
    fetchGroups();
  }, {
    title: '设置默认角色组',
    details: '新用户将自动加入此组，原默认组将失去默认状态。',
    type: 'warning'
  });
};

const submitGroup = async () => {
  submitting.value = true;

  try {
    if (showCreateModal.value) {
      await api.createGroup(groupForm);
      showNotification('角色组创建成功');
    } else {
      // 对于系统组，只发送配置相关的字段
      let updateData: any = { ...groupForm };
      if (currentGroup.value?.is_system) {
        // 系统组只能更新配置字段和描述
        updateData = {
          description: groupForm.description,
          max_file_size: groupForm.max_file_size,
          concurrent_uploads: groupForm.concurrent_uploads,
          upload_limit_minute: groupForm.upload_limit_minute,
          upload_limit_hour: groupForm.upload_limit_hour,
          upload_limit_day: groupForm.upload_limit_day,
          upload_limit_week: groupForm.upload_limit_week,
          upload_limit_month: groupForm.upload_limit_month,
          path_naming_rule: groupForm.path_naming_rule,
          file_naming_rule: groupForm.file_naming_rule,
          image_quality: groupForm.image_quality,
          image_format: groupForm.image_format,
          allowed_image_types: groupForm.allowed_image_types
        };
      }

      await api.updateGroup(currentGroup.value.id, updateData);
      showNotification(currentGroup.value?.is_system ? '系统角色组配置更新成功' : '角色组更新成功');
    }

    closeModals();
    fetchGroups();
  } catch (err: any) {
    showNotification(err.response?.data?.error || '操作失败', 'error');
  } finally {
    submitting.value = false;
  }
};

const closeModals = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  currentGroup.value = null;
  
  // 重置表单
  groupForm.name = '';
  groupForm.display_name = '';
  groupForm.description = '';
  groupForm.is_active = true;
  groupForm.is_default = false;
};

const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  if (type === 'success') {
    notify.success(message);
  } else {
    notify.error(message);
  }
};

// 旧的弹窗控制方法已移除，使用全局弹窗系统

// 生命周期
onMounted(() => {
  fetchGroups();
});
</script>

<style scoped>
.groups-view {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.header-left h2 {
  color: #333;
  margin-bottom: 8px;
}

.page-subtitle {
  color: #666;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #e3f2fd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #1976d2;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.groups-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.groups-table {
  width: 100%;
  border-collapse: collapse;
}

.groups-table th {
  background: #f8f9fa;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #dee2e6;
}

.groups-table td {
  padding: 15px 12px;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: middle;
}

.groups-table tr:hover {
  background: #f8f9fa;
}

.group-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.group-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e8f5e8;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #2e7d32;
}

.group-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.group-description {
  font-size: 12px;
  color: #888;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.member-count {
  display: inline-block;
  padding: 4px 8px;
  background: #f8f9fa;
  color: #495057;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #dee2e6;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.status-active {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.status-inactive {
  background: #ffebee;
  color: #d32f2f;
}

.status-badge.status-default {
  background: #fff3e0;
  color: #f57c00;
}

.status-badge.status-system {
  background: #e8eaf6;
  color: #3f51b5;
}

.status-badges {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.creator-name {
  font-size: 13px;
  color: #333;
}

.create-time {
  font-size: 12px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.text-center {
  text-align: center;
}

.loading-spinner, .error-state, .no-data {
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.loading-spinner i, .error-state i, .no-data i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-btn:hover {
  background: #f8f9fa;
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-outline-primary {
  border: 1px solid #007bff;
  color: #007bff;
  background: transparent;
}

.btn-outline-primary:hover {
  background: #007bff;
  color: white;
}

.btn-outline-info {
  border: 1px solid #17a2b8;
  color: #17a2b8;
  background: transparent;
}

.btn-outline-info:hover {
  background: #17a2b8;
  color: white;
}

.btn-outline-warning {
  border: 1px solid #ffc107;
  color: #ffc107;
  background: transparent;
}

.btn-outline-warning:hover {
  background: #ffc107;
  color: #212529;
}

.btn-outline-secondary {
  border: 1px solid #6c757d;
  color: #6c757d;
  background: transparent;
}

.btn-outline-secondary:hover {
  background: #6c757d;
  color: white;
}

.btn-outline-danger {
  border: 1px solid #dc3545;
  color: #dc3545;
  background: transparent;
}

.btn-outline-danger:hover {
  background: #dc3545;
  color: white;
}

.btn-outline-success {
  border: 1px solid #28a745;
  color: #28a745;
  background: transparent;
}

.btn-outline-success:hover {
  background: #28a745;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.text-muted {
  color: #6c757d;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-text {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
}

.modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

.system-group-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 6px;
  color: #1976d2;
  font-size: 14px;
  margin-bottom: 16px;
}

.system-group-notice i {
  font-size: 16px;
}

/* 旧的通知样式已移除，使用新的NotificationToast组件 */
</style>
