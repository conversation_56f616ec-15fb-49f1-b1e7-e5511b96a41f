package dao

import (
	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
)

// CreateRole 创建角色
func CreateRole(role *models.Role) error {
	return database.DB.Create(role).Error
}

// GetRoleByID 根据ID获取角色
func GetRoleByID(id uint) (*models.Role, error) {
	var role models.Role
	err := database.DB.Preload("Permissions").First(&role, id).Error
	return &role, err
}

// GetRoleByName 根据名称获取角色
func GetRoleByName(name string) (*models.Role, error) {
	var role models.Role
	err := database.DB.Preload("Permissions").Where("name = ?", name).First(&role).Error
	return &role, err
}

// GetAllRoles 获取所有角色
func GetAllRoles() ([]models.Role, error) {
	var roles []models.Role
	err := database.DB.Preload("Permissions").Find(&roles).Error
	return roles, err
}

// GetRolesWithUserCount 获取角色列表及用户数量
func GetRolesWithUserCount() ([]models.RoleResponse, error) {
	var roles []models.Role
	err := database.DB.Preload("Permissions").Find(&roles).Error
	if err != nil {
		return nil, err
	}

	var roleResponses []models.RoleResponse
	for _, role := range roles {
		var userCount int64
		database.DB.Model(&models.User{}).Where("role_id = ?", role.ID).Count(&userCount)

		var permissions []models.PermissionResponse
		for _, perm := range role.Permissions {
			permissions = append(permissions, models.PermissionResponse{
				ID:          perm.ID,
				Name:        perm.Name,
				DisplayName: perm.DisplayName,
				Description: perm.Description,
				Resource:    perm.Resource,
				Action:      perm.Action,
			})
		}

		roleResponses = append(roleResponses, models.RoleResponse{
			ID:          role.ID,
			Name:        role.Name,
			DisplayName: role.DisplayName,
			Description: role.Description,
			IsSystem:    role.IsSystem,
			UserCount:   userCount,
			Permissions: permissions,
			CreatedAt:   role.CreatedAt,
			UpdatedAt:   role.UpdatedAt,
		})
	}

	return roleResponses, nil
}

// UpdateRole 更新角色
func UpdateRole(role *models.Role) error {
	return database.DB.Save(role).Error
}

// DeleteRole 删除角色
func DeleteRole(id uint) error {
	// 检查是否为系统角色
	var role models.Role
	if err := database.DB.First(&role, id).Error; err != nil {
		return err
	}

	if role.IsSystem {
		return database.ErrSystemRoleCannotBeDeleted
	}

	// 检查是否有用户使用此角色
	var userCount int64
	database.DB.Model(&models.User{}).Where("role_id = ?", id).Count(&userCount)
	if userCount > 0 {
		return database.ErrRoleInUse
	}

	return database.DB.Delete(&models.Role{}, id).Error
}

// AssignPermissionsToRole 为角色分配权限
func AssignPermissionsToRole(roleID uint, permissionIDs []uint) error {
	// 先清除现有权限
	if err := database.DB.Where("role_id = ?", roleID).Delete(&models.RolePermission{}).Error; err != nil {
		return err
	}

	// 添加新权限
	for _, permID := range permissionIDs {
		rolePermission := models.RolePermission{
			RoleID:       roleID,
			PermissionID: permID,
		}
		if err := database.DB.Create(&rolePermission).Error; err != nil {
			return err
		}
	}

	return nil
}

// GetAllPermissions 获取所有权限
func GetAllPermissions() ([]models.Permission, error) {
	var permissions []models.Permission
	err := database.DB.Find(&permissions).Error
	return permissions, err
}

// GetPermissionsByResource 根据资源类型获取权限
func GetPermissionsByResource(resource string) ([]models.Permission, error) {
	var permissions []models.Permission
	err := database.DB.Where("resource = ?", resource).Find(&permissions).Error
	return permissions, err
}

// GetPermissionByName 根据名称获取权限
func GetPermissionByName(name string) (*models.Permission, error) {
	var permission models.Permission
	err := database.DB.Where("name = ?", name).First(&permission).Error
	return &permission, err
}

// CreatePermission 创建权限
func CreatePermission(permission *models.Permission) error {
	return database.DB.Create(permission).Error
}

// UpdatePermission 更新权限
func UpdatePermission(permission *models.Permission) error {
	return database.DB.Save(permission).Error
}

// DeletePermission 删除权限
func DeletePermission(id uint) error {
	return database.DB.Delete(&models.Permission{}, id).Error
}

// AssignUserRole 为用户分配角色
func AssignUserRole(userID, roleID uint) error {
	return database.DB.Model(&models.User{}).Where("id = ?", userID).Update("role_id", roleID).Error
}

// GrantUserPermission 授予用户特殊权限
func GrantUserPermission(userID, permissionID uint, granted bool) error {
	userPermission := models.UserPermission{
		UserID:       userID,
		PermissionID: permissionID,
		Granted:      granted,
	}

	// 检查是否已存在
	var existing models.UserPermission
	err := database.DB.Where("user_id = ? AND permission_id = ?", userID, permissionID).First(&existing).Error
	if err == nil {
		// 更新现有记录
		existing.Granted = granted
		return database.DB.Save(&existing).Error
	}

	// 创建新记录
	return database.DB.Create(&userPermission).Error
}

// RevokeUserPermission 撤销用户特殊权限
func RevokeUserPermission(userID, permissionID uint) error {
	return database.DB.Where("user_id = ? AND permission_id = ?", userID, permissionID).Delete(&models.UserPermission{}).Error
}

// GetUserPermissions 获取用户的特殊权限
func GetUserPermissions(userID uint) ([]models.UserPermission, error) {
	var userPermissions []models.UserPermission
	err := database.DB.Preload("Permission").Where("user_id = ?", userID).Find(&userPermissions).Error
	return userPermissions, err
}

// CheckUserPermission 检查用户是否拥有指定权限
func CheckUserPermission(userID uint, permissionName string) (bool, error) {
	user, err := GetUserWithRoleAndPermissions(userID)
	if err != nil {
		return false, err
	}

	return user.HasPermission(permissionName), nil
}

// GetUserWithRoleAndPermissions 获取用户及其角色和权限信息
func GetUserWithRoleAndPermissions(userID uint) (*models.User, error) {
	var user models.User
	err := database.DB.Preload("Role.Permissions").Preload("UserPermissions.Permission").First(&user, userID).Error
	return &user, err
}

// GetPermissionsByRoleID 根据角色ID获取权限列表
func GetPermissionsByRoleID(roleID uint) ([]models.Permission, error) {
	var permissions []models.Permission
	err := database.DB.Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", roleID).
		Find(&permissions).Error
	return permissions, err
}
