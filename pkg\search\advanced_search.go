package search

import (
	"fmt"
	"image"
	"image/color"
	"math"
	"os"
	"sort"
	"strings"
)

// ImageFeatures 图片特征
type ImageFeatures struct {
	ImageID        uint      `json:"image_id"`
	ImagePath      string    `json:"image_path"`
	ColorHist      []float64 `json:"color_hist"`      // 颜色直方图
	EdgeHist       []float64 `json:"edge_hist"`       // 边缘直方图
	TextureHist    []float64 `json:"texture_hist"`    // 纹理直方图
	DominantColors []string  `json:"dominant_colors"` // 主要颜色
	Brightness     float64   `json:"brightness"`      // 亮度
	Contrast       float64   `json:"contrast"`        // 对比度
	Hash           string    `json:"hash"`            // 感知哈希
}

// SimilarityResult 相似度搜索结果
type SimilarityResult struct {
	ImageID    uint    `json:"image_id"`
	ImagePath  string  `json:"image_path"`
	Similarity float64 `json:"similarity"` // 相似度 0-1
	MatchType  string  `json:"match_type"` // color, edge, texture, hash
}

// DuplicateGroup 重复图片组
type DuplicateGroup struct {
	GroupID    string  `json:"group_id"`
	Images     []uint  `json:"images"`
	Similarity float64 `json:"similarity"`
	Type       string  `json:"type"` // exact, near_exact, similar
}

// AdvancedSearchEngine 高级搜索引擎
type AdvancedSearchEngine struct {
	features map[uint]*ImageFeatures // 图片特征缓存
}

// NewAdvancedSearchEngine 创建高级搜索引擎
func NewAdvancedSearchEngine() *AdvancedSearchEngine {
	return &AdvancedSearchEngine{
		features: make(map[uint]*ImageFeatures),
	}
}

// ExtractFeatures 提取图片特征
func (ase *AdvancedSearchEngine) ExtractFeatures(imageID uint, imagePath string) (*ImageFeatures, error) {
	// 检查缓存
	if features, exists := ase.features[imageID]; exists {
		return features, nil
	}

	// 打开图片
	file, err := os.Open(imagePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open image: %v", err)
	}
	defer file.Close()

	// 解码图片
	img, _, err := image.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %v", err)
	}

	features := &ImageFeatures{
		ImageID:   imageID,
		ImagePath: imagePath,
	}

	// 提取颜色直方图
	features.ColorHist = ase.extractColorHistogram(img)

	// 提取边缘直方图
	features.EdgeHist = ase.extractEdgeHistogram(img)

	// 提取纹理直方图
	features.TextureHist = ase.extractTextureHistogram(img)

	// 提取主要颜色
	features.DominantColors = ase.extractDominantColors(img)

	// 计算亮度和对比度
	features.Brightness, features.Contrast = ase.calculateBrightnessContrast(img)

	// 生成感知哈希
	features.Hash = ase.generatePerceptualHash(img)

	// 缓存特征
	ase.features[imageID] = features

	return features, nil
}

// extractColorHistogram 提取颜色直方图
func (ase *AdvancedSearchEngine) extractColorHistogram(img image.Image) []float64 {
	bounds := img.Bounds()
	hist := make([]float64, 64) // 4x4x4 RGB直方图

	totalPixels := 0
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			r, g, b, _ := img.At(x, y).RGBA()

			// 将16位颜色值转换为4级
			rBin := (r >> 14) // 0-3
			gBin := (g >> 14) // 0-3
			bBin := (b >> 14) // 0-3

			index := rBin*16 + gBin*4 + bBin
			hist[index]++
			totalPixels++
		}
	}

	// 归一化
	for i := range hist {
		hist[i] /= float64(totalPixels)
	}

	return hist
}

// extractEdgeHistogram 提取边缘直方图
func (ase *AdvancedSearchEngine) extractEdgeHistogram(img image.Image) []float64 {
	bounds := img.Bounds()
	hist := make([]float64, 8) // 8个方向的边缘

	// Sobel算子
	sobelX := [][]int{{-1, 0, 1}, {-2, 0, 2}, {-1, 0, 1}}
	sobelY := [][]int{{-1, -2, -1}, {0, 0, 0}, {1, 2, 1}}

	totalEdges := 0
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			gx, gy := 0.0, 0.0

			// 应用Sobel算子
			for dy := -1; dy <= 1; dy++ {
				for dx := -1; dx <= 1; dx++ {
					gray := ase.rgbaToGray(img.At(x+dx, y+dy))
					gx += float64(sobelX[dy+1][dx+1]) * gray
					gy += float64(sobelY[dy+1][dx+1]) * gray
				}
			}

			// 计算梯度幅度和方向
			magnitude := math.Sqrt(gx*gx + gy*gy)
			if magnitude > 30 { // 阈值过滤
				angle := math.Atan2(gy, gx)
				// 将角度映射到8个方向
				direction := int((angle + math.Pi) / (math.Pi / 4))
				if direction >= 8 {
					direction = 7
				}
				hist[direction] += magnitude
				totalEdges++
			}
		}
	}

	// 归一化
	if totalEdges > 0 {
		for i := range hist {
			hist[i] /= float64(totalEdges)
		}
	}

	return hist
}

// extractTextureHistogram 提取纹理直方图
func (ase *AdvancedSearchEngine) extractTextureHistogram(img image.Image) []float64 {
	bounds := img.Bounds()
	hist := make([]float64, 16) // 16个纹理特征

	// 简化的LBP (Local Binary Pattern)
	totalPatterns := 0
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			center := ase.rgbaToGray(img.At(x, y))
			pattern := 0

			// 8邻域
			neighbors := []struct{ dx, dy int }{
				{-1, -1}, {0, -1}, {1, -1},
				{1, 0}, {1, 1}, {0, 1},
				{-1, 1}, {-1, 0},
			}

			for i, neighbor := range neighbors {
				neighborGray := ase.rgbaToGray(img.At(x+neighbor.dx, y+neighbor.dy))
				if neighborGray >= center {
					pattern |= (1 << i)
				}
			}

			// 简化为16个模式
			hist[pattern%16]++
			totalPatterns++
		}
	}

	// 归一化
	if totalPatterns > 0 {
		for i := range hist {
			hist[i] /= float64(totalPatterns)
		}
	}

	return hist
}

// extractDominantColors 提取主要颜色
func (ase *AdvancedSearchEngine) extractDominantColors(img image.Image) []string {
	bounds := img.Bounds()
	colorCount := make(map[string]int)

	// 采样像素
	step := 10 // 每10个像素采样一次
	for y := bounds.Min.Y; y < bounds.Max.Y; y += step {
		for x := bounds.Min.X; x < bounds.Max.X; x += step {
			r, g, b, _ := img.At(x, y).RGBA()

			// 量化颜色
			r8 := uint8(r >> 8)
			g8 := uint8(g >> 8)
			b8 := uint8(b >> 8)

			// 简化颜色空间
			r8 = (r8 / 32) * 32
			g8 = (g8 / 32) * 32
			b8 = (b8 / 32) * 32

			colorKey := fmt.Sprintf("#%02x%02x%02x", r8, g8, b8)
			colorCount[colorKey]++
		}
	}

	// 排序并取前5个主要颜色
	type colorFreq struct {
		color string
		count int
	}

	var colors []colorFreq
	for color, count := range colorCount {
		colors = append(colors, colorFreq{color, count})
	}

	sort.Slice(colors, func(i, j int) bool {
		return colors[i].count > colors[j].count
	})

	var dominantColors []string
	maxColors := 5
	if len(colors) < maxColors {
		maxColors = len(colors)
	}

	for i := 0; i < maxColors; i++ {
		dominantColors = append(dominantColors, colors[i].color)
	}

	return dominantColors
}

// calculateBrightnessContrast 计算亮度和对比度
func (ase *AdvancedSearchEngine) calculateBrightnessContrast(img image.Image) (float64, float64) {
	bounds := img.Bounds()
	var sum, sumSquares float64
	totalPixels := 0

	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			gray := ase.rgbaToGray(img.At(x, y))
			sum += gray
			sumSquares += gray * gray
			totalPixels++
		}
	}

	brightness := sum / float64(totalPixels)
	variance := (sumSquares / float64(totalPixels)) - (brightness * brightness)
	contrast := math.Sqrt(variance)

	return brightness / 255.0, contrast / 255.0
}

// generatePerceptualHash 生成感知哈希
func (ase *AdvancedSearchEngine) generatePerceptualHash(img image.Image) string {
	// 缩放到8x8
	resized := ase.resizeImage(img, 8, 8)

	// 转换为灰度并计算平均值
	var pixels []float64
	var sum float64

	for y := 0; y < 8; y++ {
		for x := 0; x < 8; x++ {
			gray := ase.rgbaToGray(resized.At(x, y))
			pixels = append(pixels, gray)
			sum += gray
		}
	}

	average := sum / 64.0

	// 生成哈希
	var hash strings.Builder
	for _, pixel := range pixels {
		if pixel >= average {
			hash.WriteString("1")
		} else {
			hash.WriteString("0")
		}
	}

	return hash.String()
}

// FindSimilarImages 查找相似图片
func (ase *AdvancedSearchEngine) FindSimilarImages(targetImageID uint, threshold float64, maxResults int) ([]SimilarityResult, error) {
	targetFeatures, exists := ase.features[targetImageID]
	if !exists {
		return nil, fmt.Errorf("target image features not found")
	}

	var results []SimilarityResult

	for imageID, features := range ase.features {
		if imageID == targetImageID {
			continue
		}

		// 计算多种相似度
		colorSim := ase.calculateHistogramSimilarity(targetFeatures.ColorHist, features.ColorHist)
		edgeSim := ase.calculateHistogramSimilarity(targetFeatures.EdgeHist, features.EdgeHist)
		textureSim := ase.calculateHistogramSimilarity(targetFeatures.TextureHist, features.TextureHist)
		hashSim := ase.calculateHashSimilarity(targetFeatures.Hash, features.Hash)

		// 综合相似度
		overallSim := (colorSim*0.4 + edgeSim*0.2 + textureSim*0.2 + hashSim*0.2)

		if overallSim >= threshold {
			matchType := "color"
			if hashSim > 0.9 {
				matchType = "hash"
			} else if edgeSim > colorSim && edgeSim > textureSim {
				matchType = "edge"
			} else if textureSim > colorSim {
				matchType = "texture"
			}

			results = append(results, SimilarityResult{
				ImageID:    imageID,
				ImagePath:  features.ImagePath,
				Similarity: overallSim,
				MatchType:  matchType,
			})
		}
	}

	// 按相似度排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Similarity > results[j].Similarity
	})

	// 限制结果数量
	if len(results) > maxResults {
		results = results[:maxResults]
	}

	return results, nil
}

// DetectDuplicates 检测重复图片
func (ase *AdvancedSearchEngine) DetectDuplicates(threshold float64) ([]DuplicateGroup, error) {
	var groups []DuplicateGroup
	processed := make(map[uint]bool)

	for imageID1, features1 := range ase.features {
		if processed[imageID1] {
			continue
		}

		var duplicates []uint
		duplicates = append(duplicates, imageID1)

		for imageID2, features2 := range ase.features {
			if imageID1 == imageID2 || processed[imageID2] {
				continue
			}

			// 计算哈希相似度（最快的重复检测方法）
			hashSim := ase.calculateHashSimilarity(features1.Hash, features2.Hash)

			if hashSim >= threshold {
				duplicates = append(duplicates, imageID2)
				processed[imageID2] = true
			}
		}

		if len(duplicates) > 1 {
			groupType := "similar"
			if threshold > 0.95 {
				groupType = "near_exact"
			}
			if threshold > 0.99 {
				groupType = "exact"
			}

			groups = append(groups, DuplicateGroup{
				GroupID:    fmt.Sprintf("group_%d", len(groups)+1),
				Images:     duplicates,
				Similarity: threshold,
				Type:       groupType,
			})
		}

		processed[imageID1] = true
	}

	return groups, nil
}

// 辅助函数
func (ase *AdvancedSearchEngine) rgbaToGray(c color.Color) float64 {
	r, g, b, _ := c.RGBA()
	// 使用标准的灰度转换公式
	gray := 0.299*float64(r) + 0.587*float64(g) + 0.114*float64(b)
	return gray / 65535.0 * 255.0
}

func (ase *AdvancedSearchEngine) resizeImage(img image.Image, width, height int) image.Image {
	bounds := img.Bounds()
	resized := image.NewRGBA(image.Rect(0, 0, width, height))

	scaleX := float64(bounds.Dx()) / float64(width)
	scaleY := float64(bounds.Dy()) / float64(height)

	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			srcX := int(float64(x) * scaleX)
			srcY := int(float64(y) * scaleY)
			resized.Set(x, y, img.At(bounds.Min.X+srcX, bounds.Min.Y+srcY))
		}
	}

	return resized
}

func (ase *AdvancedSearchEngine) calculateHistogramSimilarity(hist1, hist2 []float64) float64 {
	if len(hist1) != len(hist2) {
		return 0.0
	}

	// 使用余弦相似度
	var dotProduct, norm1, norm2 float64
	for i := 0; i < len(hist1); i++ {
		dotProduct += hist1[i] * hist2[i]
		norm1 += hist1[i] * hist1[i]
		norm2 += hist2[i] * hist2[i]
	}

	if norm1 == 0 || norm2 == 0 {
		return 0.0
	}

	return dotProduct / (math.Sqrt(norm1) * math.Sqrt(norm2))
}

func (ase *AdvancedSearchEngine) calculateHashSimilarity(hash1, hash2 string) float64 {
	if len(hash1) != len(hash2) {
		return 0.0
	}

	matches := 0
	for i := 0; i < len(hash1); i++ {
		if hash1[i] == hash2[i] {
			matches++
		}
	}

	return float64(matches) / float64(len(hash1))
}
