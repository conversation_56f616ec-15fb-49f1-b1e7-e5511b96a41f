-- 为图片表添加元数据字段
-- 添加图片尺寸、格式和缩略图信息字段

-- 添加图片宽度字段
ALTER TABLE `images` ADD COLUMN `width` INT DEFAULT 0 COMMENT '图片宽度';

-- 添加图片高度字段
ALTER TABLE `images` ADD COLUMN `height` INT DEFAULT 0 COMMENT '图片高度';

-- 添加图片格式字段
ALTER TABLE `images` ADD COLUMN `format` VARCHAR(10) DEFAULT '' COMMENT '图片格式';

-- 添加缩略图信息字段
ALTER TABLE `images` ADD COLUMN `thumbnails` TEXT COMMENT '缩略图路径信息(JSON格式)';

-- 添加索引以提升查询性能
CREATE INDEX `idx_images_format` ON `images` (`format`);
CREATE INDEX `idx_images_dimensions` ON `images` (`width`, `height`);

-- 更新现有记录的默认值（如果需要）
UPDATE `images` SET `width` = 0, `height` = 0, `format` = '', `thumbnails` = '' WHERE `width` IS NULL;

-- 分析表以更新统计信息
ANALYZE TABLE `images`;
