<template>
  <div class="image-grid">
    <!-- 虚拟滚动网格 -->
    <VirtualGrid
      :items="images"
      :item-height="itemHeight"
      :items-per-row="itemsPerRow"
      :gap="16"
      :loading="loading"
      :has-more="hasMore"
      @load-more="$emit('loadMore')"
      @scroll="onScroll"
      ref="virtualGridRef"
    >
      <template #item="{ item, index }">
        <div
          class="image-card"
          :class="{
            selectable: selectable,
            selected: selectable && selectedImages.includes(item.id)
          }"
          @click="handleImageClick(item, index)"
        >
          <!-- 懒加载图片 -->
          <div class="image-thumbnail">
            <LazyImage
              :src="item.url"
              :alt="item.name"
              :placeholder="placeholderImage"
              :thumbnails="item.thumbnails"
              :preferred-size="'medium'"
              :show-loading-text="false"
              :show-progress="true"
              image-class="thumbnail-image"
              @load="onImageLoad(item, index)"
              @error="onImageError(item, index)"
            />
            
            <!-- 图片遮罩层 -->
            <div class="image-overlay">
              <div class="overlay-actions">
                <button
                  class="action-btn view-btn"
                  @click.stop="$emit('imageView', item)"
                  title="查看大图"
                >
                  <i class="bi bi-eye"></i>
                </button>
                <button
                  class="action-btn download-btn"
                  @click.stop="$emit('imageDownload', item)"
                  title="下载图片"
                >
                  <i class="bi bi-download"></i>
                </button>
                <button
                  class="action-btn delete-btn"
                  @click.stop="$emit('imageDelete', item)"
                  title="删除图片"
                >
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </div>
            
            <!-- 选择框（如果启用多选） -->
            <div v-if="selectable" class="selection-checkbox">
              <input
                type="checkbox"
                :checked="selectedImages.includes(item.id)"
                @change="toggleSelection(item)"
                @click.stop
              />
            </div>

            <!-- 批量选择覆盖层 -->
            <div
              v-if="selectable"
              class="selection-overlay"
              :class="{ selected: selectedImages.includes(item.id) }"
              @click.stop="toggleSelection(item)"
            >
              <div class="selection-indicator">
                <i class="bi bi-check-circle-fill"></i>
              </div>
            </div>
          </div>
          
          <!-- 图片信息 -->
          <div class="image-info">
            <h4 class="image-name" :title="item.name">
              <span v-if="highlightMatches && searchQuery" v-html="highlightText(item.name)"></span>
              <span v-else>{{ item.name }}</span>
            </h4>
            <div class="image-meta">
              <span class="file-size">{{ formatFileSize(item.size) }}</span>
              <span class="upload-date">{{ formatDate(item.created_at) }}</span>
            </div>
            <div v-if="item.album" class="image-album">
              <i class="bi bi-folder"></i>
              <span v-if="highlightMatches && searchQuery" v-html="highlightText(item.album.name)"></span>
              <span v-else>{{ item.album.name }}</span>
            </div>
          </div>
        </div>
      </template>
    </VirtualGrid>
    
    <!-- 空状态 -->
    <div v-if="!loading && images.length === 0" class="empty-state">
      <div class="empty-content">
        <i class="bi bi-images"></i>
        <h3>暂无图片</h3>
        <p>{{ emptyMessage || '您还没有上传任何图片' }}</p>
        <button v-if="showUploadButton" class="btn btn-primary" @click="$emit('upload')">
          <i class="bi bi-cloud-upload"></i>
          上传图片
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import VirtualGrid from './VirtualGrid.vue'
import LazyImage from './LazyImage.vue'

interface Image {
  id: number
  name: string
  url: string
  size: number
  width?: number
  height?: number
  format?: string
  thumbnails?: {
    small?: string
    medium?: string
    large?: string
    xlarge?: string
  }
  created_at: string
  album?: {
    id: number
    name: string
  }
}

interface Props {
  images: Image[]
  loading?: boolean
  hasMore?: boolean
  selectable?: boolean
  selectedImages?: number[]
  itemHeight?: number
  itemsPerRow?: number
  placeholderImage?: string
  emptyMessage?: string
  showUploadButton?: boolean
  searchQuery?: string
  highlightMatches?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  hasMore: false,
  selectable: false,
  selectedImages: () => [],
  itemHeight: 280,
  itemsPerRow: 4,
  placeholderImage: '',
  emptyMessage: '',
  showUploadButton: true,
  searchQuery: '',
  highlightMatches: false
})

const emit = defineEmits<{
  imageClick: [image: Image, index: number]
  imageView: [image: Image]
  imageDownload: [image: Image]
  imageDelete: [image: Image]
  loadMore: []
  upload: []
  selectionChange: [selectedIds: number[]]
  scroll: [scrollTop: number]
}>()

const virtualGridRef = ref()

// 选择状态管理
const selectedImages = ref<number[]>([...props.selectedImages])

// 监听外部选择状态变化
watch(() => props.selectedImages, (newSelection) => {
  selectedImages.value = [...newSelection]
})

// 切换选择状态
const toggleSelection = (image: Image) => {
  const index = selectedImages.value.indexOf(image.id)
  if (index > -1) {
    selectedImages.value.splice(index, 1)
  } else {
    selectedImages.value.push(image.id)
  }
  emit('selectionChange', [...selectedImages.value])
}

// 处理图片点击
const handleImageClick = (image: Image, index: number) => {
  if (props.selectable) {
    toggleSelection(image)
  } else {
    emit('imageClick', image, index)
  }
}

// 图片加载成功
const onImageLoad = (image: Image, index: number) => {
  console.log(`Image loaded: ${image.name}`)
}

// 图片加载失败
const onImageError = (image: Image, index: number) => {
  console.error(`Failed to load image: ${image.name}`)
}

// 滚动事件
const onScroll = (scrollTop: number) => {
  emit('scroll', scrollTop)
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) {
    return '今天'
  } else if (diffDays === 2) {
    return '昨天'
  } else if (diffDays <= 7) {
    return `${diffDays} 天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// 高亮搜索关键词
const highlightText = (text: string): string => {
  if (!props.searchQuery || !text) return text

  const regex = new RegExp(`(${escapeRegExp(props.searchQuery)})`, 'gi')
  return text.replace(regex, '<mark class="search-highlight">$1</mark>')
}

// 转义正则表达式特殊字符
const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

// 暴露方法给父组件
const scrollToTop = () => {
  virtualGridRef.value?.scrollToTop()
}

const scrollToItem = (index: number) => {
  virtualGridRef.value?.scrollToItem(index)
}

defineExpose({
  scrollToTop,
  scrollToItem
})
</script>

<style scoped>
.image-grid {
  height: 100%;
  width: 100%;
}

.image-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.image-thumbnail {
  position: relative;
  flex: 1;
  min-height: 180px;
  overflow: hidden;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-card:hover .thumbnail-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-card:hover .image-overlay {
  opacity: 1;
}

.overlay-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: white;
  transform: scale(1.1);
}

.delete-btn:hover {
  background: #dc3545;
  color: white;
}

.selection-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
}

.selection-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.selection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 123, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
}

.selection-overlay.selected {
  opacity: 1;
  background: rgba(0, 123, 255, 0.2);
}

.selection-indicator {
  width: 40px;
  height: 40px;
  background: rgba(0, 123, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  transform: scale(0);
  transition: transform 0.2s ease;
}

.selection-overlay.selected .selection-indicator {
  transform: scale(1);
}

.image-card:hover .selection-overlay {
  opacity: 0.5;
}

.image-card.selectable {
  cursor: pointer;
}

.image-card.selected {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
  border: 2px solid #007bff;
}

.image-info {
  padding: 12px;
  flex-shrink: 0;
}

.image-name {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 4px;
}

.image-album {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #007bff;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
}

.empty-content {
  text-align: center;
  color: #6c757d;
}

.empty-content i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-content h3 {
  margin-bottom: 0.5rem;
  color: #495057;
}

.empty-content p {
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

/* 搜索高亮样式 */
.image-name :deep(.search-highlight),
.image-album :deep(.search-highlight) {
  background: linear-gradient(120deg, #ffd54f 0%, #ffeb3b 100%);
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: 600;
  color: #333;
  box-shadow: 0 1px 3px rgba(255, 193, 7, 0.3);
}
</style>
