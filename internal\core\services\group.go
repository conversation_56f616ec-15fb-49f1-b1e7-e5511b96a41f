package services

import (
	"errors"
	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
)

// GroupService 用户组服务
type GroupService struct{}

// NewGroupService 创建用户组服务实例
func NewGroupService() *GroupService {
	return &GroupService{}
}

// GetDefaultGroup 获取默认用户组
func (gs *GroupService) GetDefaultGroup() (*models.Group, error) {
	var group models.Group
	err := database.DB.Where("is_default = ? AND is_active = ?", true, true).First(&group).Error
	if err != nil {
		return nil, err
	}
	return &group, nil
}

// SetDefaultGroup 设置默认用户组
func (gs *GroupService) SetDefaultGroup(groupID uint) error {
	// 开始事务
	tx := database.DB.Begin()

	// 取消所有组的默认状态
	if err := tx.Model(&models.Group{}).Where("is_default = ?", true).Update("is_default", false).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 设置新的默认组
	if err := tx.Model(&models.Group{}).Where("id = ?", groupID).Update("is_default", true).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// AddUserToDefaultGroup 将用户添加到默认用户组
func (gs *GroupService) AddUserToDefaultGroup(userID uint) error {
	// 获取默认用户组
	defaultGroup, err := gs.GetDefaultGroup()
	if err != nil {
		return err
	}

	// 检查用户是否已在默认组中
	var existingRelation models.UserGroup
	if database.DB.Where("user_id = ? AND group_id = ?", userID, defaultGroup.ID).
		First(&existingRelation).Error == nil {
		return nil // 用户已在默认组中
	}

	// 添加用户到默认组
	userGroup := models.UserGroup{
		UserID:  userID,
		GroupID: defaultGroup.ID,
		AddedBy: 1, // 系统添加
	}

	return database.DB.Create(&userGroup).Error
}

// EnsureDefaultGroupExists 确保默认用户组存在
func (gs *GroupService) EnsureDefaultGroupExists() error {
	var count int64
	database.DB.Model(&models.Group{}).Where("is_default = ? AND is_active = ?", true, true).Count(&count)

	if count == 0 {
		// 如果没有默认组，将"系统默认组"设为默认
		var defaultGroup models.Group
		err := database.DB.Where("name = ?", "default_group").First(&defaultGroup).Error
		if err != nil {
			// 如果系统默认组不存在，创建一个
			defaultGroup = models.Group{
				Name:        "default_group",
				DisplayName: "系统默认组",
				Description: "系统默认用户组，新用户自动加入此组，拥有基本的使用权限",
				IsSystem:    true,
				IsActive:    true,
				IsDefault:   true,
				CreatedBy:   1,
			}
			return database.DB.Create(&defaultGroup).Error
		} else {
			// 设置为默认组
			return gs.SetDefaultGroup(defaultGroup.ID)
		}
	}

	return nil
}

// GetAllGroups 获取所有用户组
func (gs *GroupService) GetAllGroups() ([]models.Group, error) {
	var groups []models.Group
	err := database.DB.Preload("Permissions").Preload("Creator").Find(&groups).Error
	return groups, err
}

// GetGroupByID 根据ID获取用户组
func (gs *GroupService) GetGroupByID(id uint) (*models.Group, error) {
	var group models.Group
	err := database.DB.Preload("Permissions").Preload("Users").Preload("Creator").First(&group, id).Error
	if err != nil {
		return nil, err
	}
	return &group, nil
}

// CreateGroup 创建用户组
func (gs *GroupService) CreateGroup(group *models.Group) error {
	// 检查组名是否已存在
	var existingGroup models.Group
	if database.DB.Where("name = ?", group.Name).First(&existingGroup).Error == nil {
		return errors.New("组名已存在")
	}

	// 如果设置为默认组，需要先取消其他组的默认状态
	if group.IsDefault {
		if err := gs.SetDefaultGroup(0); err != nil { // 先清除所有默认状态
			return err
		}
	}

	return database.DB.Create(group).Error
}

// UpdateGroup 更新用户组
func (gs *GroupService) UpdateGroup(id uint, updates map[string]interface{}) error {
	// 检查是否为系统组
	var group models.Group
	if database.DB.First(&group, id).Error != nil {
		return errors.New("用户组不存在")
	}

	if group.IsSystem {
		// 系统组只能修改描述和激活状态
		allowedFields := map[string]interface{}{}
		if desc, ok := updates["description"]; ok {
			allowedFields["description"] = desc
		}
		if active, ok := updates["is_active"]; ok {
			allowedFields["is_active"] = active
		}
		updates = allowedFields
	}

	// 如果要设置为默认组
	if isDefault, ok := updates["is_default"]; ok && isDefault.(bool) {
		if err := gs.SetDefaultGroup(id); err != nil {
			return err
		}
		delete(updates, "is_default") // 从updates中移除，因为已经处理了
	}

	return database.DB.Model(&models.Group{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteGroup 删除用户组
func (gs *GroupService) DeleteGroup(id uint) error {
	// 检查是否为系统组
	var group models.Group
	if database.DB.First(&group, id).Error != nil {
		return errors.New("用户组不存在")
	}

	if group.IsSystem {
		return errors.New("不能删除系统组")
	}

	if group.IsDefault {
		return errors.New("不能删除默认组，请先设置其他组为默认组")
	}

	// 开始事务
	tx := database.DB.Begin()

	// 删除组权限关联
	if err := tx.Where("group_id = ?", id).Delete(&models.GroupPermission{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除用户组关联
	if err := tx.Where("group_id = ?", id).Delete(&models.UserGroup{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除用户组
	if err := tx.Delete(&group).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetGroupStats 获取用户组统计信息
func (gs *GroupService) GetGroupStats() ([]models.GroupStats, error) {
	var stats []models.GroupStats

	query := `
		SELECT 
			g.id as group_id,
			g.display_name as group_name,
			COUNT(ug.user_id) as member_count,
			g.created_at
		FROM groups g
		LEFT JOIN user_groups ug ON g.id = ug.group_id
		WHERE g.is_active = true
		GROUP BY g.id, g.display_name, g.created_at
		ORDER BY member_count DESC
	`

	err := database.DB.Raw(query).Scan(&stats).Error
	return stats, err
}
