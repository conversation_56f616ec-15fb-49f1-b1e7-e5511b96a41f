<template>
  <div class="settings-container">
    <div class="page-header">
      <h2><i class="bi bi-gear"></i> 系统设置</h2>
      <p class="page-subtitle">管理个人账户和应用设置</p>
    </div>
    
    <div class="settings-grid">

      <div class="card setting-card">
        <div class="card-header">
          <h4><i class="bi bi-link"></i> 链接设置</h4>
        </div>
        <div class="card-body">
          <div class="form-group">
            <label>默认图片链接格式</label>
            <select class="form-select" v-model="settings.linkFormat">
              <option value="direct">直链</option>
              <option value="thumbnail">缩略图</option>
              <option value="custom">自定义参数</option>
            </select>
            <div class="form-text">设置复制链接时的默认格式</div>
          </div>
          
          <div class="form-group">
            <label>链接有效期</label>
            <select class="form-select" v-model="settings.linkExpiration">
              <option value="never">永不过期</option>
              <option value="1h">1小时</option>
              <option value="1d">1天</option>
              <option value="1w">1周</option>
              <option value="1m">1个月</option>
            </select>
            <div class="form-text">设置生成链接的有效期</div>
          </div>
        </div>
      </div>
      
      <div class="card setting-card">
        <div class="card-header">
          <h4><i class="bi bi-palette"></i> 外观设置</h4>
        </div>
        <div class="card-body">
          <div class="form-group">
            <label>主题模式</label>
            <select class="form-select" v-model="settings.theme">
              <option value="light">浅色主题</option>
              <option value="dark">深色主题</option>
              <option value="auto">自动</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>语言</label>
            <select class="form-select" v-model="settings.language">
              <option value="zh">中文</option>
              <option value="en">English</option>
            </select>
          </div>
        </div>
      </div>
      
      <div class="card setting-card">
        <div class="card-header">
          <h4><i class="bi bi-shield-lock"></i> 安全设置</h4>
        </div>
        <div class="card-body">
          <div class="form-group">
            <div class="form-check form-switch">
              <input 
                class="form-check-input" 
                type="checkbox" 
                id="autoCleanup" 
                v-model="settings.autoCleanup"
              >
              <label class="form-check-label" for="autoCleanup">启用自动清理</label>
            </div>
            <div class="form-text">定期清理30天未访问的图片</div>
          </div>
          
          <div class="form-group">
            <label>默认上传相册</label>
            <select class="form-select" v-model="settings.defaultAlbumId">
              <option :value="null">无相册</option>
              <option v-for="album in albums" :key="album.id" :value="album.id">
                {{ album.name }}
              </option>
            </select>
            <div class="form-text">设置上传图片时的默认相册</div>
          </div>

          <div class="form-group">
            <div class="form-check form-switch">
              <input
                class="form-check-input"
                type="checkbox"
                id="privateUploads"
                v-model="settings.privateUploads"
              >
              <label class="form-check-label" for="privateUploads">默认私有上传</label>
            </div>
            <div class="form-text">新上传的图片默认为私有</div>
          </div>
        </div>
      </div>
      
      <div class="card setting-card">
        <div class="card-header">
          <h4><i class="bi bi-hdd"></i> 存储设置</h4>
        </div>
        <div class="card-body">
          <div class="form-group">
            <label>最大文件大小</label>
            <select class="form-select" v-model="settings.maxFileSize">
              <option value="5">5 MB</option>
              <option value="10">10 MB</option>
              <option value="20">20 MB</option>
              <option value="50">50 MB</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>允许的文件类型</label>
            <div class="file-types">
              <div 
                class="file-type" 
                :class="{ active: settings.allowedTypes.includes(type) }"
                v-for="type in fileTypes" 
                :key="type"
                @click="toggleFileType(type)"
              >
                {{ type }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
    </div>
    
    <div class="actions">
      <button class="btn btn-primary" @click="saveSettings">
        <i class="bi bi-save"></i> 保存设置
      </button>
      <button class="btn btn-outline" @click="resetSettings">
        <i class="bi bi-arrow-counterclockwise"></i> 重置
      </button>
    </div>
    
    <!-- 通知消息 -->
    <div v-if="notification.message" class="notification" :class="notification.type">
      <i :class="getNotificationIcon(notification.type)"></i> {{ notification.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { useAlbumStore } from '../stores/album';
import { useAuthStore } from '../stores/auth';
import { useDialog } from '../utils/dialog';

const albumStore = useAlbumStore();
const authStore = useAuthStore();
const { confirm, notify } = useDialog();

const settings = reactive({
  linkFormat: 'direct',
  linkExpiration: 'never',
  theme: 'light',
  language: 'zh',
  autoCleanup: false,
  privateUploads: false,
  maxFileSize: '10',
  allowedTypes: ['jpg', 'png', 'gif'],
  defaultAlbumId: null as number | null
});

const albums = ref<Array<{id: number, name: string}>>([]);


const fileTypes = ['jpg', 'png', 'gif', 'webp', 'svg', 'bmp'];
const notification = ref({ message: '', type: 'info' });

// 切换文件类型
const toggleFileType = (type: string) => {
  const index = settings.allowedTypes.indexOf(type);
  if (index > -1) {
    settings.allowedTypes.splice(index, 1);
  } else {
    settings.allowedTypes.push(type);
  }
};

// 初始化数据
const initData = async () => {
  try {
    // 获取相册列表
    const albumResult = await albumStore.fetchAlbums();
    if (albumResult.success) {
      albums.value = albumStore.getAlbumList.map(album => ({
        id: album.id,
        name: album.name
      }));
    } else {
      albums.value = [];
    }

    // 获取用户设置
    if (authStore.getUser?.default_album_id) {
      // 检查默认相册是否还存在
      const albumExists = albums.value.some(album => album.id === authStore.getUser?.default_album_id);
      if (albumExists) {
        settings.defaultAlbumId = authStore.getUser.default_album_id;
      } else {
        // 如果默认相册不存在，清除设置
        settings.defaultAlbumId = null;
      }
    }
  } catch (error) {
    console.error('Failed to load data:', error);
    albums.value = [];
  }
};

// 保存设置
const saveSettings = async () => {
  try {
    // 保存默认相册设置
    if (settings.defaultAlbumId !== authStore.getUser?.default_album_id) {
      const result = await authStore.updateUserSettings({
        default_album_id: settings.defaultAlbumId || undefined
      });

      if (!result.success) {
        showNotification(result.message || '保存设置失败', 'error');
        return;
      }
    }

    // 应用主题设置
    applyTheme(settings.theme);
    localStorage.setItem('theme', settings.theme);

    // 保存其他设置到本地存储
    localStorage.setItem('settings', JSON.stringify(settings));
    showNotification('设置已保存', 'success');
  } catch (error) {
    showNotification('保存设置失败', 'error');
  }
};

// 重置设置
const resetSettings = async () => {
  const confirmed = await confirm('确定要重置所有设置吗？', {
    title: '重置设置',
    details: '这将恢复所有设置到默认值。',
    type: 'warning',
    confirmText: '重置',
    cancelText: '取消'
  });

  if (confirmed) {
    settings.linkFormat = 'direct';
    settings.linkExpiration = 'never';
    settings.theme = 'light';
    settings.language = 'zh';
    settings.autoCleanup = false;
    settings.privateUploads = false;
    settings.maxFileSize = '10';
    settings.allowedTypes = ['jpg', 'png', 'gif'];
    notify.info('设置已重置');
  }
};

// 应用主题设置
const applyTheme = (theme: string) => {
  if (theme === 'dark') {
    document.body.classList.add('dark-theme');
    document.body.classList.remove('light-theme');
  } else if (theme === 'light') {
    document.body.classList.add('light-theme');
    document.body.classList.remove('dark-theme');
  } else {
    // 自动模式 - 根据系统偏好设置
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (prefersDark) {
      document.body.classList.add('dark-theme');
      document.body.classList.remove('light-theme');
    } else {
      document.body.classList.add('light-theme');
      document.body.classList.remove('dark-theme');
    }
  }
};

// 组件挂载时初始化数据和应用保存的主题
onMounted(async () => {
  const savedTheme = localStorage.getItem('theme') || 'light';
  settings.theme = savedTheme;
  applyTheme(savedTheme);

  // 初始化数据
  await initData();
});

// 监听主题变化
watch(() => settings.theme, (newTheme) => {
  applyTheme(newTheme);
});

// 显示通知消息
const showNotification = (message: string, type: string) => {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value.message = '';
  }, 3000);
};

// 获取通知图标
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'success': return 'bi bi-check-circle';
    case 'error': return 'bi bi-exclamation-circle';
    case 'info': return 'bi bi-info-circle';
    default: return 'bi bi-info-circle';
  }
};
</script>

<style scoped>
.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px; /* 添加内边距以适应缩放 */
}

.page-header {
  margin-bottom: 25px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: var(--text-color);
  font-weight: 600;
}

.page-header h2 i {
  margin-right: 10px;
  color: #007bff;
}

body.dark-theme .page-header h2 i {
  color: #66b3ff;
}

.page-subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.card {
  background: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid var(--border);
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid var(--border);
  background-color: var(--header-bg);
}

.card-header h4 {
  margin: 0;
  color: var(--text-color);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-body {
  padding: 20px;
  background-color: var(--card-bg);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-select {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 16px;
  box-sizing: border-box;
  background-color: var(--input-bg);
  color: var(--text-color);
  transition: border-color 0.3s;
}

.form-select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

body.dark-theme .form-select:focus {
  border-color: #66b3ff;
  box-shadow: 0 0 0 3px rgba(102, 179, 255, 0.1);
}

.form-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-top: 5px;
}

.form-check-input {
  width: 1.5em;
  height: 1.5em;
  margin-top: 0.1em;
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
}

.form-check-input:checked {
  background-color: #007bff;
  border-color: #007bff;
}

body.dark-theme .form-check-input:checked {
  background-color: #66b3ff;
  border-color: #66b3ff;
}

.form-check-label {
  font-weight: normal;
  margin-left: 8px;
  color: var(--text-color);
}

.file-types {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.file-type {
  padding: 8px 15px;
  background-color: var(--header-bg);
  border-radius: 20px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid var(--border);
  color: var(--text-color);
}

.file-type:hover {
  background-color: var(--card-bg);
}

.file-type.active {
  background: linear-gradient(135deg, #007bff, #00bcd4);
  color: white;
  border-color: transparent;
}

body.dark-theme .file-type.active {
  background: linear-gradient(135deg, #3a57b0, #1a8fc9);
}


.actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-bottom: 30px;
}

.btn {
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s;
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  background-color: var(--card-bg);
  color: var(--text-color);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff, #00bcd4);
  color: white;
}

body.dark-theme .btn-primary {
  background: linear-gradient(135deg, #3a57b0, #1a8fc9);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

body.dark-theme .btn-primary:hover {
  box-shadow: 0 5px 15px rgba(102, 179, 255, 0.3);
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--border);
  color: var(--text-color);
}

.btn-outline:hover {
  background-color: var(--header-bg);
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  color: white;
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
  z-index: 1000;
  animation: slideIn 0.3s, fadeOut 0.5s 2.5s forwards;
  display: flex;
  align-items: center;
  gap: 10px;
  max-width: 350px;
}

.notification.success {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.notification.error {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.notification.info {
  background: linear-gradient(135deg, #17a2b8, #00bcd4);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .actions {
    justify-content: center;
  }
  
  .settings-container {
    padding: 0 10px;
  }
  
  .card-body {
    padding: 15px;
  }
}

/* 针对浏览器缩放的优化 */
@media screen and (-webkit-min-device-pixel-ratio: 1.25), 
       screen and (min-resolution: 120dpi) {
  .form-select {
    padding: 10px;
    font-size: 15px;
  }
  
  .file-type {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 1.5), 
       screen and (min-resolution: 144dpi) {
  .settings-grid {
    gap: 20px;
  }
  
  .card-header {
    padding: 18px;
  }
  
  .card-body {
    padding: 18px;
  }
}
</style>