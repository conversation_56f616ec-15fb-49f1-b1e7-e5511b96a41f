package dao

import (
	"cloudbed/internal/database"
	"cloudbed/internal/core/domain"
	"cloudbed/pkg/pagination"
)

// CreateAlbum 创建新相册
func CreateAlbum(album *models.Album) error {
	return database.DB.Create(album).Error
}

// GetAlbumsByUserID 根据用户ID获取相册列表
func GetAlbumsByUserID(userID uint) ([]models.Album, error) {
	var albums []models.Album
	err := database.DB.Where("user_id = ?", userID).
		Preload("Images").
		Find(&albums).Error
	return albums, err
}

// GetAlbumByID 根据ID获取相册
func GetAlbumByID(id uint) (*models.Album, error) {
	var album models.Album
	err := database.DB.Preload("Images").First(&album, id).Error
	return &album, err
}

// GetAlbumByIDAndUserID 根据ID和用户ID获取相册（确保用户权限）
func GetAlbumByIDAndUserID(id, userID uint) (*models.Album, error) {
	var album models.Album
	err := database.DB.Where("id = ? AND user_id = ?", id, userID).
		Preload("Images").
		First(&album).Error
	return &album, err
}

// UpdateAlbum 更新相册信息
func UpdateAlbum(album *models.Album) error {
	return database.DB.Save(album).Error
}

// DeleteAlbum 删除相册
func DeleteAlbum(id uint) error {
	// 先将相册中的图片移到默认相册（AlbumID设为nil）
	if err := database.DB.Model(&models.Image{}).
		Where("album_id = ?", id).
		Update("album_id", nil).Error; err != nil {
		return err
	}

	// 将使用此相册作为默认相册的用户的默认相册设为nil
	if err := database.DB.Model(&models.User{}).
		Where("default_album_id = ?", id).
		Update("default_album_id", nil).Error; err != nil {
		return err
	}

	// 删除相册
	return database.DB.Delete(&models.Album{}, id).Error
}

// DeleteAlbumByUserID 删除用户的相册（确保用户权限）
func DeleteAlbumByUserID(id, userID uint) error {
	// 先将相册中的图片移到默认相册（AlbumID设为nil）
	if err := database.DB.Model(&models.Image{}).
		Where("album_id = ?", id).
		Update("album_id", nil).Error; err != nil {
		return err
	}

	// 将使用此相册作为默认相册的用户的默认相册设为nil
	if err := database.DB.Model(&models.User{}).
		Where("default_album_id = ?", id).
		Update("default_album_id", nil).Error; err != nil {
		return err
	}

	// 删除相册
	return database.DB.Where("id = ? AND user_id = ?", id, userID).
		Delete(&models.Album{}).Error
}

// GetAlbumWithImageCount 获取相册及其图片数量（已优化）
func GetAlbumWithImageCount(userID uint) ([]models.AlbumResponse, error) {
	var albums []models.AlbumResponse

	// 这个函数已经是优化的，使用了JOIN和GROUP BY避免N+1查询
	err := database.DB.Table("albums").
		Select("albums.id, albums.name, albums.description, albums.cover_image, albums.created_at, albums.updated_at, COUNT(images.id) as image_count, COALESCE(SUM(images.size), 0) as total_size").
		Joins("LEFT JOIN images ON albums.id = images.album_id").
		Where("albums.user_id = ?", userID).
		Group("albums.id").
		Order("albums.created_at DESC").
		Scan(&albums).Error

	return albums, err
}

// UpdateAlbumCover 更新相册封面
func UpdateAlbumCover(albumID uint, coverImageURL string) error {
	return database.DB.Model(&models.Album{}).
		Where("id = ?", albumID).
		Update("cover_image", coverImageURL).Error
}

// CheckAlbumOwnership 检查相册是否属于指定用户
func CheckAlbumOwnership(albumID, userID uint) (bool, error) {
	var count int64
	err := database.DB.Model(&models.Album{}).
		Where("id = ? AND user_id = ?", albumID, userID).
		Count(&count).Error
	return count > 0, err
}

// GetAlbumsByUserIDPaginated 分页获取用户相册列表
func GetAlbumsByUserIDPaginated(userID uint, req pagination.AlbumPaginationRequest) (pagination.Response, error) {
	var albums []models.Album

	query := database.DB.Model(&models.Album{}).Where("user_id = ?", userID)

	// 应用搜索过滤
	if req.Search != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	// 使用分页工具进行查询（预加载图片信息）
	return pagination.PaginateWithPreload(query, req.Request, &albums, "created_at DESC", "Images")
}

// GetAlbumImagesPaginated 分页获取相册中的图片
func GetAlbumImagesPaginated(albumID uint, req pagination.Request) (pagination.Response, error) {
	var images []models.Image

	query := database.DB.Model(&models.Image{}).Where("album_id = ?", albumID)

	// 使用分页工具进行查询
	return pagination.PaginateWithPreload(query, req, &images, "created_at DESC", "User", "User.Role", "Album")
}
