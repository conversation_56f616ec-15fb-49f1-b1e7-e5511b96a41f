<template>
  <div class="lazy-image-container" :class="{ 'loading': isLoading, 'error': hasError }">
    <!-- 占位符 -->
    <div v-if="isLoading" class="image-placeholder">
      <div class="placeholder-content">
        <i class="bi bi-image"></i>
        <span v-if="showLoadingText">加载中...</span>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="hasError" class="image-error">
      <div class="error-content">
        <i class="bi bi-exclamation-triangle"></i>
        <span>加载失败</span>
        <button class="retry-btn" @click="retryLoad" title="重试">
          <i class="bi bi-arrow-clockwise"></i>
        </button>
      </div>
    </div>
    
    <!-- 实际图片 -->
    <img
      v-else
      ref="imageRef"
      :src="actualSrc"
      :alt="alt"
      :class="imageClass"
      @load="onLoad"
      @error="onError"
      :style="imageStyle"
    />
    
    <!-- 加载进度条（可选） -->
    <div v-if="showProgress && isLoading" class="loading-progress">
      <div class="progress-bar" :style="{ width: progress + '%' }"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'

interface Props {
  src: string
  alt?: string
  placeholder?: string
  errorImage?: string
  threshold?: number
  rootMargin?: string
  showLoadingText?: boolean
  showProgress?: boolean
  imageClass?: string
  imageStyle?: Record<string, any>
  lazy?: boolean
  thumbnails?: {
    small?: string
    medium?: string
    large?: string
    xlarge?: string
  }
  preferredSize?: 'small' | 'medium' | 'large' | 'xlarge' | 'original'
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  placeholder: '',
  errorImage: '',
  threshold: 0.1,
  rootMargin: '50px',
  showLoadingText: true,
  showProgress: false,
  imageClass: '',
  imageStyle: () => ({}),
  lazy: true,
  thumbnails: () => ({}),
  preferredSize: 'medium'
})

const emit = defineEmits<{
  load: [event: Event]
  error: [event: Event]
  intersect: [isIntersecting: boolean]
}>()

const imageRef = ref<HTMLImageElement>()
const isLoading = ref(true)
const hasError = ref(false)
const isIntersecting = ref(false)
const progress = ref(0)
const observer = ref<IntersectionObserver>()

// 实际显示的图片源
const actualSrc = computed(() => {
  if (hasError.value && props.errorImage) {
    return props.errorImage
  }
  if (!props.lazy || isIntersecting.value) {
    // 优先使用缩略图
    if (props.thumbnails && Object.keys(props.thumbnails).length > 0) {
      const thumbnail = getThumbnailUrl()
      if (thumbnail) {
        return thumbnail
      }
    }
    return props.src
  }
  return props.placeholder || ''
})

// 获取合适的缩略图URL
const getThumbnailUrl = () => {
  if (!props.thumbnails) return null

  // 根据首选尺寸获取缩略图
  switch (props.preferredSize) {
    case 'small':
      return props.thumbnails.small || props.thumbnails.medium || props.thumbnails.large || props.thumbnails.xlarge
    case 'medium':
      return props.thumbnails.medium || props.thumbnails.large || props.thumbnails.small || props.thumbnails.xlarge
    case 'large':
      return props.thumbnails.large || props.thumbnails.xlarge || props.thumbnails.medium || props.thumbnails.small
    case 'xlarge':
      return props.thumbnails.xlarge || props.thumbnails.large || props.thumbnails.medium || props.thumbnails.small
    case 'original':
      return null // 使用原图
    default:
      return props.thumbnails.medium || props.thumbnails.large || props.thumbnails.small || props.thumbnails.xlarge
  }
}

// 初始化 Intersection Observer
const initObserver = () => {
  if (!props.lazy) {
    isIntersecting.value = true
    return
  }

  observer.value = new IntersectionObserver(
    (entries) => {
      const entry = entries[0]
      if (entry.isIntersecting) {
        isIntersecting.value = true
        emit('intersect', true)
        // 一旦开始加载就停止观察
        if (observer.value) {
          observer.value.disconnect()
        }
      }
    },
    {
      threshold: props.threshold,
      rootMargin: props.rootMargin
    }
  )

  // 观察容器元素
  const container = imageRef.value?.parentElement
  if (container) {
    observer.value.observe(container)
  }
}

// 图片加载成功
const onLoad = (event: Event) => {
  isLoading.value = false
  hasError.value = false
  progress.value = 100
  emit('load', event)
}

// 图片加载失败
const onError = (event: Event) => {
  isLoading.value = false
  hasError.value = true
  emit('error', event)
}

// 重试加载
const retryLoad = () => {
  hasError.value = false
  isLoading.value = true
  progress.value = 0
  
  // 强制重新加载图片
  if (imageRef.value) {
    imageRef.value.src = ''
    setTimeout(() => {
      if (imageRef.value) {
        imageRef.value.src = props.src
      }
    }, 100)
  }
}

// 模拟加载进度（可选）
const simulateProgress = () => {
  if (!props.showProgress) return
  
  const interval = setInterval(() => {
    if (progress.value < 90 && isLoading.value) {
      progress.value += Math.random() * 10
    } else {
      clearInterval(interval)
    }
  }, 200)
}

// 监听src变化
watch(() => props.src, () => {
  isLoading.value = true
  hasError.value = false
  progress.value = 0
  
  if (props.showProgress) {
    simulateProgress()
  }
})

onMounted(() => {
  initObserver()
  
  if (props.showProgress) {
    simulateProgress()
  }
})

onUnmounted(() => {
  if (observer.value) {
    observer.value.disconnect()
  }
})
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.image-placeholder,
.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 120px;
  background-color: #f8f9fa;
  color: #6c757d;
}

.placeholder-content,
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.placeholder-content i,
.error-content i {
  font-size: 2rem;
  opacity: 0.5;
}

.placeholder-content span,
.error-content span {
  font-size: 0.875rem;
  opacity: 0.7;
}

.retry-btn {
  background: none;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 4px 8px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background-color: #e9ecef;
  color: #495057;
}

.lazy-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.loading-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: rgba(0, 0, 0, 0.1);
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  transition: width 0.3s ease;
}

/* 加载动画 */
.loading .placeholder-content i {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}

/* 错误状态样式 */
.error .error-content i {
  color: #dc3545;
}
</style>
