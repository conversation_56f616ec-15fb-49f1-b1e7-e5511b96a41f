import { defineStore } from 'pinia';
import { ref } from 'vue';
import api from '../services/api';
import type { StorageQuotaInfo } from '../types/storage';

export const useStorageStore = defineStore('storage', () => {
  // 状态
  const storageInfo = ref<StorageQuotaInfo | null>(null);
  const loading = ref(false);
  const error = ref('');

  // 加载存储信息
  const loadStorageInfo = async () => {
    loading.value = true;
    error.value = '';
    
    try {
      const response = await api.getUserStorageInfo();
      storageInfo.value = response;
      return response;
    } catch (err: any) {
      error.value = err.response?.data?.error || '加载存储信息失败';
      console.error('Failed to load storage info:', err);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 刷新存储信息
  const refreshStorageInfo = async () => {
    return await loadStorageInfo();
  };

  // 更新存储使用量（用于上传/删除图片后的本地更新）
  const updateStorageUsed = (sizeChange: number) => {
    if (storageInfo.value) {
      const newUsed = Math.max(0, storageInfo.value.storage_used + sizeChange);
      storageInfo.value.storage_used = newUsed;
      
      // 重新计算百分比和其他字段
      const usagePercent = storageInfo.value.storage_quota > 0 
        ? (newUsed / storageInfo.value.storage_quota) * 100 
        : 0;
      
      storageInfo.value.usage_percent = usagePercent;
      storageInfo.value.used_gb = newUsed / (1024 * 1024 * 1024);
      storageInfo.value.available_gb = storageInfo.value.quota_gb - storageInfo.value.used_gb;
    }
  };

  // 清除存储信息（登出时使用）
  const clearStorageInfo = () => {
    storageInfo.value = null;
    error.value = '';
  };

  // 延迟刷新存储信息（防止频繁调用）
  let refreshTimeout: number | null = null;
  const debouncedRefresh = () => {
    if (refreshTimeout) {
      clearTimeout(refreshTimeout);
    }
    refreshTimeout = setTimeout(() => {
      refreshStorageInfo();
    }, 1000); // 1秒后刷新
  };

  return {
    // 状态
    storageInfo,
    loading,
    error,

    // 方法
    loadStorageInfo,
    refreshStorageInfo,
    updateStorageUsed,
    clearStorageInfo,
    debouncedRefresh
  };
});
