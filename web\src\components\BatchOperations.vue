<template>
  <div class="batch-operations" :class="{ visible: selectedItems.length > 0 }">
    <div class="batch-toolbar">
      <div class="selection-info">
        <span class="selected-count">已选择 {{ selectedItems.length }} 项</span>
        <button class="select-all-btn" @click="$emit('selectAll')" v-if="!allSelected">
          全选
        </button>
        <button class="clear-selection-btn" @click="$emit('clearSelection')">
          取消选择
        </button>
      </div>

      <div class="batch-actions">
        <!-- 移动到相册 -->
        <div class="action-group">
          <button
            class="action-btn move-btn"
            @click="showMoveModal = true"
            :disabled="selectedItems.length === 0"
            title="移动到相册"
          >
            <i class="bi bi-folder-plus"></i>
            移动
          </button>
        </div>

        <!-- 下载 -->
        <div class="action-group">
          <button
            class="action-btn download-btn"
            @click="downloadSelected"
            :disabled="selectedItems.length === 0 || downloading"
            title="批量下载"
          >
            <i class="bi bi-download" v-if="!downloading"></i>
            <i class="bi bi-arrow-repeat spin" v-else></i>
            {{ downloading ? '准备中...' : '下载' }}
          </button>
        </div>

        <!-- 删除 -->
        <div class="action-group">
          <button
            class="action-btn delete-btn"
            @click="showDeleteConfirm = true"
            :disabled="selectedItems.length === 0"
            title="批量删除"
          >
            <i class="bi bi-trash"></i>
            删除
          </button>
        </div>
      </div>
    </div>

    <!-- 移动到相册模态框 -->
    <div v-if="showMoveModal" class="modal-overlay" @click="showMoveModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>移动到相册</h3>
          <button class="close-btn" @click="showMoveModal = false">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <p>将 {{ selectedItems.length }} 张图片移动到：</p>
          
          <div class="album-options">
            <div
              class="album-option"
              :class="{ selected: selectedAlbumId === null }"
              @click="selectedAlbumId = null"
            >
              <i class="bi bi-folder"></i>
              <span>未分类</span>
            </div>
            
            <div
              v-for="album in albums"
              :key="album.id"
              class="album-option"
              :class="{ selected: selectedAlbumId === album.id }"
              @click="selectedAlbumId = album.id"
            >
              <i class="bi bi-folder-fill"></i>
              <span>{{ album.name }}</span>
              <span class="image-count">({{ album.image_count || 0 }})</span>
            </div>
          </div>

          <div class="create-album-section">
            <button
              class="create-album-btn"
              @click="showCreateAlbum = !showCreateAlbum"
            >
              <i class="bi bi-plus"></i>
              创建新相册
            </button>
            
            <div v-if="showCreateAlbum" class="create-album-form">
              <input
                v-model="newAlbumName"
                type="text"
                placeholder="相册名称"
                class="album-name-input"
                @keydown.enter="createAlbum"
              />
              <button class="create-btn" @click="createAlbum" :disabled="!newAlbumName.trim()">
                创建
              </button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="showMoveModal = false">取消</button>
          <button
            class="confirm-btn"
            @click="moveToAlbum"
            :disabled="moving"
          >
            <i class="bi bi-arrow-repeat spin" v-if="moving"></i>
            {{ moving ? '移动中...' : '确认移动' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteConfirm" class="modal-overlay" @click="showDeleteConfirm = false">
      <div class="modal-content delete-modal" @click.stop>
        <div class="modal-header">
          <h3>确认删除</h3>
          <button class="close-btn" @click="showDeleteConfirm = false">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="warning-icon">
            <i class="bi bi-exclamation-triangle"></i>
          </div>
          <p>您确定要删除这 {{ selectedItems.length }} 张图片吗？</p>
          <p class="warning-text">此操作不可撤销，图片将被永久删除。</p>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="showDeleteConfirm = false">取消</button>
          <button
            class="delete-confirm-btn"
            @click="deleteSelected"
            :disabled="deleting"
          >
            <i class="bi bi-arrow-repeat spin" v-if="deleting"></i>
            {{ deleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 操作进度提示 -->
    <div v-if="operationProgress.show" class="progress-toast">
      <div class="progress-content">
        <i class="bi bi-arrow-repeat spin"></i>
        <span>{{ operationProgress.message }}</span>
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: operationProgress.percent + '%' }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Props {
  selectedItems: number[]
  allSelected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  allSelected: false
})

const emit = defineEmits<{
  selectAll: []
  clearSelection: []
  itemsDeleted: [deletedIds: number[]]
  itemsMoved: [movedIds: number[], albumId: number | null]
  refresh: []
}>()

// 模态框状态
const showMoveModal = ref(false)
const showDeleteConfirm = ref(false)
const showCreateAlbum = ref(false)

// 操作状态
const moving = ref(false)
const deleting = ref(false)
const downloading = ref(false)

// 相册相关
const albums = ref<any[]>([])
const selectedAlbumId = ref<number | null>(null)
const newAlbumName = ref('')

// 操作进度
const operationProgress = ref({
  show: false,
  message: '',
  percent: 0
})

// 计算属性
const selectedAlbumName = computed(() => {
  if (selectedAlbumId.value === null) return '未分类'
  const album = albums.value.find(a => a.id === selectedAlbumId.value)
  return album ? album.name : '未知相册'
})

// 方法
const loadAlbums = async () => {
  try {
    // TODO: 调用API获取相册列表
    // const response = await api.getAlbums()
    // albums.value = response.data
    
    // 模拟数据
    albums.value = [
      { id: 1, name: '家庭照片', image_count: 25 },
      { id: 2, name: '旅行回忆', image_count: 48 },
      { id: 3, name: '工作相关', image_count: 12 }
    ]
  } catch (error) {
    console.error('Failed to load albums:', error)
  }
}

const createAlbum = async () => {
  if (!newAlbumName.value.trim()) return
  
  try {
    // TODO: 调用API创建相册
    // const response = await api.createAlbum({ name: newAlbumName.value })
    // albums.value.push(response.data)
    // selectedAlbumId.value = response.data.id
    
    // 模拟创建
    const newAlbum = {
      id: Date.now(),
      name: newAlbumName.value,
      image_count: 0
    }
    albums.value.push(newAlbum)
    selectedAlbumId.value = newAlbum.id
    
    newAlbumName.value = ''
    showCreateAlbum.value = false
  } catch (error) {
    console.error('Failed to create album:', error)
  }
}

const moveToAlbum = async () => {
  if (props.selectedItems.length === 0) return
  
  moving.value = true
  operationProgress.value = {
    show: true,
    message: `正在移动 ${props.selectedItems.length} 张图片到 ${selectedAlbumName.value}...`,
    percent: 0
  }
  
  try {
    // 模拟进度
    for (let i = 0; i <= 100; i += 10) {
      operationProgress.value.percent = i
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    // TODO: 调用API移动图片
    // await api.batchMoveImages({
    //   image_ids: props.selectedItems,
    //   album_id: selectedAlbumId.value
    // })
    
    emit('itemsMoved', [...props.selectedItems], selectedAlbumId.value)
    emit('clearSelection')
    showMoveModal.value = false
    
    // 显示成功消息
    // notify.success(`成功移动 ${props.selectedItems.length} 张图片到 ${selectedAlbumName.value}`)
  } catch (error) {
    console.error('Failed to move images:', error)
    // notify.error('移动图片失败')
  } finally {
    moving.value = false
    operationProgress.value.show = false
  }
}

const deleteSelected = async () => {
  if (props.selectedItems.length === 0) return
  
  deleting.value = true
  operationProgress.value = {
    show: true,
    message: `正在删除 ${props.selectedItems.length} 张图片...`,
    percent: 0
  }
  
  try {
    // 模拟进度
    for (let i = 0; i <= 100; i += 10) {
      operationProgress.value.percent = i
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    // TODO: 调用API删除图片
    // await api.batchDeleteImages({
    //   image_ids: props.selectedItems
    // })
    
    emit('itemsDeleted', [...props.selectedItems])
    emit('clearSelection')
    showDeleteConfirm.value = false
    
    // 显示成功消息
    // notify.success(`成功删除 ${props.selectedItems.length} 张图片`)
  } catch (error) {
    console.error('Failed to delete images:', error)
    // notify.error('删除图片失败')
  } finally {
    deleting.value = false
    operationProgress.value.show = false
  }
}

const downloadSelected = async () => {
  if (props.selectedItems.length === 0) return
  
  downloading.value = true
  
  try {
    // TODO: 调用API下载图片
    // const response = await api.batchDownloadImages({
    //   image_ids: props.selectedItems
    // })
    
    // 创建下载链接
    // const url = window.URL.createObjectURL(new Blob([response.data]))
    // const link = document.createElement('a')
    // link.href = url
    // link.download = `images_${new Date().getTime()}.zip`
    // document.body.appendChild(link)
    // link.click()
    // document.body.removeChild(link)
    // window.URL.revokeObjectURL(url)
    
    // 模拟下载
    console.log('Downloading images:', props.selectedItems)
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // notify.success(`开始下载 ${props.selectedItems.length} 张图片`)
  } catch (error) {
    console.error('Failed to download images:', error)
    // notify.error('下载失败')
  } finally {
    downloading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadAlbums()
})
</script>

<style scoped>
.batch-operations {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e9ecef;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.batch-operations.visible {
  transform: translateY(0);
}

.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-count {
  font-weight: 600;
  color: #007bff;
}

.select-all-btn,
.clear-selection-btn {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  background: white;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.select-all-btn:hover,
.clear-selection-btn:hover {
  background: #f8f9fa;
  border-color: #007bff;
  color: #007bff;
}

.batch-actions {
  display: flex;
  gap: 12px;
}

.action-group {
  display: flex;
  align-items: center;
}

.action-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.move-btn {
  background: #28a745;
  color: white;
}

.move-btn:hover:not(:disabled) {
  background: #218838;
}

.download-btn {
  background: #17a2b8;
  color: white;
}

.download-btn:hover:not(:disabled) {
  background: #138496;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover:not(:disabled) {
  background: #c82333;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.modal-body {
  padding: 24px;
}

.album-options {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin: 16px 0;
}

.album-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f8f9fa;
}

.album-option:last-child {
  border-bottom: none;
}

.album-option:hover {
  background: #f8f9fa;
}

.album-option.selected {
  background: #e3f2fd;
  color: #1976d2;
}

.image-count {
  margin-left: auto;
  font-size: 0.8rem;
  color: #6c757d;
}

.create-album-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.create-album-btn {
  width: 100%;
  padding: 10px;
  border: 2px dashed #dee2e6;
  background: none;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.create-album-btn:hover {
  border-color: #007bff;
  color: #007bff;
}

.create-album-form {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.album-name-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 0.875rem;
}

.album-name-input:focus {
  outline: none;
  border-color: #007bff;
}

.create-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.create-btn:hover:not(:disabled) {
  background: #0056b3;
}

.create-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
}

.cancel-btn {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: white;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #f8f9fa;
}

.confirm-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.confirm-btn:hover:not(:disabled) {
  background: #0056b3;
}

.confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.delete-modal .modal-body {
  text-align: center;
}

.warning-icon {
  font-size: 3rem;
  color: #dc3545;
  margin-bottom: 16px;
}

.warning-text {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 8px;
}

.delete-confirm-btn {
  padding: 8px 16px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.delete-confirm-btn:hover:not(:disabled) {
  background: #c82333;
}

.delete-confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.progress-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 300px;
  z-index: 3000;
}

.progress-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
