export interface User {
  id: number;
  username: string;
  email: string;
  role?: {
    id: number;
    name: string;
    display_name: string;
    description: string;
    is_system: boolean;
  };
  status?: string;
  last_login_at?: string;
  default_album_id?: number;
  default_album?: {
    id: number;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

export interface UserSettings {
  id: number;
  username: string;
  email: string;
  default_album_id?: number;
}

export interface UserSettingsRequest {
  username?: string;
  default_album_id?: number;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
}

export interface RegisterResponse {
  id: number;
  username: string;
  email: string;
  created_at: string;
  updated_at: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoggedIn: boolean;
}