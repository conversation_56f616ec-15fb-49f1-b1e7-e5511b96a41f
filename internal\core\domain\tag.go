package models

import (
	"fmt"
	"regexp"
	"strings"
	"time"
)

// Tag 标签模型
type Tag struct {
	ID          uint      `json:"id" gorm:"primary_key"`
	Name        string    `json:"name" gorm:"type:varchar(50);not null;uniqueIndex:idx_user_tag_name"`
	Color       string    `json:"color" gorm:"type:varchar(7);default:'#007bff'"` // 十六进制颜色值
	Description string    `json:"description" gorm:"type:varchar(255)"`
	UserID      uint      `json:"user_id" gorm:"not null;uniqueIndex:idx_user_tag_name"`
	User        User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
	UsageCount  int       `json:"usage_count" gorm:"default:0"` // 使用次数
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ImageTag 图片标签关联模型
type ImageTag struct {
	ID        uint      `json:"id" gorm:"primary_key"`
	ImageID   uint      `json:"image_id" gorm:"not null;uniqueIndex:idx_image_tag"`
	TagID     uint      `json:"tag_id" gorm:"not null;uniqueIndex:idx_image_tag"`
	Image     Image     `json:"image,omitempty" gorm:"foreignKey:ImageID"`
	Tag       Tag       `json:"tag,omitempty" gorm:"foreignKey:TagID"`
	UserID    uint      `json:"user_id" gorm:"not null"` // 冗余字段，便于查询
	CreatedAt time.Time `json:"created_at"`
}

// TagCategory 标签分类模型
type TagCategory struct {
	ID          uint      `json:"id" gorm:"primary_key"`
	Name        string    `json:"name" gorm:"type:varchar(50);not null"`
	Color       string    `json:"color" gorm:"type:varchar(7);default:'#6c757d'"`
	Description string    `json:"description" gorm:"type:varchar(255)"`
	UserID      uint      `json:"user_id" gorm:"not null"`
	User        User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
	SortOrder   int       `json:"sort_order" gorm:"default:0"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TagCategoryRelation 标签分类关联模型
type TagCategoryRelation struct {
	ID         uint        `json:"id" gorm:"primary_key"`
	TagID      uint        `json:"tag_id" gorm:"not null"`
	CategoryID uint        `json:"category_id" gorm:"not null"`
	Tag        Tag         `json:"tag,omitempty" gorm:"foreignKey:TagID"`
	Category   TagCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	CreatedAt  time.Time   `json:"created_at"`
}

// TagStats 标签统计信息
type TagStats struct {
	TagID      uint       `json:"tag_id"`
	TagName    string     `json:"tag_name"`
	TagColor   string     `json:"tag_color"`
	ImageCount int64      `json:"image_count"`
	LastUsed   *time.Time `json:"last_used,omitempty"`
}

// ImageWithTags 带标签的图片
type ImageWithTags struct {
	Image
	Tags []Tag `json:"tags"`
}

// TagWithStats 带统计信息的标签
type TagWithStats struct {
	Tag
	ImageCount int64      `json:"image_count"`
	LastUsed   *time.Time `json:"last_used,omitempty"`
}

// PopularTag 热门标签
type PopularTag struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`
	Color      string `json:"color"`
	UsageCount int    `json:"usage_count"`
	ImageCount int64  `json:"image_count"`
}

// TagSuggestion 标签建议
type TagSuggestion struct {
	Name       string  `json:"name"`
	Color      string  `json:"color"`
	Confidence float64 `json:"confidence"` // 建议置信度 0-1
	Source     string  `json:"source"`     // 建议来源：filename, similar, popular
}

// 预定义标签颜色
var DefaultTagColors = []string{
	"#007bff", // 蓝色
	"#28a745", // 绿色
	"#dc3545", // 红色
	"#ffc107", // 黄色
	"#17a2b8", // 青色
	"#6f42c1", // 紫色
	"#e83e8c", // 粉色
	"#fd7e14", // 橙色
	"#20c997", // 青绿色
	"#6c757d", // 灰色
}

// GetRandomColor 获取随机标签颜色
func GetRandomColor() string {
	colors := DefaultTagColors
	return colors[time.Now().UnixNano()%int64(len(colors))]
}

// ValidateTagName 验证标签名称
func ValidateTagName(name string) error {
	if len(name) == 0 {
		return fmt.Errorf("标签名称不能为空")
	}
	if len(name) > 50 {
		return fmt.Errorf("标签名称不能超过50个字符")
	}
	// 检查是否包含特殊字符
	for _, char := range name {
		if char == ',' || char == ';' || char == '|' || char == '\n' || char == '\r' || char == '\t' {
			return fmt.Errorf("标签名称不能包含特殊字符")
		}
	}
	return nil
}

// ValidateTagColor 验证标签颜色
func ValidateTagColor(color string) error {
	if color == "" {
		return nil // 允许空值，使用默认颜色
	}

	// 检查十六进制颜色格式
	if len(color) != 7 || color[0] != '#' {
		return fmt.Errorf("颜色格式错误，应为 #RRGGBB 格式")
	}

	for i := 1; i < 7; i++ {
		char := color[i]
		if !((char >= '0' && char <= '9') || (char >= 'A' && char <= 'F') || (char >= 'a' && char <= 'f')) {
			return fmt.Errorf("颜色格式错误，应为 #RRGGBB 格式")
		}
	}

	return nil
}

// NormalizeTagName 标准化标签名称
func NormalizeTagName(name string) string {
	// 去除首尾空格
	name = strings.TrimSpace(name)
	// 转换为小写
	name = strings.ToLower(name)
	// 替换多个空格为单个空格
	re := regexp.MustCompile(`\s+`)
	name = re.ReplaceAllString(name, " ")
	return name
}

// ParseTagsFromString 从字符串解析标签
func ParseTagsFromString(tagsStr string) []string {
	if tagsStr == "" {
		return []string{}
	}

	// 支持多种分隔符
	separators := []string{",", ";", "|", "\n"}
	tags := []string{tagsStr}

	for _, sep := range separators {
		var newTags []string
		for _, tag := range tags {
			parts := strings.Split(tag, sep)
			for _, part := range parts {
				part = strings.TrimSpace(part)
				if part != "" {
					newTags = append(newTags, part)
				}
			}
		}
		tags = newTags
	}

	// 去重和标准化
	tagMap := make(map[string]bool)
	var result []string
	for _, tag := range tags {
		normalized := NormalizeTagName(tag)
		if normalized != "" && !tagMap[normalized] {
			tagMap[normalized] = true
			result = append(result, normalized)
		}
	}

	return result
}

// GenerateTagSuggestions 生成标签建议
func GenerateTagSuggestions(filename string, existingTags []string) []TagSuggestion {
	suggestions := []TagSuggestion{}

	// 基于文件名的建议
	filenameTags := extractTagsFromFilename(filename)
	for _, tag := range filenameTags {
		if !contains(existingTags, tag) {
			suggestions = append(suggestions, TagSuggestion{
				Name:       tag,
				Color:      GetRandomColor(),
				Confidence: 0.8,
				Source:     "filename",
			})
		}
	}

	// 常用标签建议
	commonTags := []string{"风景", "人物", "动物", "建筑", "食物", "旅行", "家庭", "朋友", "工作", "学习"}
	for _, tag := range commonTags {
		if !contains(existingTags, tag) && !containsSuggestion(suggestions, tag) {
			suggestions = append(suggestions, TagSuggestion{
				Name:       tag,
				Color:      GetRandomColor(),
				Confidence: 0.3,
				Source:     "popular",
			})
		}
	}

	return suggestions
}

// extractTagsFromFilename 从文件名提取标签
func extractTagsFromFilename(filename string) []string {
	// 移除扩展名
	name := strings.ToLower(filename)
	if dotIndex := strings.LastIndex(name, "."); dotIndex > 0 {
		name = name[:dotIndex]
	}

	// 常见的标签关键词
	keywords := map[string]string{
		"vacation": "度假",
		"travel":   "旅行",
		"family":   "家庭",
		"friend":   "朋友",
		"work":     "工作",
		"food":     "食物",
		"nature":   "自然",
		"city":     "城市",
		"beach":    "海滩",
		"mountain": "山",
		"sunset":   "日落",
		"sunrise":  "日出",
	}

	var tags []string
	for keyword, tag := range keywords {
		if strings.Contains(name, keyword) {
			tags = append(tags, tag)
		}
	}

	return tags
}

// contains 检查字符串数组是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// containsSuggestion 检查建议数组是否包含指定标签
func containsSuggestion(suggestions []TagSuggestion, name string) bool {
	for _, s := range suggestions {
		if s.Name == name {
			return true
		}
	}
	return false
}
