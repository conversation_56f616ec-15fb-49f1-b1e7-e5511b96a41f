package controllers

import (
	"cloudbed/internal/core/repository"
	"cloudbed/internal/core/domain"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// GetUserStorageInfo 获取当前用户的存储信息
func GetUserStorageInfo(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	storageInfo, err := dao.GetUserStorageInfo(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get storage info"})
		return
	}

	c.<PERSON>(http.StatusOK, storageInfo)
}

// GetAllUsersStorageInfo 获取所有用户的存储信息（管理员功能）
func GetAllUsersStorageInfo(c *gin.Context) {
	storageInfos, err := dao.GetAllUsersStorageInfo()
	if err != nil {
		c.<PERSON>SO<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to get storage info"})
		return
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
		"storage_info": storageInfos,
		"total":        len(storageInfos),
	})
}

// GetUserStorageInfoByID 获取指定用户的存储信息（管理员功能）
func GetUserStorageInfoByID(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	storageInfo, err := dao.GetUserStorageInfo(uint(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, storageInfo)
}

// UpdateUserStorageQuota 更新用户存储配额（管理员功能）
func UpdateUserStorageQuota(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req models.StorageQuotaUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查用户是否存在
	_, err = dao.GetUserByID(uint(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 更新存储配额
	err = dao.UpdateUserStorageQuota(uint(userID), req.StorageQuota)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update storage quota"})
		return
	}

	// 返回更新后的存储信息
	storageInfo, err := dao.GetUserStorageInfo(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get updated storage info"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":      "Storage quota updated successfully",
		"storage_info": storageInfo,
	})
}

// RecalculateUserStorage 重新计算用户存储使用量（管理员功能）
func RecalculateUserStorage(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// 检查用户是否存在
	_, err = dao.GetUserByID(uint(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 重新计算存储使用量
	err = dao.RecalculateUserStorageUsed(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to recalculate storage usage"})
		return
	}

	// 返回更新后的存储信息
	storageInfo, err := dao.GetUserStorageInfo(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get updated storage info"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":      "Storage usage recalculated successfully",
		"storage_info": storageInfo,
	})
}

// RecalculateAllUsersStorage 重新计算所有用户的存储使用量（管理员功能）
func RecalculateAllUsersStorage(c *gin.Context) {
	// 获取所有用户
	users, err := dao.GetAllUsersWithRole()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get users"})
		return
	}

	var successCount, failCount int
	for _, user := range users {
		err := dao.RecalculateUserStorageUsed(user.ID)
		if err != nil {
			failCount++
		} else {
			successCount++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Storage recalculation completed",
		"success_count": successCount,
		"fail_count":    failCount,
		"total_users":   len(users),
	})
}

// CheckStorageQuota 检查用户存储配额（内部使用）
func CheckStorageQuota(userID uint, requiredSize int64) (bool, error) {
	return dao.CheckStorageQuota(userID, requiredSize)
}

// UpdateStorageUsage 更新用户存储使用量（内部使用）
func UpdateStorageUsage(userID uint, sizeChange int64) error {
	if sizeChange > 0 {
		return dao.IncrementUserStorageUsed(userID, sizeChange)
	} else if sizeChange < 0 {
		return dao.DecrementUserStorageUsed(userID, -sizeChange)
	}
	return nil
}
