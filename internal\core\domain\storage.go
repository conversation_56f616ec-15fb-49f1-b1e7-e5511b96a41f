package models

import (
	"time"
)

// StorageProvider 存储提供商类型
type StorageProvider string

const (
	StorageProviderLocal     StorageProvider = "local"      // 本地存储
	StorageProviderAliOSS    StorageProvider = "ali_oss"    // 阿里云OSS
	StorageProviderTencentCOS StorageProvider = "tencent_cos" // 腾讯云COS
	StorageProviderQiniuKodo StorageProvider = "qiniu_kodo" // 七牛云Kodo
	StorageProviderAWSS3     StorageProvider = "aws_s3"     // AWS S3
	StorageProviderFTP       StorageProvider = "ftp"        // FTP服务器
	StorageProviderSFTP      StorageProvider = "sftp"       // SFTP服务器
)

// StorageConfig 存储配置模型
type StorageConfig struct {
	ID          uint            `json:"id" gorm:"primaryKey"`
	Name        string          `json:"name" gorm:"type:varchar(100);not null"`                    // 配置名称
	Provider    StorageProvider `json:"provider" gorm:"type:varchar(50);not null"`                 // 存储提供商
	IsDefault   bool            `json:"is_default" gorm:"default:false"`                           // 是否为默认配置
	IsEnabled   bool            `json:"is_enabled" gorm:"default:true"`                            // 是否启用
	AccessURL   string          `json:"access_url" gorm:"type:varchar(500)"`                       // 访问域名
	StoragePath string          `json:"storage_path" gorm:"type:varchar(500)"`                     // 存储路径
	Config      string          `json:"config" gorm:"type:text"`                                   // JSON配置信息
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

// StorageConfigDetail 存储配置详情（包含解析后的配置）
type StorageConfigDetail struct {
	StorageConfig
	ParsedConfig interface{} `json:"parsed_config"` // 解析后的配置
}

// LocalStorageConfig 本地存储配置
type LocalStorageConfig struct {
	UploadPath string `json:"upload_path"` // 上传路径
	MaxSize    int64  `json:"max_size"`    // 最大文件大小（字节）
}

// AliOSSConfig 阿里云OSS配置
type AliOSSConfig struct {
	AccessKeyID     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
	Endpoint        string `json:"endpoint"`
	BucketName      string `json:"bucket_name"`
	Region          string `json:"region"`
	PathPrefix      string `json:"path_prefix"` // 路径前缀
}

// TencentCOSConfig 腾讯云COS配置
type TencentCOSConfig struct {
	SecretID   string `json:"secret_id"`
	SecretKey  string `json:"secret_key"`
	Region     string `json:"region"`
	BucketName string `json:"bucket_name"`
	PathPrefix string `json:"path_prefix"`
}

// QiniuKodoConfig 七牛云Kodo配置
type QiniuKodoConfig struct {
	AccessKey  string `json:"access_key"`
	SecretKey  string `json:"secret_key"`
	BucketName string `json:"bucket_name"`
	Domain     string `json:"domain"`
	Zone       string `json:"zone"`
	PathPrefix string `json:"path_prefix"`
}

// AWSS3Config AWS S3配置
type AWSS3Config struct {
	AccessKeyID     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
	Region          string `json:"region"`
	BucketName      string `json:"bucket_name"`
	PathPrefix      string `json:"path_prefix"`
}

// FTPConfig FTP配置
type FTPConfig struct {
	Host       string `json:"host"`
	Port       int    `json:"port"`
	Username   string `json:"username"`
	Password   string `json:"password"`
	RemotePath string `json:"remote_path"` // 远程路径
	PassiveMode bool  `json:"passive_mode"` // 被动模式
}

// SFTPConfig SFTP配置
type SFTPConfig struct {
	Host       string `json:"host"`
	Port       int    `json:"port"`
	Username   string `json:"username"`
	Password   string `json:"password"`
	PrivateKey string `json:"private_key"` // 私钥内容
	RemotePath string `json:"remote_path"` // 远程路径
}

// StorageConfigRequest 存储配置请求
type StorageConfigRequest struct {
	Name        string          `json:"name" binding:"required"`
	Provider    StorageProvider `json:"provider" binding:"required"`
	IsDefault   bool            `json:"is_default"`
	IsEnabled   bool            `json:"is_enabled"`
	AccessURL   string          `json:"access_url"`
	StoragePath string          `json:"storage_path"`
	Config      interface{}     `json:"config" binding:"required"` // 具体配置
}

// StorageConfigResponse 存储配置响应
type StorageConfigResponse struct {
	ID          uint            `json:"id"`
	Name        string          `json:"name"`
	Provider    StorageProvider `json:"provider"`
	IsDefault   bool            `json:"is_default"`
	IsEnabled   bool            `json:"is_enabled"`
	AccessURL   string          `json:"access_url"`
	StoragePath string          `json:"storage_path"`
	Config      interface{}     `json:"config"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

// StorageConfigListResponse 存储配置列表响应
type StorageConfigListResponse struct {
	Configs []StorageConfigResponse `json:"configs"`
	Total   int64                   `json:"total"`
}

// StorageTestRequest 存储测试请求
type StorageTestRequest struct {
	Provider StorageProvider `json:"provider" binding:"required"`
	Config   interface{}     `json:"config" binding:"required"`
}

// StorageTestResponse 存储测试响应
type StorageTestResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// StorageStats 存储统计信息
type StorageStats struct {
	TotalFiles int64 `json:"total_files"`
	TotalSize  int64 `json:"total_size"`
	UsedQuota  int64 `json:"used_quota"`
	TotalQuota int64 `json:"total_quota"`
}
