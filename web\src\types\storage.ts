// 存储配额相关类型定义

export interface StorageQuotaInfo {
  user_id: number;
  username: string;
  email: string;
  storage_quota: number;     // 存储配额（字节）
  storage_used: number;      // 已使用存储（字节）
  usage_percent: number;     // 使用百分比
  quota_gb: number;          // 配额（GB）
  used_gb: number;           // 已使用（GB）
  available_gb: number;      // 可用（GB）
}

export interface StorageQuotaUpdateRequest {
  storage_quota: number;     // 存储配额（字节）
}

export interface StorageQuotaResponse {
  storage_info: StorageQuotaInfo[];
  total: number;
}

export interface StorageQuotaUpdateResponse {
  message: string;
  storage_info: StorageQuotaInfo;
}

export interface StorageRecalculateResponse {
  message: string;
  success_count?: number;
  fail_count?: number;
  total_users?: number;
  storage_info?: StorageQuotaInfo;
}

// 存储配额预设值（GB）
export const STORAGE_QUOTA_PRESETS = [
  { label: '100MB', value: 100 * 1024 * 1024 },
  { label: '500MB', value: 500 * 1024 * 1024 },
  { label: '1GB', value: 1 * 1024 * 1024 * 1024 },
  { label: '2GB', value: 2 * 1024 * 1024 * 1024 },
  { label: '5GB', value: 5 * 1024 * 1024 * 1024 },
  { label: '10GB', value: 10 * 1024 * 1024 * 1024 },
  { label: '20GB', value: 20 * 1024 * 1024 * 1024 },
  { label: '50GB', value: 50 * 1024 * 1024 * 1024 },
  { label: '100GB', value: 100 * 1024 * 1024 * 1024 },
];

// 工具函数
export const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

export const parseStorageSize = (sizeStr: string): number => {
  const units: { [key: string]: number } = {
    'B': 1,
    'KB': 1024,
    'MB': 1024 * 1024,
    'GB': 1024 * 1024 * 1024,
    'TB': 1024 * 1024 * 1024 * 1024,
  };

  const match = sizeStr.trim().match(/^(\d+(?:\.\d+)?)\s*([A-Z]{1,2})$/i);
  if (!match) return 0;

  const value = parseFloat(match[1]);
  const unit = match[2].toUpperCase();

  return Math.floor(value * (units[unit] || 1));
};
