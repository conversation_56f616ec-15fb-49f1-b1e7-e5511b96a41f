<template>
  <div class="users-container">
    <div class="page-header">
      <h2><i class="bi bi-people"></i> 用户管理</h2>
      <p class="page-subtitle">管理系统用户账号和权限</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="search-filters">
        <div class="search-box">
          <i class="bi bi-search"></i>
          <input 
            type="text" 
            placeholder="搜索用户邮箱..." 
            v-model="searchEmail"
            @input="handleSearch"
          />
        </div>
        <select v-model="filterRole" @change="handleSearch" class="filter-select">
          <option value="">所有角色</option>
          <option v-for="role in roles" :key="role.id" :value="role.id">
            {{ role.display_name }}
          </option>
        </select>
        <select v-model="filterStatus" @change="handleSearch" class="filter-select">
          <option value="">所有状态</option>
          <option value="active">活跃</option>
          <option value="inactive">非活跃</option>
          <option value="suspended">暂停</option>
        </select>
      </div>
      <button class="btn btn-primary" @click="showCreateModal = true">
        <i class="bi bi-plus-circle"></i> 创建用户
      </button>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info" style="background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px;">
      <h4>调试信息</h4>
      <p><strong>Loading:</strong> {{ loading }}</p>
      <p><strong>Error:</strong> {{ error }}</p>
      <p><strong>Users count:</strong> {{ users.length }}</p>
      <p><strong>Show Debug:</strong> {{ showDebug }}</p>
      <button @click="fetchUsers" class="btn btn-primary">手动刷新</button>
      <p><strong>Raw users data:</strong></p>
      <pre style="background: white; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;">{{ JSON.stringify(users, null, 2) }}</pre>
    </div>

    <!-- 用户列表 -->
    <div class="users-table-container">
      <table class="users-table">
        <thead>
          <tr>
            <th>用户</th>
            <th>角色</th>
            <th>状态</th>
            <th>存储使用</th>
            <th>图片数量</th>
            <th>相册数量</th>
            <th>最后登录</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <!-- 加载状态 -->
          <tr v-if="loading">
            <td colspan="8" class="text-center">
              <div class="loading-spinner">
                <i class="bi bi-arrow-clockwise spinning"></i>
                <span>加载中...</span>
              </div>
            </td>
          </tr>

          <!-- 错误状态 -->
          <tr v-else-if="error">
            <td colspan="8" class="text-center">
              <div class="error-state" style="padding: 20px; color: #dc3545;">
                <i class="bi bi-exclamation-triangle"></i>
                <span>{{ error }}</span>
                <button class="btn btn-sm btn-primary" @click="fetchUsers" style="margin-left: 10px;">重试</button>
              </div>
            </td>
          </tr>

          <!-- 无数据状态 -->
          <tr v-else-if="users.length === 0">
            <td colspan="8" class="text-center">
              <div class="no-data">
                <i class="bi bi-inbox"></i>
                <span>暂无用户数据</span>
              </div>
            </td>
          </tr>

          <!-- 用户数据 - 简化版本用于调试 -->
          <tr v-else v-for="(user, index) in users" :key="index">
            <td>
              <div class="user-info">
                <div class="user-avatar">
                  <i class="bi bi-person-circle"></i>
                </div>
                <div class="user-details">
                  <div class="user-email">{{ getUserEmail(user) }}</div>
                  <div class="user-id">ID: {{ getUserId(user) }}</div>
                </div>
              </div>
            </td>
            <td>
              <span class="role-badge">
                {{ getUserRole(user) }}
              </span>
            </td>
            <td>
              <span class="status-badge">
                {{ getUserStatus(user) }}
              </span>
            </td>
            <td>
              <div class="storage-info">
                <div class="storage-used">{{ formatBytes(getUserStorageUsed(user)) }}</div>
                <div class="storage-quota">/ {{ formatBytes(getUserStorageQuota(user)) }}</div>
              </div>
            </td>
            <td>
              <span class="count-badge">{{ user.image_count || 0 }}</span>
            </td>
            <td>
              <span class="count-badge">{{ user.album_count || 0 }}</span>
            </td>
            <td>
              <span class="last-login">
                {{ getUserLastLogin(user) }}
              </span>
            </td>
            <td>
              <div class="action-buttons">
                <button 
                  class="btn-icon" 
                  @click="editUser(user)"
                  title="编辑用户"
                >
                  <i class="bi bi-pencil"></i>
                </button>
                <button 
                  class="btn-icon" 
                  @click="resetPassword(user)"
                  title="重置密码"
                >
                  <i class="bi bi-key"></i>
                </button>
                <button 
                  class="btn-icon" 
                  @click="toggleUserStatus(user)"
                  :title="user.status === 'active' ? '暂停用户' : '激活用户'"
                >
                  <i :class="user.status === 'active' ? 'bi bi-pause' : 'bi bi-play'"></i>
                </button>
                <button 
                  class="btn-icon btn-danger" 
                  @click="deleteUser(user)"
                  title="删除用户"
                  v-if="user.role.name !== 'super_admin'"
                >
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <div class="pagination-info">
        显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalUsers) }} 
        共 {{ totalUsers }} 个用户
      </div>
      <div class="pagination">
        <button 
          class="btn btn-sm" 
          @click="changePage(currentPage - 1)"
          :disabled="currentPage <= 1"
        >
          上一页
        </button>
        <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
        <button 
          class="btn btn-sm" 
          @click="changePage(currentPage + 1)"
          :disabled="currentPage >= totalPages"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 创建/编辑用户模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModals">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ showCreateModal ? '创建用户' : '编辑用户' }}</h3>
          <button class="close-btn" @click="closeModals">
            <i class="bi bi-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitUserForm">
            <div class="form-group">
              <label for="email">邮箱</label>
              <input 
                id="email" 
                v-model="userForm.email" 
                type="email" 
                required 
                :disabled="loading"
              />
            </div>
            
            <div class="form-group" v-if="showCreateModal">
              <label for="password">密码</label>
              <input 
                id="password" 
                v-model="userForm.password" 
                type="password" 
                required 
                :disabled="loading"
                minlength="6"
              />
            </div>
            
            <div class="form-group">
              <label for="role">角色</label>
              <select id="role" v-model="userForm.role_id" required :disabled="loading">
                <option value="">请选择角色</option>
                <option v-for="role in roles" :key="role.id" :value="role.id">
                  {{ role.display_name }}
                </option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="status">状态</label>
              <select id="status" v-model="userForm.status" required :disabled="loading">
                <option value="active">活跃</option>
                <option value="inactive">非活跃</option>
                <option value="suspended">暂停</option>
              </select>
            </div>
            
            <div class="form-actions">
              <button type="button" class="btn btn-secondary" @click="closeModals" :disabled="loading">
                取消
              </button>
              <button type="submit" class="btn btn-primary" :disabled="loading">
                {{ loading ? '处理中...' : (showCreateModal ? '创建' : '更新') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 通知 -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import api from '../../services/api';
import { formatBytes } from '../../types/storage';
import { useDialog } from '../../utils/dialog';

// 响应式数据
const users = ref<any[]>([]);
const roles = ref<any[]>([]);
const loading = ref(false);
const { confirmDelete } = useDialog();
const showCreateModal = ref(false);
const showEditModal = ref(false);
const editingUser = ref<any>(null);

// 搜索和过滤
const searchEmail = ref('');
const filterRole = ref('');
const filterStatus = ref('');

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const totalUsers = ref(0);
const totalPages = ref(0);

// 错误处理和调试
const error = ref('');
const showDebug = ref(true);

// 表单数据
const userForm = reactive({
  email: '',
  password: '',
  role_id: '',
  status: 'active'
});

// 通知
const notification = reactive({
  show: false,
  message: '',
  type: 'success'
});

// 方法
const fetchUsers = async () => {
  loading.value = true;
  error.value = '';
  try {
    console.log('Fetching users with stats...');
    const response = await api.getAllUsersWithStats();
    console.log('Users response:', response);
    users.value = response.users || [];
    console.log('Users loaded:', users.value.length);
  } catch (err: any) {
    console.error('Error fetching users:', err);
    error.value = err.response?.data?.error || err.message || '加载用户列表失败';
    showNotification('加载用户列表失败', 'error');
  } finally {
    loading.value = false;
  }
};

const fetchRoles = async () => {
  // TODO: 实现获取角色列表的API调用
  console.log('Fetching roles...');
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchUsers();
};

const changePage = (page: number) => {
  currentPage.value = page;
  fetchUsers();
};

const editUser = (user: any) => {
  editingUser.value = user;
  userForm.email = user.email;
  userForm.role_id = user.role.id;
  userForm.status = user.status;
  showEditModal.value = true;
};

const submitUserForm = async () => {
  // TODO: 实现创建/更新用户的API调用
  console.log('Submitting user form...');
};

const resetPassword = async (user: any) => {
  // TODO: 实现重置密码的API调用
  console.log('Resetting password for user:', user.id);
};

const toggleUserStatus = async (user: any) => {
  // TODO: 实现切换用户状态的API调用
  console.log('Toggling status for user:', user.id);
};

const deleteUser = async (user: any) => {
  const confirmed = await confirmDelete(user.email, {
    details: '删除用户后，该用户的所有数据将被永久删除。'
  });

  if (confirmed) {
    // TODO: 实现删除用户的API调用
    console.log('Deleting user:', user.id);
  }
};

const closeModals = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  editingUser.value = null;
  Object.assign(userForm, {
    email: '',
    password: '',
    role_id: '',
    status: 'active'
  });
};

// 工具函数 - 暂时注释掉未使用的函数
// const getRoleClass = (roleName: string) => {
//   const classes: { [key: string]: string } = {
//     'super_admin': 'role-super-admin',
//     'admin': 'role-admin',
//     'user': 'role-user',
//     'guest': 'role-guest'
//   };
//   return classes[roleName] || 'role-default';
// };

// const getStatusClass = (status: string) => {
//   const classes: { [key: string]: string } = {
//     'active': 'status-active',
//     'inactive': 'status-inactive',
//     'suspended': 'status-suspended'
//   };
//   return classes[status] || 'status-default';
// };

// const getStatusText = (status: string) => {
//   const texts: { [key: string]: string } = {
//     'active': '活跃',
//     'inactive': '非活跃',
//     'suspended': '暂停'
//   };
//   return texts[status] || status;
// };

// const formatDate = (dateString: string) => {
//   return new Date(dateString).toLocaleString('zh-CN');
// };

const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
  notification.message = message;
  notification.type = type;
  notification.show = true;
  setTimeout(() => {
    notification.show = false;
  }, 3000);
};

// 存储相关工具函数 - 暂时注释掉未使用的函数
// const getStoragePercentage = (user: any) => {
//   if (user.storage_quota === 0) return 0;
//   return Math.min((user.storage_used / user.storage_quota) * 100, 100);
// };

// const getStorageClass = (user: any) => {
//   const percentage = getStoragePercentage(user);
//   if (percentage >= 90) return 'storage-critical';
//   if (percentage >= 80) return 'storage-warning';
//   return 'storage-normal';
// };

// 简化的工具函数用于调试
const getUserId = (user: any) => {
  return user.user?.id || user.id || 'N/A';
};

const getUserEmail = (user: any) => {
  return user.user?.email || user.email || 'N/A';
};

const getUserRole = (user: any) => {
  return user.user?.role?.display_name || user.role?.display_name || 'N/A';
};

const getUserStatus = (user: any) => {
  return user.user?.status || user.status || 'N/A';
};

const getUserStorageUsed = (user: any) => {
  return user.user?.storage_used || user.storage_used || 0;
};

const getUserStorageQuota = (user: any) => {
  return user.user?.storage_quota || user.storage_quota || 0;
};

const getUserLastLogin = (user: any) => {
  const lastLogin = user.user?.last_login_at || user.last_login_at;
  return lastLogin ? new Date(lastLogin).toLocaleString('zh-CN') : '从未登录';
};

// 生命周期
onMounted(() => {
  fetchUsers();
  fetchRoles();
});
</script>

<style scoped>
.users-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-subtitle {
  color: #666;
  margin: 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
}

.search-filters {
  display: flex;
  gap: 15px;
  align-items: center;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: #666;
}

.search-box input {
  padding: 8px 12px 8px 35px;
  border: 1px solid #ddd;
  border-radius: 6px;
  width: 250px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
}

.users-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  background: #f8f9fa;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #dee2e6;
}

.users-table td {
  padding: 15px;
  border-bottom: 1px solid #f1f3f4;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar i {
  font-size: 32px;
  color: #6c757d;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-email {
  font-weight: 500;
  color: #333;
}

.user-id {
  font-size: 12px;
  color: #666;
}

.role-badge, .status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.role-super-admin { background: #dc3545; color: white; }
.role-admin { background: #fd7e14; color: white; }
.role-user { background: #198754; color: white; }
.role-guest { background: #6c757d; color: white; }

.status-active { background: #d1edff; color: #0969da; }
.status-inactive { background: #f1f3f4; color: #656d76; }
.status-suspended { background: #ffebe9; color: #cf222e; }

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-icon {
  padding: 6px 8px;
  border: none;
  background: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon:hover {
  background: #e9ecef;
}

.btn-icon.btn-danger:hover {
  background: #dc3545;
  color: white;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:hover:not(:disabled) {
  background: #f8f9fa;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 6px;
  color: white;
  z-index: 1001;
}

.notification.success {
  background: #198754;
}

.notification.error {
  background: #dc3545;
}

/* 存储信息样式 */
.storage-info {
  min-width: 120px;
}

.storage-used, .storage-quota {
  font-size: 12px;
  color: #666;
}

.storage-used {
  font-weight: 600;
  color: #333;
}

.storage-progress {
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-fill.storage-normal {
  background: #28a745;
}

.progress-fill.storage-warning {
  background: #ffc107;
}

.progress-fill.storage-critical {
  background: #dc3545;
}

.progress-text {
  font-size: 10px;
  color: #666;
  min-width: 35px;
}

.count-badge {
  display: inline-block;
  padding: 2px 8px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: #495057;
}

/* 加载和空状态样式 */
.loading-spinner, .no-data {
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.loading-spinner i, .no-data i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.text-center {
  text-align: center;
}

.text-muted {
  color: #6c757d;
}
</style>
