package controllers

import (
	"strconv"

	"cloudbed/internal/core/repository"
	"cloudbed/pkg/ai"
	"cloudbed/pkg/ocr"
	"cloudbed/pkg/response"
	"cloudbed/pkg/search"

	"github.com/gin-gonic/gin"
)

// SimilaritySearchRequest 相似度搜索请求
type SimilaritySearchRequest struct {
	ImageID     uint    `json:"image_id" binding:"required"`
	Threshold   float64 `json:"threshold"`   // 相似度阈值 0-1
	MaxResults  int     `json:"max_results"` // 最大结果数
	SearchType  string  `json:"search_type"` // color, edge, texture, hash, all
}

// TextSearchRequest 文本搜索请求
type TextSearchRequest struct {
	Query      string `json:"query" binding:"required"`
	Language   string `json:"language"`   // 语言过滤
	Confidence float64 `json:"confidence"` // 最小置信度
}

// DuplicateDetectionRequest 重复检测请求
type DuplicateDetectionRequest struct {
	Threshold   float64 `json:"threshold"`   // 相似度阈值
	GroupSize   int     `json:"group_size"`  // 最小组大小
	Algorithm   string  `json:"algorithm"`   // hash, feature, hybrid
}

// AutoTagRequest 自动标签请求
type AutoTagRequest struct {
	ImageID     uint    `json:"image_id" binding:"required"`
	Confidence  float64 `json:"confidence"`  // 最小置信度
	MaxTags     int     `json:"max_tags"`    // 最大标签数
	Categories  []string `json:"categories"` // 标签分类过滤
}

// FindSimilarImages 查找相似图片
func FindSimilarImages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req SimilaritySearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 设置默认值
	if req.Threshold == 0 {
		req.Threshold = 0.7
	}
	if req.MaxResults == 0 {
		req.MaxResults = 20
	}

	// 验证图片权限
	image, err := dao.GetImageByIDAndUserID(req.ImageID, userID.(uint))
	if err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 创建高级搜索引擎
	searchEngine := search.NewAdvancedSearchEngine()

	// 获取用户的所有图片
	userImages, err := dao.GetImagesByUserID(userID.(uint))
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取用户图片失败", err.Error())
		return
	}

	// 提取所有图片的特征
	for _, img := range userImages {
		imagePath := "./uploads/" + img.Name
		_, err := searchEngine.ExtractFeatures(img.ID, imagePath)
		if err != nil {
			// 记录错误但继续处理其他图片
			continue
		}
	}

	// 查找相似图片
	results, err := searchEngine.FindSimilarImages(req.ImageID, req.Threshold, req.MaxResults)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "相似度搜索失败", err.Error())
		return
	}

	// 获取相似图片的详细信息
	var similarImages []map[string]interface{}
	for _, result := range results {
		if similarImg, err := dao.GetImageByID(result.ImageID); err == nil {
			similarImages = append(similarImages, map[string]interface{}{
				"image":      similarImg,
				"similarity": result.Similarity,
				"match_type": result.MatchType,
			})
		}
	}

	response.Success(c, gin.H{
		"target_image":   image,
		"similar_images": similarImages,
		"total_found":    len(similarImages),
		"threshold":      req.Threshold,
		"search_type":    req.SearchType,
	})
}

// SearchTextInImages 在图片中搜索文本
func SearchTextInImages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req TextSearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 设置默认值
	if req.Confidence == 0 {
		req.Confidence = 0.5
	}

	// 获取用户的所有图片
	userImages, err := dao.GetImagesByUserID(userID.(uint))
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取用户图片失败", err.Error())
		return
	}

	// 创建OCR引擎
	ocrEngine := ocr.NewOCREngine()

	// 构建图片路径列表
	var imagePaths []string
	imageMap := make(map[string]uint)
	for _, img := range userImages {
		imagePath := "./uploads/" + img.Name
		imagePaths = append(imagePaths, imagePath)
		imageMap[imagePath] = img.ID
	}

	// 执行文本搜索
	searchResults, err := ocrEngine.SearchTextInImages(req.Query, imagePaths)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "文本搜索失败", err.Error())
		return
	}

	// 过滤结果并获取图片详细信息
	var filteredResults []map[string]interface{}
	for _, result := range searchResults {
		if result.Confidence >= req.Confidence {
			imageID := imageMap[result.ImagePath]
			if image, err := dao.GetImageByID(imageID); err == nil {
				filteredResults = append(filteredResults, map[string]interface{}{
					"image":      image,
					"text":       result.Text,
					"matches":    result.Matches,
					"confidence": result.Confidence,
				})
			}
		}
	}

	response.Success(c, gin.H{
		"query":         req.Query,
		"results":       filteredResults,
		"total_found":   len(filteredResults),
		"min_confidence": req.Confidence,
	})
}

// DetectDuplicateImages 检测重复图片
func DetectDuplicateImages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req DuplicateDetectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 设置默认值
	if req.Threshold == 0 {
		req.Threshold = 0.9
	}
	if req.GroupSize == 0 {
		req.GroupSize = 2
	}
	if req.Algorithm == "" {
		req.Algorithm = "hash"
	}

	// 获取用户的所有图片
	userImages, err := dao.GetImagesByUserID(userID.(uint))
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取用户图片失败", err.Error())
		return
	}

	// 创建高级搜索引擎
	searchEngine := search.NewAdvancedSearchEngine()

	// 提取所有图片的特征
	for _, img := range userImages {
		imagePath := "./uploads/" + img.Name
		_, err := searchEngine.ExtractFeatures(img.ID, imagePath)
		if err != nil {
			continue
		}
	}

	// 检测重复图片
	duplicateGroups, err := searchEngine.DetectDuplicates(req.Threshold)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "重复检测失败", err.Error())
		return
	}

	// 过滤并获取图片详细信息
	var filteredGroups []map[string]interface{}
	for _, group := range duplicateGroups {
		if len(group.Images) >= req.GroupSize {
			var groupImages []interface{}
			for _, imageID := range group.Images {
				if image, err := dao.GetImageByID(imageID); err == nil {
					groupImages = append(groupImages, image)
				}
			}

			if len(groupImages) >= req.GroupSize {
				filteredGroups = append(filteredGroups, map[string]interface{}{
					"group_id":   group.GroupID,
					"images":     groupImages,
					"similarity": group.Similarity,
					"type":       group.Type,
					"count":      len(groupImages),
				})
			}
		}
	}

	response.Success(c, gin.H{
		"duplicate_groups": filteredGroups,
		"total_groups":     len(filteredGroups),
		"threshold":        req.Threshold,
		"algorithm":        req.Algorithm,
	})
}

// GenerateAutoTags 生成自动标签
func GenerateAutoTags(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req AutoTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	// 设置默认值
	if req.Confidence == 0 {
		req.Confidence = 0.5
	}
	if req.MaxTags == 0 {
		req.MaxTags = 10
	}

	// 验证图片权限
	image, err := dao.GetImageByIDAndUserID(req.ImageID, userID.(uint))
	if err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 创建自动标签器
	autoTagger := ai.NewAutoTagger()

	// 分析图片并生成标签建议
	imagePath := "./uploads/" + image.Name
	suggestions, analysis, err := autoTagger.AnalyzeImage(imagePath)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "图片分析失败", err.Error())
		return
	}

	// 过滤标签建议
	var filteredSuggestions []ai.TagSuggestion
	for _, suggestion := range suggestions {
		if suggestion.Confidence >= req.Confidence {
			// 分类过滤
			if len(req.Categories) == 0 || contains(req.Categories, suggestion.Category) {
				filteredSuggestions = append(filteredSuggestions, suggestion)
			}
		}
	}

	// 限制数量
	if len(filteredSuggestions) > req.MaxTags {
		filteredSuggestions = filteredSuggestions[:req.MaxTags]
	}

	response.Success(c, gin.H{
		"image":       image,
		"suggestions": filteredSuggestions,
		"analysis":    analysis,
		"total_suggestions": len(filteredSuggestions),
		"min_confidence": req.Confidence,
	})
}

// ExtractImageText 提取图片文本
func ExtractImageText(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	imageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "图片ID格式错误")
		return
	}

	// 验证图片权限
	image, err := dao.GetImageByIDAndUserID(uint(imageID), userID.(uint))
	if err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 创建OCR引擎
	ocrEngine := ocr.NewOCREngine()

	// 提取文本
	imagePath := "./uploads/" + image.Name
	ocrResult, err := ocrEngine.RecognizeText(imagePath)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "文本提取失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"image":      image,
		"ocr_result": ocrResult,
	})
}

// GetImageFeatures 获取图片特征
func GetImageFeatures(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	imageID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c, "图片ID格式错误")
		return
	}

	// 验证图片权限
	image, err := dao.GetImageByIDAndUserID(uint(imageID), userID.(uint))
	if err != nil {
		response.NotFound(c, "图片不存在")
		return
	}

	// 创建高级搜索引擎
	searchEngine := search.NewAdvancedSearchEngine()

	// 提取图片特征
	imagePath := "./uploads/" + image.Name
	features, err := searchEngine.ExtractFeatures(uint(imageID), imagePath)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "特征提取失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"image":    image,
		"features": features,
	})
}

// BatchAnalyzeImages 批量分析图片
func BatchAnalyzeImages(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req struct {
		ImageIDs   []uint   `json:"image_ids" binding:"required"`
		Operations []string `json:"operations"` // features, ocr, auto_tags
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "请求参数错误", err.Error())
		return
	}

	if len(req.ImageIDs) > 50 {
		response.BadRequest(c, "单次最多分析50张图片")
		return
	}

	// 设置默认操作
	if len(req.Operations) == 0 {
		req.Operations = []string{"features"}
	}

	var results []map[string]interface{}
	
	// 创建分析器
	searchEngine := search.NewAdvancedSearchEngine()
	ocrEngine := ocr.NewOCREngine()
	autoTagger := ai.NewAutoTagger()

	for _, imageID := range req.ImageIDs {
		// 验证图片权限
		image, err := dao.GetImageByIDAndUserID(imageID, userID.(uint))
		if err != nil {
			continue
		}

		imagePath := "./uploads/" + image.Name
		result := map[string]interface{}{
			"image_id": imageID,
			"image":    image,
		}

		// 执行请求的操作
		for _, operation := range req.Operations {
			switch operation {
			case "features":
				if features, err := searchEngine.ExtractFeatures(imageID, imagePath); err == nil {
					result["features"] = features
				}
			case "ocr":
				if ocrResult, err := ocrEngine.RecognizeText(imagePath); err == nil {
					result["ocr"] = ocrResult
				}
			case "auto_tags":
				if suggestions, analysis, err := autoTagger.AnalyzeImage(imagePath); err == nil {
					result["auto_tags"] = suggestions
					result["analysis"] = analysis
				}
			}
		}

		results = append(results, result)
	}

	response.Success(c, gin.H{
		"results":    results,
		"total":      len(results),
		"operations": req.Operations,
	})
}

// 辅助函数
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
