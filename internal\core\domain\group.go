package models

import (
	"time"
)

// Group 表示用户组模型
type Group struct {
	ID          uint   `json:"id" gorm:"primary_key"`
	Name        string `json:"name" gorm:"type:varchar(50);unique;not null"`   // 组名称（英文）
	DisplayName string `json:"display_name" gorm:"type:varchar(100);not null"` // 显示名称（中文）
	Description string `json:"description" gorm:"type:varchar(500)"`           // 组描述
	IsSystem    bool   `json:"is_system" gorm:"default:false"`                 // 是否为系统组
	IsActive    bool   `json:"is_active" gorm:"default:true"`                  // 是否激活
	IsDefault   bool   `json:"is_default" gorm:"default:false"`                // 是否为默认组

	// 上传限制配置
	MaxFileSize       int64 `json:"max_file_size" gorm:"default:10240"`     // 最大文件大小(KB)
	ConcurrentUploads int   `json:"concurrent_uploads" gorm:"default:3"`    // 并发上传限制
	UploadLimitMinute int   `json:"upload_limit_minute" gorm:"default:10"`  // 每分钟上传限制
	UploadLimitHour   int   `json:"upload_limit_hour" gorm:"default:100"`   // 每小时上传限制
	UploadLimitDay    int   `json:"upload_limit_day" gorm:"default:500"`    // 每天上传限制
	UploadLimitWeek   int   `json:"upload_limit_week" gorm:"default:2000"`  // 每周上传限制
	UploadLimitMonth  int   `json:"upload_limit_month" gorm:"default:5000"` // 每月上传限制

	// 文件命名和路径配置
	PathNamingRule string `json:"path_naming_rule" gorm:"default:{Y}/{m}/{d}"` // 路径命名规则
	FileNamingRule string `json:"file_naming_rule" gorm:"default:{uniqid}"`    // 文件命名规则

	// 图片处理配置
	ImageQuality      int    `json:"image_quality" gorm:"default:85"`                          // 图片保存质量(1-100)
	ImageFormat       string `json:"image_format" gorm:"default:original"`                     // 图片转换格式
	AllowedImageTypes string `json:"allowed_image_types" gorm:"default:jpg,jpeg,png,gif,webp"` // 允许的图片类型

	Permissions []Permission `json:"permissions" gorm:"many2many:group_permissions"` // 组权限
	Users       []User       `json:"users" gorm:"many2many:user_groups"`             // 组成员
	CreatedBy   uint         `json:"created_by"`                                     // 创建者ID
	Creator     User         `json:"creator" gorm:"foreignKey:CreatedBy"`            // 创建者
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
}

// GroupPermission 表示组权限关联模型
type GroupPermission struct {
	ID           uint       `json:"id" gorm:"primary_key"`
	GroupID      uint       `json:"group_id" gorm:"not null"`
	Group        Group      `json:"group" gorm:"foreignKey:GroupID"`
	PermissionID uint       `json:"permission_id" gorm:"not null"`
	Permission   Permission `json:"permission" gorm:"foreignKey:PermissionID"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// UserGroup 表示用户组关联模型
type UserGroup struct {
	ID        uint      `json:"id" gorm:"primary_key"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	User      User      `json:"user" gorm:"foreignKey:UserID"`
	GroupID   uint      `json:"group_id" gorm:"not null"`
	Group     Group     `json:"group" gorm:"foreignKey:GroupID"`
	JoinedAt  time.Time `json:"joined_at" gorm:"default:CURRENT_TIMESTAMP"` // 加入时间
	AddedBy   uint      `json:"added_by"`                                   // 添加者ID
	Adder     User      `json:"adder" gorm:"foreignKey:AddedBy"`            // 添加者
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// GroupStats 表示组统计信息
type GroupStats struct {
	GroupID     uint      `json:"group_id"`
	GroupName   string    `json:"group_name"`
	MemberCount int64     `json:"member_count"`
	CreatedAt   time.Time `json:"created_at"`
}

// TableName 设置表名
func (Group) TableName() string {
	return "groups"
}

func (GroupPermission) TableName() string {
	return "group_permissions"
}

func (UserGroup) TableName() string {
	return "user_groups"
}

// GroupCreateRequest 表示创建用户组的请求
type GroupCreateRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=50"`
	DisplayName string `json:"display_name" binding:"required,min=1,max=100"`
	Description string `json:"description" binding:"max=500"`
	IsActive    *bool  `json:"is_active"`
	IsDefault   *bool  `json:"is_default"`

	// 上传限制配置
	MaxFileSize       *int64 `json:"max_file_size" binding:"omitempty,min=1,max=1048576"`     // 最大文件大小(KB) 1KB-1GB
	ConcurrentUploads *int   `json:"concurrent_uploads" binding:"omitempty,min=1,max=10"`     // 并发上传限制 1-10
	UploadLimitMinute *int   `json:"upload_limit_minute" binding:"omitempty,min=0,max=1000"`  // 每分钟上传限制
	UploadLimitHour   *int   `json:"upload_limit_hour" binding:"omitempty,min=0,max=10000"`   // 每小时上传限制
	UploadLimitDay    *int   `json:"upload_limit_day" binding:"omitempty,min=0,max=50000"`    // 每天上传限制
	UploadLimitWeek   *int   `json:"upload_limit_week" binding:"omitempty,min=0,max=200000"`  // 每周上传限制
	UploadLimitMonth  *int   `json:"upload_limit_month" binding:"omitempty,min=0,max=500000"` // 每月上传限制

	// 文件命名和路径配置
	PathNamingRule *string `json:"path_naming_rule" binding:"omitempty,max=100"` // 路径命名规则
	FileNamingRule *string `json:"file_naming_rule" binding:"omitempty,max=100"` // 文件命名规则

	// 图片处理配置
	ImageQuality      *int    `json:"image_quality" binding:"omitempty,min=1,max=100"`                   // 图片保存质量
	ImageFormat       *string `json:"image_format" binding:"omitempty,oneof=original jpg jpeg png webp"` // 图片转换格式
	AllowedImageTypes *string `json:"allowed_image_types" binding:"omitempty,max=200"`                   // 允许的图片类型
}

// GroupUpdateRequest 表示更新用户组的请求
type GroupUpdateRequest struct {
	DisplayName string `json:"display_name" binding:"required,min=1,max=100"`
	Description string `json:"description" binding:"max=500"`
	IsActive    *bool  `json:"is_active"`
	IsDefault   *bool  `json:"is_default"`

	// 上传限制配置
	MaxFileSize       *int64 `json:"max_file_size" binding:"omitempty,min=1,max=1048576"`     // 最大文件大小(KB) 1KB-1GB
	ConcurrentUploads *int   `json:"concurrent_uploads" binding:"omitempty,min=1,max=10"`     // 并发上传限制 1-10
	UploadLimitMinute *int   `json:"upload_limit_minute" binding:"omitempty,min=0,max=1000"`  // 每分钟上传限制
	UploadLimitHour   *int   `json:"upload_limit_hour" binding:"omitempty,min=0,max=10000"`   // 每小时上传限制
	UploadLimitDay    *int   `json:"upload_limit_day" binding:"omitempty,min=0,max=50000"`    // 每天上传限制
	UploadLimitWeek   *int   `json:"upload_limit_week" binding:"omitempty,min=0,max=200000"`  // 每周上传限制
	UploadLimitMonth  *int   `json:"upload_limit_month" binding:"omitempty,min=0,max=500000"` // 每月上传限制

	// 文件命名和路径配置
	PathNamingRule *string `json:"path_naming_rule" binding:"omitempty,max=100"` // 路径命名规则
	FileNamingRule *string `json:"file_naming_rule" binding:"omitempty,max=100"` // 文件命名规则

	// 图片处理配置
	ImageQuality      *int    `json:"image_quality" binding:"omitempty,min=1,max=100"`                   // 图片保存质量
	ImageFormat       *string `json:"image_format" binding:"omitempty,oneof=original jpg jpeg png webp"` // 图片转换格式
	AllowedImageTypes *string `json:"allowed_image_types" binding:"omitempty,max=200"`                   // 允许的图片类型
}
